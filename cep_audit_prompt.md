# CEP Extension Comprehensive Code Audit Prompt

## Executive Summary
Perform a systematic audit of the Sahai-CEP-Extension_V2 codebase to identify dead code, unused imports, redundant CSS entries, overriding styles, and architectural inconsistencies that violate Adobe CEP standards.

## Pre-Audit Setup Requirements

### 1. Static Analysis Tools Configuration
- Configure TypeScript compiler with `--noUnusedLocals` and `--noUnusedParameters` flags
- Set up ESLint with `@typescript-eslint/no-unused-vars` rule enabled
- Enable CSS analysis tools for unused selector detection
- Configure bundle analyzer to identify unused dependencies

### 2. CEP Environment Validation
- Verify CSXS/manifest.xml CEP version compatibility (CEP 9.x/10.x/11.x)
- Validate ExtendScript engine compatibility with target Adobe applications
- Check CSInterface.js version alignment with manifest requirements

## Core Audit Categories

### A. Dead Code & Unused Module Detection

#### A1. Dependency Graph Analysis
**Target Directories:**
```
client/src/hooks/, client/src/utils/, client/src/services/
client/src/components/, client/src/features/, client/src/stores/
```
**Analysis Points:**
- Trace dependency graph starting from `main.tsx` → `App.tsx` entry points
- Mark all `.ts` and `.tsx` files not reachable through import chains as unused
- Identify utility functions from `utils/common/`, `utils/cep/`, `utils/api/` that are imported but never called
- Detect modules that are referenced only in comments or disabled code blocks

#### A2. Import/Export Utilization Audit
**Target Files:** All TypeScript/JavaScript files in `/client/src/`
**Analysis Points:**
- Identify imported modules never referenced in file scope
- Detect type-only imports used as runtime values
- Find circular import dependencies between stores/hooks/components
- Locate imports of entire libraries when only specific functions are used
- Flag exported functions/components never imported elsewhere
- Detect default exports that should be named exports based on usage patterns
- Find re-exports in index files that serve no aggregation purpose

### B. Redundant Logic & Duplicate Pattern Detection

#### B1. Store Architecture Redundancy Analysis
**Target Stores:**
```
chatStore.ts, settingsStore.ts, modalStore.ts, themeStore.ts
```
**Analysis Points:**
- Identify duplicate state selectors across multiple stores
- Detect redundant actions with similar functionality
- Find overlapping Zustand slice responsibilities (e.g., shared logic between chatStore and settingsStore)
- Check for state properties that could be consolidated into a single store
- Verify store actions that perform identical operations with different names

#### B2. Service Layer Duplication Audit
**Target Services:**
```
services/providers/: openai.ts, anthropic.ts, google.ts, ollama.ts
```
**Analysis Points:**
- Identify repeated API request logic (auth headers, error formats, request interceptors)
- Detect duplicate error handling patterns across providers
- Find similar response parsing logic that could be centralized
- Check for redundant configuration validation across provider services
- Identify common HTTP client setup that could be abstracted

#### B3. Component Architecture Analysis
**Target Components:**
```
components/ui/: Modal.tsx, Button.tsx, Input.tsx, LoadingSpinner.tsx
components/modals/: SettingsModal.tsx, ChatHistoryModal.tsx, ProviderHealthModal.tsx
components/shared/: ErrorBoundary.tsx, ThemeProvider.tsx
```
**Analysis Points:**
- Verify each component is actively imported and rendered
- Check for components with identical functionality (potential duplicates)
- Identify reusable components recreated locally in features/ or modals/
- Detect components styled inconsistently due to direct inline styles or inconsistent className usage
- Find props that are declared but never passed from parent components
- Detect state variables that are set but never read

### D. State Management & Event Cycle Audit

#### D1. Zustand Store Analysis
**Target Stores:**
```
chatStore.ts, settingsStore.ts, modalStore.ts, themeStore.ts
```
**Analysis Points:**
- Identify store state properties that are never accessed via selectors
- Detect store actions that are defined but never dispatched
- Find computed values/selectors that are never subscribed to
- Check for duplicate state across multiple stores
- Verify store persistence configuration matches actual usage

#### D2. Modal & Event Cycle Redundancy
**Target Files:**
```
useModal.ts, modalStore.ts, components/modals/*
```
**Analysis Points:**
- Ensure no duplicate open/close logic or event listeners across modal components
- Confirm modal visibility is not conditionally handled both via store and local state redundantly
- Detect modal components that maintain internal state when store state should be the single source of truth
- Verify modal event handlers aren't duplicated across multiple modal instances

#### D3. Hook Utilization Review
**Target Hooks:**
```
useTheme.ts, useChat.ts, useSettings.ts, useModal.ts
```
**Analysis Points:**
- Confirm each custom hook is imported and called in components
- Identify hook return values that are destructured but never used
- Detect hooks that wrap single store calls without adding value
- Check for hook dependencies that could be simplified

### E. Service Layer Architecture Audit

#### E1. API Service Integration
**Target Services:**
```
services/cep/: ClineServiceAdapter.ts, CEPNetworkAdapter.ts, SecureStorage.ts
services/providers/: openai.ts, anthropic.ts, google.ts, ollama.ts
services/api/: client.ts, types.ts
```
**Analysis Points:**
- Verify each provider service is configured in settings and callable
- Check for API methods that are defined but never invoked
- Identify redundant error handling across multiple service layers
- Detect configuration options that aren't exposed to the UI

#### E2. CEP Integration Validation
**Target Files:**
```
utils/cep/: csInterface.ts, eventHandlers.ts, storage.ts
```
**Analysis Points:**
- Confirm CEP utility functions are called from React components or ExtendScript
- Verify ExtendScript event handlers correspond to registered CEP events
- Check for storage utilities that aren't used in store persistence
- Identify CEP-specific polyfills that may be unnecessary for target CEP version

### F. ExtendScript Layer Consistency Audit

#### F1. ExtendScript Module Analysis
**Target Files:**
```
extendscript/main.jsx, extendscript/handlers/*, extendscript/utils/*
```
**Analysis Points:**
- Verify each ExtendScript handler function is called from CEP layer
- Check for Adobe application APIs that are imported but unused
- Identify utility functions that duplicate native ExtendScript capabilities
- Confirm document/layer utilities are actually used in chat handlers
- Flag handlers/utilities defined but never called from `main.jsx` or via `csInterface.ts`

#### F2. CEP-ExtendScript Bridge Validation
**Analysis Points:**
- Verify each `evalScript` call in React corresponds to existing ExtendScript function
- Check for ExtendScript functions that aren't exposed to CEP layer
- Identify event callbacks that are registered but never triggered
- Confirm ExtendScript error handling integrates with React error boundaries

### C. CSS Architecture & Style Override Audit

#### C1. Unused CSS Detection
**Target Stylesheets:**
```
styles/: globals.css, themes.css, components.css
features/*/: TopBar.css, ChatMessages.css, InputArea.css
App.css
```
**Analysis Points:**
- Identify CSS classes/IDs that aren't referenced in any TSX files
- Detect CSS custom properties (variables) that are defined but never used
- Find unused media queries that target unsupported CEP panel dimensions
- Check for font-face declarations that aren't applied to any elements

#### C2. CSS Override & Conflict Detection
**Analysis Points:**
- Identify overlapping or conflicting style definitions (same selector with redefined properties in multiple files)
- Detect CSS specificity conflicts between global and component styles
- Find inline styles in TSX that override CSS class definitions
- Check for theme variables that are overridden rather than properly configured
- Flag global selectors in component styles that may override scoped styles unintentionally
- Identify CSS imports that are loaded but don't affect rendered components

#### C3. Feature Module CSS Validation
**Target Features:**
```
features/TopBar/, features/ChatMessages/, features/InputArea/
```
**Analysis Points:**
- Confirm each feature module's CSS file is imported and applied
- Verify feature components are properly integrated into App.tsx
- Check for feature-specific utilities that aren't used within the feature
- Identify cross-feature dependencies that violate module boundaries

### H. DevTools & Code Quality Audit

#### H1. TypeScript Configuration Analysis
**Target Files:**
```
tsconfig.json, types/: messages.ts, settings.ts, providers.ts, cline.d.ts, css-modules.d.ts
```
**Analysis Points:**
- Check for files/folders excluded from lint/TS checks unnecessarily
- Identify files with unused type declarations
- Find missing strict mode or redundant type assertions (as any, etc.)
- Identify interface definitions that aren't implemented by any classes
- Detect type aliases that are never used as type annotations
- Find ambient type declarations that don't match actual module exports
- Check for duplicate type definitions across multiple files
- Verify each custom type is imported and used in component props or function parameters
- Identify `any` types that could be narrowed to specific interfaces
- Check for assertion (`as`) usage that bypasses proper type checking
- Confirm generic type parameters are utilized in function/component definitions

#### H2. Linting & Code Standards Validation
**Analysis Points:**
- Verify ESLint configuration includes unused variable/import detection
- Check for consistent code formatting patterns across the codebase
- Identify files that bypass linting rules without justification
- Detect inconsistent naming conventions across modules
- Find TODO/FIXME comments that reference non-existent issues

### G. CEP Compliance & Manifest Validation

#### G1. Manifest Configuration Audit
**Target Files:**
```
CSXS/manifest.xml, scripts/copy-cep-files.js, scripts/package.js
```
**Analysis Points:**
- Review manifest.xml for version lock issues, unused events, or undefined script inclusions
- Cross-check all extendscript files mentioned in scripts/copy-cep-files.js and manifest.xml to ensure only required ones are included
- Verify that no files are referenced in manifest or build scripts but absent in runtime dependency flow
- Confirm all files listed in copy scripts exist and are necessary
- Verify manifest.xml declarations match actual HTML/ExtendScript files
- Check for packaging steps that bundle unused assets
- Identify CSP hash generation that covers non-existent inline scripts

#### G2. Build System & Tree Shaking Validation
**Target Files:**
```
vite.config.ts, tsconfig.json, package.json, scripts/*
```
**Analysis Points:**
- Check that vite.config.ts is configured with tree shaking and does not include manual imports of full libraries where modular imports are available
- Ensure that only used files are included in the final bundle (dist/)
- Identify build script steps that don't affect final dist/ output
- Check for Vite plugins that aren't utilized in the build process
- Verify TypeScript compiler options align with actual code patterns
- Detect npm dependencies that aren't imported anywhere in the codebase

## Audit Execution Methodology

### Phase 1: Automated Detection (30 minutes)
1. Run TypeScript compiler with unused code detection flags
2. Execute ESLint with unused variable/import rules
3. Analyze bundle composition with Vite bundle analyzer
4. Run CSS unused selector detection tools

### Phase 2: Manual Code Review (60 minutes)
1. Trace component render paths from App.tsx entry point
2. Follow store subscription patterns from hooks to components
3. Verify service method call chains from user interactions
4. Validate ExtendScript integration points with CEP events

### Phase 3: Integration Testing (30 minutes)
1. Test each modal/feature in isolation to confirm functionality
2. Verify theme switching affects all styled components
3. Confirm API provider switching utilizes respective service modules
4. Test CEP panel lifecycle events trigger appropriate handlers

## Expected Audit Deliverables

### 1. Unused Files Report
**Format: Structured Markdown**
- List of `.ts/.tsx` files that can be safely removed
- Unused import statements with file locations and suggested removals
- Unreferenced utility functions from `utils/` directories
- ExtendScript files not called from CEP layer

### 2. Redundant Functions & Duplicate Components Report
**Format: Structured Markdown**
- Duplicate state selectors and actions across Zustand stores
- Repeated API request logic in provider services that could be centralized
- UI components with identical functionality
- Modal components with duplicate open/close logic

### 3. Dead CSS Classes Report
**Format: Structured Markdown**
- Unreferenced CSS classes/IDs with file locations
- Overlapping or conflicting style definitions across files
- CSS custom properties defined but never used
- Global selectors that override component styles unintentionally

### 4. Architecture Inconsistency Report
**Format: Structured Markdown**
- Circular dependencies that need resolution
- Service layer abstractions that could be simplified
- State management patterns that violate single responsibility
- Component styling inconsistencies (inline styles vs className usage)

### 5. Manifest & Script Issues Report
**Format: Structured Markdown**
- Files referenced in manifest.xml or build scripts but absent in runtime
- ExtendScript files copied but never called
- Build script steps that don't affect final output
- CSP hash generation covering non-existent scripts

### 6. CEP Compliance Flags Report
**Format: Structured Markdown**
- ExtendScript integration patterns not following Adobe guidelines
- Manifest.xml declarations that could be streamlined
- CSInterface usage patterns that could be modernized
- Version compatibility issues with target CEP versions

### 7. Optimization Suggestions Report
**Format: Structured Markdown**
- Bundle size reduction potential from removing unused code
- CSS specificity optimizations to reduce override complexity
- Import tree-shaking opportunities in service modules
- TypeScript configuration improvements for better type checking

## Success Criteria
- Zero unused imports/exports in final codebase
- All CSS classes are referenced by actual DOM elements
- Every Zustand store property is actively used
- All service methods are reachable through user interactions
- ExtendScript functions align 1:1 with CEP event handlers
- Build output contains only necessary files for CEP deployment