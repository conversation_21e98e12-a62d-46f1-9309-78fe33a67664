{"version": 3, "file": "stylis.mjs", "sources": ["../src/Enum.js", "../src/Utility.js", "../src/Tokenizer.js", "../src/Parser.js", "../src/Prefixer.js", "../src/Serializer.js", "../src/Middleware.js"], "sourcesContent": ["export var MS = '-ms-'\nexport var MOZ = '-moz-'\nexport var WEBKIT = '-webkit-'\n\nexport var COMMENT = 'comm'\nexport var RULESET = 'rule'\nexport var DECLARATION = 'decl'\n\nexport var PAGE = '@page'\nexport var MEDIA = '@media'\nexport var IMPORT = '@import'\nexport var CHARSET = '@charset'\nexport var VIEWPORT = '@viewport'\nexport var SUPPORTS = '@supports'\nexport var DOCUMENT = '@document'\nexport var NAMESPACE = '@namespace'\nexport var KEYFRAMES = '@keyframes'\nexport var FONT_FACE = '@font-face'\nexport var COUNTER_STYLE = '@counter-style'\nexport var FONT_FEATURE_VALUES = '@font-feature-values'\nexport var LAYER = '@layer'\n", "/**\n * @param {number}\n * @return {number}\n */\nexport var abs = Math.abs\n\n/**\n * @param {number}\n * @return {string}\n */\nexport var from = String.fromCharCode\n\n/**\n * @param {object}\n * @return {object}\n */\nexport var assign = Object.assign\n\n/**\n * @param {string} value\n * @param {number} length\n * @return {number}\n */\nexport function hash (value, length) {\n\treturn charat(value, 0) ^ 45 ? (((((((length << 2) ^ charat(value, 0)) << 2) ^ charat(value, 1)) << 2) ^ charat(value, 2)) << 2) ^ charat(value, 3) : 0\n}\n\n/**\n * @param {string} value\n * @return {string}\n */\nexport function trim (value) {\n\treturn value.trim()\n}\n\n/**\n * @param {string} value\n * @param {RegExp} pattern\n * @return {string?}\n */\nexport function match (value, pattern) {\n\treturn (value = pattern.exec(value)) ? value[0] : value\n}\n\n/**\n * @param {string} value\n * @param {(string|RegExp)} pattern\n * @param {string} replacement\n * @return {string}\n */\nexport function replace (value, pattern, replacement) {\n\treturn value.replace(pattern, replacement)\n}\n\n/**\n * @param {string} value\n * @param {string} search\n * @return {number}\n */\nexport function indexof (value, search) {\n\treturn value.indexOf(search)\n}\n\n/**\n * @param {string} value\n * @param {number} index\n * @return {number}\n */\nexport function charat (value, index) {\n\treturn value.charCodeAt(index) | 0\n}\n\n/**\n * @param {string} value\n * @param {number} begin\n * @param {number} end\n * @return {string}\n */\nexport function substr (value, begin, end) {\n\treturn value.slice(begin, end)\n}\n\n/**\n * @param {string} value\n * @return {number}\n */\nexport function strlen (value) {\n\treturn value.length\n}\n\n/**\n * @param {any[]} value\n * @return {number}\n */\nexport function sizeof (value) {\n\treturn value.length\n}\n\n/**\n * @param {any} value\n * @param {any[]} array\n * @return {any}\n */\nexport function append (value, array) {\n\treturn array.push(value), value\n}\n\n/**\n * @param {string[]} array\n * @param {function} callback\n * @return {string}\n */\nexport function combine (array, callback) {\n\treturn array.map(callback).join('')\n}\n", "import {from, trim, charat, strlen, substr, append, assign} from './Utility.js'\n\nexport var line = 1\nexport var column = 1\nexport var length = 0\nexport var position = 0\nexport var character = 0\nexport var characters = ''\n\n/**\n * @param {string} value\n * @param {object | null} root\n * @param {object | null} parent\n * @param {string} type\n * @param {string[] | string} props\n * @param {object[] | string} children\n * @param {number} length\n */\nexport function node (value, root, parent, type, props, children, length) {\n\treturn {value: value, root: root, parent: parent, type: type, props: props, children: children, line: line, column: column, length: length, return: ''}\n}\n\n/**\n * @param {object} root\n * @param {object} props\n * @return {object}\n */\nexport function copy (root, props) {\n\treturn assign(node('', null, null, '', null, null, 0), root, {length: -root.length}, props)\n}\n\n/**\n * @return {number}\n */\nexport function char () {\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function prev () {\n\tcharacter = position > 0 ? charat(characters, --position) : 0\n\n\tif (column--, character === 10)\n\t\tcolumn = 1, line--\n\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function next () {\n\tcharacter = position < length ? charat(characters, position++) : 0\n\n\tif (column++, character === 10)\n\t\tcolumn = 1, line++\n\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function peek () {\n\treturn charat(characters, position)\n}\n\n/**\n * @return {number}\n */\nexport function caret () {\n\treturn position\n}\n\n/**\n * @param {number} begin\n * @param {number} end\n * @return {string}\n */\nexport function slice (begin, end) {\n\treturn substr(characters, begin, end)\n}\n\n/**\n * @param {number} type\n * @return {number}\n */\nexport function token (type) {\n\tswitch (type) {\n\t\t// \\0 \\t \\n \\r \\s whitespace token\n\t\tcase 0: case 9: case 10: case 13: case 32:\n\t\t\treturn 5\n\t\t// ! + , / > @ ~ isolate token\n\t\tcase 33: case 43: case 44: case 47: case 62: case 64: case 126:\n\t\t// ; { } breakpoint token\n\t\tcase 59: case 123: case 125:\n\t\t\treturn 4\n\t\t// : accompanied token\n\t\tcase 58:\n\t\t\treturn 3\n\t\t// \" ' ( [ opening delimit token\n\t\tcase 34: case 39: case 40: case 91:\n\t\t\treturn 2\n\t\t// ) ] closing delimit token\n\t\tcase 41: case 93:\n\t\t\treturn 1\n\t}\n\n\treturn 0\n}\n\n/**\n * @param {string} value\n * @return {any[]}\n */\nexport function alloc (value) {\n\treturn line = column = 1, length = strlen(characters = value), position = 0, []\n}\n\n/**\n * @param {any} value\n * @return {any}\n */\nexport function dealloc (value) {\n\treturn characters = '', value\n}\n\n/**\n * @param {number} type\n * @return {string}\n */\nexport function delimit (type) {\n\treturn trim(slice(position - 1, delimiter(type === 91 ? type + 2 : type === 40 ? type + 1 : type)))\n}\n\n/**\n * @param {string} value\n * @return {string[]}\n */\nexport function tokenize (value) {\n\treturn dealloc(tokenizer(alloc(value)))\n}\n\n/**\n * @param {number} type\n * @return {string}\n */\nexport function whitespace (type) {\n\twhile (character = peek())\n\t\tif (character < 33)\n\t\t\tnext()\n\t\telse\n\t\t\tbreak\n\n\treturn token(type) > 2 || token(character) > 3 ? '' : ' '\n}\n\n/**\n * @param {string[]} children\n * @return {string[]}\n */\nexport function tokenizer (children) {\n\twhile (next())\n\t\tswitch (token(character)) {\n\t\t\tcase 0: append(identifier(position - 1), children)\n\t\t\t\tbreak\n\t\t\tcase 2: append(delimit(character), children)\n\t\t\t\tbreak\n\t\t\tdefault: append(from(character), children)\n\t\t}\n\n\treturn children\n}\n\n/**\n * @param {number} index\n * @param {number} count\n * @return {string}\n */\nexport function escaping (index, count) {\n\twhile (--count && next())\n\t\t// not 0-9 A-F a-f\n\t\tif (character < 48 || character > 102 || (character > 57 && character < 65) || (character > 70 && character < 97))\n\t\t\tbreak\n\n\treturn slice(index, caret() + (count < 6 && peek() == 32 && next() == 32))\n}\n\n/**\n * @param {number} type\n * @return {number}\n */\nexport function delimiter (type) {\n\twhile (next())\n\t\tswitch (character) {\n\t\t\t// ] ) \" '\n\t\t\tcase type:\n\t\t\t\treturn position\n\t\t\t// \" '\n\t\t\tcase 34: case 39:\n\t\t\t\tif (type !== 34 && type !== 39)\n\t\t\t\t\tdelimiter(character)\n\t\t\t\tbreak\n\t\t\t// (\n\t\t\tcase 40:\n\t\t\t\tif (type === 41)\n\t\t\t\t\tdelimiter(type)\n\t\t\t\tbreak\n\t\t\t// \\\n\t\t\tcase 92:\n\t\t\t\tnext()\n\t\t\t\tbreak\n\t\t}\n\n\treturn position\n}\n\n/**\n * @param {number} type\n * @param {number} index\n * @return {number}\n */\nexport function commenter (type, index) {\n\twhile (next())\n\t\t// //\n\t\tif (type + character === 47 + 10)\n\t\t\tbreak\n\t\t// /*\n\t\telse if (type + character === 42 + 42 && peek() === 47)\n\t\t\tbreak\n\n\treturn '/*' + slice(index, position - 1) + '*' + from(type === 47 ? type : next())\n}\n\n/**\n * @param {number} index\n * @return {string}\n */\nexport function identifier (index) {\n\twhile (!token(peek()))\n\t\tnext()\n\n\treturn slice(index, position)\n}\n", "import {COMMENT, RULESET, DECLARATION} from './Enum.js'\nimport {abs, charat, trim, from, sizeof, strlen, substr, append, replace, indexof} from './Utility.js'\nimport {node, char, prev, next, peek, caret, alloc, dealloc, delimit, whitespace, escaping, identifier, commenter} from './Tokenizer.js'\n\n/**\n * @param {string} value\n * @return {object[]}\n */\nexport function compile (value) {\n\treturn dealloc(parse('', null, null, null, [''], value = alloc(value), 0, [0], value))\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {string[]} rule\n * @param {string[]} rules\n * @param {string[]} rulesets\n * @param {number[]} pseudo\n * @param {number[]} points\n * @param {string[]} declarations\n * @return {object}\n */\nexport function parse (value, root, parent, rule, rules, rulesets, pseudo, points, declarations) {\n\tvar index = 0\n\tvar offset = 0\n\tvar length = pseudo\n\tvar atrule = 0\n\tvar property = 0\n\tvar previous = 0\n\tvar variable = 1\n\tvar scanning = 1\n\tvar ampersand = 1\n\tvar character = 0\n\tvar type = ''\n\tvar props = rules\n\tvar children = rulesets\n\tvar reference = rule\n\tvar characters = type\n\n\twhile (scanning)\n\t\tswitch (previous = character, character = next()) {\n\t\t\t// (\n\t\t\tcase 40:\n\t\t\t\tif (previous != 108 && charat(characters, length - 1) == 58) {\n\t\t\t\t\tif (indexof(characters += replace(delimit(character), '&', '&\\f'), '&\\f') != -1)\n\t\t\t\t\t\tampersand = -1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t// \" ' [\n\t\t\tcase 34: case 39: case 91:\n\t\t\t\tcharacters += delimit(character)\n\t\t\t\tbreak\n\t\t\t// \\t \\n \\r \\s\n\t\t\tcase 9: case 10: case 13: case 32:\n\t\t\t\tcharacters += whitespace(previous)\n\t\t\t\tbreak\n\t\t\t// \\\n\t\t\tcase 92:\n\t\t\t\tcharacters += escaping(caret() - 1, 7)\n\t\t\t\tcontinue\n\t\t\t// /\n\t\t\tcase 47:\n\t\t\t\tswitch (peek()) {\n\t\t\t\t\tcase 42: case 47:\n\t\t\t\t\t\tappend(comment(commenter(next(), caret()), root, parent), declarations)\n\t\t\t\t\t\tbreak\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tcharacters += '/'\n\t\t\t\t}\n\t\t\t\tbreak\n\t\t\t// {\n\t\t\tcase 123 * variable:\n\t\t\t\tpoints[index++] = strlen(characters) * ampersand\n\t\t\t// } ; \\0\n\t\t\tcase 125 * variable: case 59: case 0:\n\t\t\t\tswitch (character) {\n\t\t\t\t\t// \\0 }\n\t\t\t\t\tcase 0: case 125: scanning = 0\n\t\t\t\t\t// ;\n\t\t\t\t\tcase 59 + offset: if (ampersand == -1) characters = replace(characters, /\\f/g, '')\n\t\t\t\t\t\tif (property > 0 && (strlen(characters) - length))\n\t\t\t\t\t\t\tappend(property > 32 ? declaration(characters + ';', rule, parent, length - 1) : declaration(replace(characters, ' ', '') + ';', rule, parent, length - 2), declarations)\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// @ ;\n\t\t\t\t\tcase 59: characters += ';'\n\t\t\t\t\t// { rule/at-rule\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tappend(reference = ruleset(characters, root, parent, index, offset, rules, points, type, props = [], children = [], length), rulesets)\n\n\t\t\t\t\t\tif (character === 123)\n\t\t\t\t\t\t\tif (offset === 0)\n\t\t\t\t\t\t\t\tparse(characters, root, reference, reference, props, rulesets, length, points, children)\n\t\t\t\t\t\t\telse\n\t\t\t\t\t\t\t\tswitch (atrule === 99 && charat(characters, 3) === 110 ? 100 : atrule) {\n\t\t\t\t\t\t\t\t\t// d l m s\n\t\t\t\t\t\t\t\t\tcase 100: case 108: case 109: case 115:\n\t\t\t\t\t\t\t\t\t\tparse(value, reference, reference, rule && append(ruleset(value, reference, reference, 0, 0, rules, points, type, rules, props = [], length), children), rules, children, length, points, rule ? props : children)\n\t\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t\t\tparse(characters, reference, reference, reference, [''], children, 0, points, children)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tindex = offset = property = 0, variable = ampersand = 1, type = characters = '', length = pseudo\n\t\t\t\tbreak\n\t\t\t// :\n\t\t\tcase 58:\n\t\t\t\tlength = 1 + strlen(characters), property = previous\n\t\t\tdefault:\n\t\t\t\tif (variable < 1)\n\t\t\t\t\tif (character == 123)\n\t\t\t\t\t\t--variable\n\t\t\t\t\telse if (character == 125 && variable++ == 0 && prev() == 125)\n\t\t\t\t\t\tcontinue\n\n\t\t\t\tswitch (characters += from(character), character * variable) {\n\t\t\t\t\t// &\n\t\t\t\t\tcase 38:\n\t\t\t\t\t\tampersand = offset > 0 ? 1 : (characters += '\\f', -1)\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// ,\n\t\t\t\t\tcase 44:\n\t\t\t\t\t\tpoints[index++] = (strlen(characters) - 1) * ampersand, ampersand = 1\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// @\n\t\t\t\t\tcase 64:\n\t\t\t\t\t\t// -\n\t\t\t\t\t\tif (peek() === 45)\n\t\t\t\t\t\t\tcharacters += delimit(next())\n\n\t\t\t\t\t\tatrule = peek(), offset = length = strlen(type = characters += identifier(caret())), character++\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// -\n\t\t\t\t\tcase 45:\n\t\t\t\t\t\tif (previous === 45 && strlen(characters) == 2)\n\t\t\t\t\t\t\tvariable = 0\n\t\t\t\t}\n\t\t}\n\n\treturn rulesets\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {number} index\n * @param {number} offset\n * @param {string[]} rules\n * @param {number[]} points\n * @param {string} type\n * @param {string[]} props\n * @param {string[]} children\n * @param {number} length\n * @return {object}\n */\nexport function ruleset (value, root, parent, index, offset, rules, points, type, props, children, length) {\n\tvar post = offset - 1\n\tvar rule = offset === 0 ? rules : ['']\n\tvar size = sizeof(rule)\n\n\tfor (var i = 0, j = 0, k = 0; i < index; ++i)\n\t\tfor (var x = 0, y = substr(value, post + 1, post = abs(j = points[i])), z = value; x < size; ++x)\n\t\t\tif (z = trim(j > 0 ? rule[x] + ' ' + y : replace(y, /&\\f/g, rule[x])))\n\t\t\t\tprops[k++] = z\n\n\treturn node(value, root, parent, offset === 0 ? RULESET : type, props, children, length)\n}\n\n/**\n * @param {number} value\n * @param {object} root\n * @param {object?} parent\n * @return {object}\n */\nexport function comment (value, root, parent) {\n\treturn node(value, root, parent, COMMENT, from(char()), substr(value, 2, -2), 0)\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {number} length\n * @return {object}\n */\nexport function declaration (value, root, parent, length) {\n\treturn node(value, root, parent, DECLARATION, substr(value, 0, length), substr(value, length + 1, -1), length)\n}\n", "import {MS, MO<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>} from './Enum.js'\nimport {hash, charat, strlen, indexof, replace, substr, match} from './Utility.js'\n\n/**\n * @param {string} value\n * @param {number} length\n * @param {object[]} children\n * @return {string}\n */\nexport function prefix (value, length, children) {\n\tswitch (hash(value, length)) {\n\t\t// color-adjust\n\t\tcase 5103:\n\t\t\treturn WEBKIT + 'print-' + value + value\n\t\t// animation, animation-(delay|direction|duration|fill-mode|iteration-count|name|play-state|timing-function)\n\t\tcase 5737: case 4201: case 3177: case 3433: case 1641: case 4457: case 2921:\n\t\t// text-decoration, filter, clip-path, backface-visibility, column, box-decoration-break\n\t\tcase 5572: case 6356: case 5844: case 3191: case 6645: case 3005:\n\t\t// mask, mask-image, mask-(mode|clip|size), mask-(repeat|origin), mask-position, mask-composite,\n\t\tcase 6391: case 5879: case 5623: case 6135: case 4599: case 4855:\n\t\t// background-clip, columns, column-(count|fill|gap|rule|rule-color|rule-style|rule-width|span|width)\n\t\tcase 4215: case 6389: case 5109: case 5365: case 5621: case 3829:\n\t\t\treturn WEBKIT + value + value\n\t\t// tab-size\n\t\tcase 4789:\n\t\t\treturn MOZ + value + value\n\t\t// appearance, user-select, transform, hyphens, text-size-adjust\n\t\tcase 5349: case 4246: case 4810: case 6968: case 2756:\n\t\t\treturn WEBKIT + value + MOZ + value + MS + value + value\n\t\t// writing-mode\n\t\tcase 5936:\n\t\t\tswitch (charat(value, length + 11)) {\n\t\t\t\t// vertical-l(r)\n\t\t\t\tcase 114:\n\t\t\t\t\treturn WEBKIT + value + MS + replace(value, /[svh]\\w+-[tblr]{2}/, 'tb') + value\n\t\t\t\t// vertical-r(l)\n\t\t\t\tcase 108:\n\t\t\t\t\treturn WEBKIT + value + MS + replace(value, /[svh]\\w+-[tblr]{2}/, 'tb-rl') + value\n\t\t\t\t// horizontal(-)tb\n\t\t\t\tcase 45:\n\t\t\t\t\treturn WEBKIT + value + MS + replace(value, /[svh]\\w+-[tblr]{2}/, 'lr') + value\n\t\t\t\t// default: fallthrough to below\n\t\t\t}\n\t\t// flex, flex-direction, scroll-snap-type, writing-mode\n\t\tcase 6828: case 4268: case 2903:\n\t\t\treturn WEBKIT + value + MS + value + value\n\t\t// order\n\t\tcase 6165:\n\t\t\treturn WEBKIT + value + MS + 'flex-' + value + value\n\t\t// align-items\n\t\tcase 5187:\n\t\t\treturn WEBKIT + value + replace(value, /(\\w+).+(:[^]+)/, WEBKIT + 'box-$1$2' + MS + 'flex-$1$2') + value\n\t\t// align-self\n\t\tcase 5443:\n\t\t\treturn WEBKIT + value + MS + 'flex-item-' + replace(value, /flex-|-self/g, '') + (!match(value, /flex-|baseline/) ? MS + 'grid-row-' + replace(value, /flex-|-self/g, '') : '') + value\n\t\t// align-content\n\t\tcase 4675:\n\t\t\treturn WEBKIT + value + MS + 'flex-line-pack' + replace(value, /align-content|flex-|-self/g, '') + value\n\t\t// flex-shrink\n\t\tcase 5548:\n\t\t\treturn WEBKIT + value + MS + replace(value, 'shrink', 'negative') + value\n\t\t// flex-basis\n\t\tcase 5292:\n\t\t\treturn WEBKIT + value + MS + replace(value, 'basis', 'preferred-size') + value\n\t\t// flex-grow\n\t\tcase 6060:\n\t\t\treturn WEBKIT + 'box-' + replace(value, '-grow', '') + WEBKIT + value + MS + replace(value, 'grow', 'positive') + value\n\t\t// transition\n\t\tcase 4554:\n\t\t\treturn WEBKIT + replace(value, /([^-])(transform)/g, '$1' + WEBKIT + '$2') + value\n\t\t// cursor\n\t\tcase 6187:\n\t\t\treturn replace(replace(replace(value, /(zoom-|grab)/, WEBKIT + '$1'), /(image-set)/, WEBKIT + '$1'), value, '') + value\n\t\t// background, background-image\n\t\tcase 5495: case 3959:\n\t\t\treturn replace(value, /(image-set\\([^]*)/, WEBKIT + '$1' + '$`$1')\n\t\t// justify-content\n\t\tcase 4968:\n\t\t\treturn replace(replace(value, /(.+:)(flex-)?(.*)/, WEBKIT + 'box-pack:$3' + MS + 'flex-pack:$3'), /s.+-b[^;]+/, 'justify') + WEBKIT + value + value\n\t\t// justify-self\n\t\tcase 4200:\n\t\t\tif (!match(value, /flex-|baseline/)) return MS + 'grid-column-align' + substr(value, length) + value\n\t\t\tbreak\n\t\t// grid-template-(columns|rows)\n\t\tcase 2592: case 3360:\n\t\t\treturn MS + replace(value, 'template-', '') + value\n\t\t// grid-(row|column)-start\n\t\tcase 4384: case 3616:\n\t\t\tif (children && children.some(function (element, index) { return length = index, match(element.props, /grid-\\w+-end/) })) {\n\t\t\t\treturn ~indexof(value + (children = children[length].value), 'span') ? value : (MS + replace(value, '-start', '') + value + MS + 'grid-row-span:' + (~indexof(children, 'span') ? match(children, /\\d+/) : +match(children, /\\d+/) - +match(value, /\\d+/)) + ';')\n\t\t\t}\n\t\t\treturn MS + replace(value, '-start', '') + value\n\t\t// grid-(row|column)-end\n\t\tcase 4896: case 4128:\n\t\t\treturn (children && children.some(function (element) { return match(element.props, /grid-\\w+-start/) })) ? value : MS + replace(replace(value, '-end', '-span'), 'span ', '') + value\n\t\t// (margin|padding)-inline-(start|end)\n\t\tcase 4095: case 3583: case 4068: case 2532:\n\t\t\treturn replace(value, /(.+)-inline(.+)/, WEBKIT + '$1$2') + value\n\t\t// (min|max)?(width|height|inline-size|block-size)\n\t\tcase 8116: case 7059: case 5753: case 5535:\n\t\tcase 5445: case 5701: case 4933: case 4677:\n\t\tcase 5533: case 5789: case 5021: case 4765:\n\t\t\t// stretch, max-content, min-content, fill-available\n\t\t\tif (strlen(value) - 1 - length > 6)\n\t\t\t\tswitch (charat(value, length + 1)) {\n\t\t\t\t\t// (m)ax-content, (m)in-content\n\t\t\t\t\tcase 109:\n\t\t\t\t\t\t// -\n\t\t\t\t\t\tif (charat(value, length + 4) !== 45)\n\t\t\t\t\t\t\tbreak\n\t\t\t\t\t// (f)ill-available, (f)it-content\n\t\t\t\t\tcase 102:\n\t\t\t\t\t\treturn replace(value, /(.+:)(.+)-([^]+)/, '$1' + WEBKIT + '$2-$3' + '$1' + MOZ + (charat(value, length + 3) == 108 ? '$3' : '$2-$3')) + value\n\t\t\t\t\t// (s)tretch\n\t\t\t\t\tcase 115:\n\t\t\t\t\t\treturn ~indexof(value, 'stretch') ? prefix(replace(value, 'stretch', 'fill-available'), length, children) + value : value\n\t\t\t\t}\n\t\t\tbreak\n\t\t// grid-(column|row)\n\t\tcase 5152: case 5920:\n\t\t\treturn replace(value, /(.+?):(\\d+)(\\s*\\/\\s*(span)?\\s*(\\d+))?(.*)/, function (_, a, b, c, d, e, f) { return (MS + a + ':' + b + f) + (c ? (MS + a + '-span:' + (d ? e : +e - +b)) + f : '') + value })\n\t\t// position: sticky\n\t\tcase 4949:\n\t\t\t// stick(y)?\n\t\t\tif (charat(value, length + 6) === 121)\n\t\t\t\treturn replace(value, ':', ':' + WEBKIT) + value\n\t\t\tbreak\n\t\t// display: (flex|inline-flex|grid|inline-grid)\n\t\tcase 6444:\n\t\t\tswitch (charat(value, charat(value, 14) === 45 ? 18 : 11)) {\n\t\t\t\t// (inline-)?fle(x)\n\t\t\t\tcase 120:\n\t\t\t\t\treturn replace(value, /(.+:)([^;\\s!]+)(;|(\\s+)?!.+)?/, '$1' + WEBKIT + (charat(value, 14) === 45 ? 'inline-' : '') + 'box$3' + '$1' + WEBKIT + '$2$3' + '$1' + MS + '$2box$3') + value\n\t\t\t\t// (inline-)?gri(d)\n\t\t\t\tcase 100:\n\t\t\t\t\treturn replace(value, ':', ':' + MS) + value\n\t\t\t}\n\t\t\tbreak\n\t\t// scroll-margin, scroll-margin-(top|right|bottom|left)\n\t\tcase 5719: case 2647: case 2135: case 3927: case 2391:\n\t\t\treturn replace(value, 'scroll-', 'scroll-snap-') + value\n\t}\n\n\treturn value\n}\n", "import {IMPOR<PERSON>, LAYER, COMMENT, RULESE<PERSON>, DECLARATION, KEYFRAMES} from './Enum.js'\nimport {strlen, sizeof} from './Utility.js'\n\n/**\n * @param {object[]} children\n * @param {function} callback\n * @return {string}\n */\nexport function serialize (children, callback) {\n\tvar output = ''\n\tvar length = sizeof(children)\n\n\tfor (var i = 0; i < length; i++)\n\t\toutput += callback(children[i], i, children, callback) || ''\n\n\treturn output\n}\n\n/**\n * @param {object} element\n * @param {number} index\n * @param {object[]} children\n * @param {function} callback\n * @return {string}\n */\nexport function stringify (element, index, children, callback) {\n\tswitch (element.type) {\n\t\tcase LAYER: if (element.children.length) break\n\t\tcase IMPORT: case DECLARATION: return element.return = element.return || element.value\n\t\tcase COMMENT: return ''\n\t\tcase KEYFRAMES: return element.return = element.value + '{' + serialize(element.children, callback) + '}'\n\t\tcase RULESET: element.value = element.props.join(',')\n\t}\n\n\treturn strlen(children = serialize(element.children, callback)) ? element.return = element.value + '{' + children + '}' : ''\n}\n", "import {MS, MOZ, WEBKIT, RULESET, KEYFRAMES, DECLARATION} from './Enum.js'\nimport {match, charat, substr, strlen, sizeof, replace, combine} from './Utility.js'\nimport {copy, tokenize} from './Tokenizer.js'\nimport {serialize} from './Serializer.js'\nimport {prefix} from './Prefixer.js'\n\n/**\n * @param {function[]} collection\n * @return {function}\n */\nexport function middleware (collection) {\n\tvar length = sizeof(collection)\n\n\treturn function (element, index, children, callback) {\n\t\tvar output = ''\n\n\t\tfor (var i = 0; i < length; i++)\n\t\t\toutput += collection[i](element, index, children, callback) || ''\n\n\t\treturn output\n\t}\n}\n\n/**\n * @param {function} callback\n * @return {function}\n */\nexport function rulesheet (callback) {\n\treturn function (element) {\n\t\tif (!element.root)\n\t\t\tif (element = element.return)\n\t\t\t\tcallback(element)\n\t}\n}\n\n/**\n * @param {object} element\n * @param {number} index\n * @param {object[]} children\n * @param {function} callback\n */\nexport function prefixer (element, index, children, callback) {\n\tif (element.length > -1)\n\t\tif (!element.return)\n\t\t\tswitch (element.type) {\n\t\t\t\tcase DECLARATION: element.return = prefix(element.value, element.length, children)\n\t\t\t\t\treturn\n\t\t\t\tcase KEYFRAMES:\n\t\t\t\t\treturn serialize([copy(element, {value: replace(element.value, '@', '@' + WEBKIT)})], callback)\n\t\t\t\tcase RULESET:\n\t\t\t\t\tif (element.length)\n\t\t\t\t\t\treturn combine(element.props, function (value) {\n\t\t\t\t\t\t\tswitch (match(value, /(::plac\\w+|:read-\\w+)/)) {\n\t\t\t\t\t\t\t\t// :read-(only|write)\n\t\t\t\t\t\t\t\tcase ':read-only': case ':read-write':\n\t\t\t\t\t\t\t\t\treturn serialize([copy(element, {props: [replace(value, /:(read-\\w+)/, ':' + MOZ + '$1')]})], callback)\n\t\t\t\t\t\t\t\t// :placeholder\n\t\t\t\t\t\t\t\tcase '::placeholder':\n\t\t\t\t\t\t\t\t\treturn serialize([\n\t\t\t\t\t\t\t\t\t\tcopy(element, {props: [replace(value, /:(plac\\w+)/, ':' + WEBKIT + 'input-$1')]}),\n\t\t\t\t\t\t\t\t\t\tcopy(element, {props: [replace(value, /:(plac\\w+)/, ':' + MOZ + '$1')]}),\n\t\t\t\t\t\t\t\t\t\tcopy(element, {props: [replace(value, /:(plac\\w+)/, MS + 'input-$1')]})\n\t\t\t\t\t\t\t\t\t], callback)\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\treturn ''\n\t\t\t\t\t\t})\n\t\t\t}\n}\n\n/**\n * @param {object} element\n * @param {number} index\n * @param {object[]} children\n */\nexport function namespace (element) {\n\tswitch (element.type) {\n\t\tcase RULESET:\n\t\t\telement.props = element.props.map(function (value) {\n\t\t\t\treturn combine(tokenize(value), function (value, index, children) {\n\t\t\t\t\tswitch (charat(value, 0)) {\n\t\t\t\t\t\t// \\f\n\t\t\t\t\t\tcase 12:\n\t\t\t\t\t\t\treturn substr(value, 1, strlen(value))\n\t\t\t\t\t\t// \\0 ( + > ~\n\t\t\t\t\t\tcase 0: case 40: case 43: case 62: case 126:\n\t\t\t\t\t\t\treturn value\n\t\t\t\t\t\t// :\n\t\t\t\t\t\tcase 58:\n\t\t\t\t\t\t\tif (children[++index] === 'global')\n\t\t\t\t\t\t\t\tchildren[index] = '', children[++index] = '\\f' + substr(children[index], index = 1, -1)\n\t\t\t\t\t\t// \\s\n\t\t\t\t\t\tcase 32:\n\t\t\t\t\t\t\treturn index === 1 ? '' : value\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\tswitch (index) {\n\t\t\t\t\t\t\t\tcase 0: element = value\n\t\t\t\t\t\t\t\t\treturn sizeof(children) > 1 ? '' : value\n\t\t\t\t\t\t\t\tcase index = sizeof(children) - 1: case 2:\n\t\t\t\t\t\t\t\t\treturn index === 2 ? value + element + element : value + element\n\t\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t\treturn value\n\t\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t})\n\t}\n}\n"], "names": ["MS", "MOZ", "WEBKIT", "COMMENT", "RULESET", "DECLARATION", "PAGE", "MEDIA", "IMPORT", "CHARSET", "VIEWPORT", "SUPPORTS", "DOCUMENT", "NAMESPACE", "KEYFRAMES", "FONT_FACE", "COUNTER_STYLE", "FONT_FEATURE_VALUES", "LAYER", "abs", "Math", "from", "String", "fromCharCode", "assign", "Object", "hash", "value", "length", "charat", "trim", "match", "pattern", "exec", "replace", "replacement", "indexof", "search", "indexOf", "index", "charCodeAt", "substr", "begin", "end", "slice", "strlen", "sizeof", "append", "array", "push", "combine", "callback", "map", "join", "line", "column", "position", "character", "characters", "node", "root", "parent", "type", "props", "children", "return", "copy", "char", "prev", "next", "peek", "caret", "token", "alloc", "dealloc", "delimit", "delimiter", "tokenize", "tokenizer", "whitespace", "identifier", "escaping", "count", "commenter", "compile", "parse", "rule", "rules", "rulesets", "pseudo", "points", "declarations", "offset", "at<PERSON>le", "property", "previous", "variable", "scanning", "ampersand", "reference", "comment", "declaration", "ruleset", "post", "size", "i", "j", "k", "x", "y", "z", "prefix", "some", "element", "_", "a", "b", "c", "d", "e", "f", "serialize", "output", "stringify", "middleware", "collection", "rulesheet", "prefixer", "namespace"], "mappings": "AAAU,IAACA,EAAK,OACN,IAACC,EAAM,QACP,IAACC,EAAS,WAEV,IAACC,EAAU,OACX,IAACC,EAAU,OACX,IAACC,EAAc,OAEf,IAACC,EAAO,QACR,IAACC,EAAQ,SACT,IAACC,EAAS,UACV,IAACC,EAAU,WACX,IAACC,EAAW,YACZ,IAACC,EAAW,YACZ,IAACC,EAAW,YACZ,IAACC,EAAY,aACb,IAACC,EAAY,aACb,IAACC,EAAY,aACb,IAACC,EAAgB,iBACjB,IAACC,EAAsB,uBACvB,IAACC,EAAQ,SChBT,IAACC,EAAMC,KAAKD,IAMZ,IAACE,EAAOC,OAAOC,aAMf,IAACC,EAASC,OAAOD,OAOpB,SAASE,EAAMC,EAAOC,GAC5B,OAAOC,EAAOF,EAAO,GAAK,MAAYC,GAAU,EAAKC,EAAOF,EAAO,KAAO,EAAKE,EAAOF,EAAO,KAAO,EAAKE,EAAOF,EAAO,KAAO,EAAKE,EAAOF,EAAO,GAAK,EAOhJ,SAASG,EAAMH,GACrB,OAAOA,EAAMG,OAQP,SAASC,EAAOJ,EAAOK,GAC7B,OAAQL,EAAQK,EAAQC,KAAKN,IAAUA,EAAM,GAAKA,EASnD,SAAgBO,EAASP,EAAOK,EAASG,GACxC,OAAOR,EAAMO,QAAQF,EAASG,GAQxB,SAASC,EAAST,EAAOU,GAC/B,OAAOV,EAAMW,QAAQD,GAQf,SAASR,EAAQF,EAAOY,GAC9B,OAAOZ,EAAMa,WAAWD,GAAS,EASlC,SAAgBE,EAAQd,EAAOe,EAAOC,GACrC,OAAOhB,EAAMiB,MAAMF,EAAOC,GAOpB,SAASE,EAAQlB,GACvB,OAAOA,EAAMC,OAOP,SAASkB,EAAQnB,GACvB,OAAOA,EAAMC,OAQP,SAASmB,EAAQpB,EAAOqB,GAC9B,OAAOA,EAAMC,KAAKtB,GAAQA,EAQpB,SAASuB,EAASF,EAAOG,GAC/B,OAAOH,EAAMI,IAAID,GAAUE,KAAK,IC/GvB,IAACC,EAAO,EAClB,IAAWC,EAAS,EACpB,IAAW3B,EAAS,EACpB,IAAW4B,EAAW,EACtB,IAAWC,EAAY,EACvB,IAAWC,EAAa,GAWjB,SAASC,EAAMhC,EAAOiC,EAAMC,EAAQC,EAAMC,EAAOC,EAAUpC,GACjE,MAAO,CAACD,MAAOA,EAAOiC,KAAMA,EAAMC,OAAQA,EAAQC,KAAMA,EAAMC,MAAOA,EAAOC,SAAUA,EAAUV,KAAMA,EAAMC,OAAQA,EAAQ3B,OAAQA,EAAQqC,OAAQ,IAQ9I,SAASC,EAAMN,EAAMG,GAC3B,OAAOvC,EAAOmC,EAAK,GAAI,KAAM,KAAM,GAAI,KAAM,KAAM,GAAIC,EAAM,CAAChC,QAASgC,EAAKhC,QAASmC,GAMtF,SAAgBI,IACf,OAAOV,EAMR,SAAgBW,IACfX,EAAYD,EAAW,EAAI3B,EAAO6B,IAAcF,GAAY,EAE5D,GAAID,IAAUE,IAAc,GAC3BF,EAAS,EAAGD,IAEb,OAAOG,EAMR,SAAgBY,IACfZ,EAAYD,EAAW5B,EAASC,EAAO6B,EAAYF,KAAc,EAEjE,GAAID,IAAUE,IAAc,GAC3BF,EAAS,EAAGD,IAEb,OAAOG,EAMR,SAAgBa,IACf,OAAOzC,EAAO6B,EAAYF,GAM3B,SAAgBe,IACf,OAAOf,EAQD,SAASZ,EAAOF,EAAOC,GAC7B,OAAOF,EAAOiB,EAAYhB,EAAOC,GAO3B,SAAS6B,EAAOV,GACtB,OAAQA,GAEP,KAAK,EAAG,KAAK,EAAG,KAAK,GAAI,KAAK,GAAI,KAAK,GACtC,OAAO,EAER,KAAK,GAAI,KAAK,GAAI,KAAK,GAAI,KAAK,GAAI,KAAK,GAAI,KAAK,GAAI,KAAK,IAE3D,KAAK,GAAI,KAAK,IAAK,KAAK,IACvB,OAAO,EAER,KAAK,GACJ,OAAO,EAER,KAAK,GAAI,KAAK,GAAI,KAAK,GAAI,KAAK,GAC/B,OAAO,EAER,KAAK,GAAI,KAAK,GACb,OAAO,EAGT,OAAO,EAOD,SAASW,EAAO9C,GACtB,OAAO2B,EAAOC,EAAS,EAAG3B,EAASiB,EAAOa,EAAa/B,GAAQ6B,EAAW,EAAG,GAOvE,SAASkB,EAAS/C,GACxB,OAAO+B,EAAa,GAAI/B,EAOlB,SAASgD,EAASb,GACxB,OAAOhC,EAAKc,EAAMY,EAAW,EAAGoB,GAAUd,IAAS,GAAKA,EAAO,EAAIA,IAAS,GAAKA,EAAO,EAAIA,KAOtF,SAASe,EAAUlD,GACzB,OAAO+C,EAAQI,EAAUL,EAAM9C,KAOzB,SAASoD,EAAYjB,GAC3B,MAAOL,EAAYa,IAClB,GAAIb,EAAY,GACfY,SAEA,MAEF,OAAOG,EAAMV,GAAQ,GAAKU,EAAMf,GAAa,EAAI,GAAK,IAOhD,SAASqB,EAAWd,GAC1B,MAAOK,IACN,OAAQG,EAAMf,IACb,KAAK,EAAGV,EAAOiC,GAAWxB,EAAW,GAAIQ,GACxC,MACD,KAAK,EAAGjB,EAAO4B,EAAQlB,GAAYO,GAClC,MACD,QAASjB,EAAO1B,EAAKoC,GAAYO,GAGnC,OAAOA,EAQD,SAASiB,GAAU1C,EAAO2C,GAChC,QAASA,GAASb,IAEjB,GAAIZ,EAAY,IAAMA,EAAY,KAAQA,EAAY,IAAMA,EAAY,IAAQA,EAAY,IAAMA,EAAY,GAC7G,MAEF,OAAOb,EAAML,EAAOgC,KAAWW,EAAQ,GAAKZ,KAAU,IAAMD,KAAU,KAOhE,SAASO,GAAWd,GAC1B,MAAOO,IACN,OAAQZ,GAEP,KAAKK,EACJ,OAAON,EAER,KAAK,GAAI,KAAK,GACb,GAAIM,IAAS,IAAMA,IAAS,GAC3Bc,GAAUnB,GACX,MAED,KAAK,GACJ,GAAIK,IAAS,GACZc,GAAUd,GACX,MAED,KAAK,GACJO,IACA,MAGH,OAAOb,EAQD,SAAS2B,GAAWrB,EAAMvB,GAChC,MAAO8B,IAEN,GAAIP,EAAOL,IAAc,GAAK,GAC7B,WAEI,GAAIK,EAAOL,IAAc,GAAK,IAAMa,MAAW,GACnD,MAEF,MAAO,KAAO1B,EAAML,EAAOiB,EAAW,GAAK,IAAMnC,EAAKyC,IAAS,GAAKA,EAAOO,KAOrE,SAASW,GAAYzC,GAC3B,OAAQiC,EAAMF,KACbD,IAED,OAAOzB,EAAML,EAAOiB,GC5Od,SAAS4B,GAASzD,GACxB,OAAO+C,EAAQW,GAAM,GAAI,KAAM,KAAM,KAAM,CAAC,IAAK1D,EAAQ8C,EAAM9C,GAAQ,EAAG,CAAC,GAAIA,IAehF,SAAgB0D,GAAO1D,EAAOiC,EAAMC,EAAQyB,EAAMC,EAAOC,EAAUC,EAAQC,EAAQC,GAClF,IAAIpD,EAAQ,EACZ,IAAIqD,EAAS,EACb,IAAIhE,EAAS6D,EACb,IAAII,EAAS,EACb,IAAIC,EAAW,EACf,IAAIC,EAAW,EACf,IAAIC,EAAW,EACf,IAAIC,EAAW,EACf,IAAIC,EAAY,EAChB,IAAIzC,EAAY,EAChB,IAAIK,EAAO,GACX,IAAIC,EAAQwB,EACZ,IAAIvB,EAAWwB,EACf,IAAIW,EAAYb,EAChB,IAAI5B,EAAaI,EAEjB,MAAOmC,EACN,OAAQF,EAAWtC,EAAWA,EAAYY,KAEzC,KAAK,GACJ,GAAI0B,GAAY,KAAOlE,EAAO6B,EAAY9B,EAAS,IAAM,GAAI,CAC5D,GAAIQ,EAAQsB,GAAcxB,EAAQyC,EAAQlB,GAAY,IAAK,OAAQ,SAAW,EAC7EyC,GAAa,EACd,MAGF,KAAK,GAAI,KAAK,GAAI,KAAK,GACtBxC,GAAciB,EAAQlB,GACtB,MAED,KAAK,EAAG,KAAK,GAAI,KAAK,GAAI,KAAK,GAC9BC,GAAcqB,EAAWgB,GACzB,MAED,KAAK,GACJrC,GAAcuB,GAASV,IAAU,EAAG,GACpC,SAED,KAAK,GACJ,OAAQD,KACP,KAAK,GAAI,KAAK,GACbvB,EAAOqD,GAAQjB,GAAUd,IAAQE,KAAUX,EAAMC,GAAS8B,GAC1D,MACD,QACCjC,GAAc,IAEhB,MAED,KAAK,IAAMsC,EACVN,EAAOnD,KAAWM,EAAOa,GAAcwC,EAExC,KAAK,IAAMF,EAAU,KAAK,GAAI,KAAK,EAClC,OAAQvC,GAEP,KAAK,EAAG,KAAK,IAAKwC,EAAW,EAE7B,KAAK,GAAKL,EAAQ,GAAIM,IAAc,EAAGxC,EAAaxB,EAAQwB,EAAY,MAAO,IAC9E,GAAIoC,EAAW,GAAMjD,EAAOa,GAAc9B,EACzCmB,EAAO+C,EAAW,GAAKO,GAAY3C,EAAa,IAAK4B,EAAMzB,EAAQjC,EAAS,GAAKyE,GAAYnE,EAAQwB,EAAY,IAAK,IAAM,IAAK4B,EAAMzB,EAAQjC,EAAS,GAAI+D,GAC7J,MAED,KAAK,GAAIjC,GAAc,IAEvB,QACCX,EAAOoD,EAAYG,GAAQ5C,EAAYE,EAAMC,EAAQtB,EAAOqD,EAAQL,EAAOG,EAAQ5B,EAAMC,EAAQ,GAAIC,EAAW,GAAIpC,GAAS4D,GAE7H,GAAI/B,IAAc,IACjB,GAAImC,IAAW,EACdP,GAAM3B,EAAYE,EAAMuC,EAAWA,EAAWpC,EAAOyB,EAAU5D,EAAQ8D,EAAQ1B,QAE/E,OAAQ6B,IAAW,IAAMhE,EAAO6B,EAAY,KAAO,IAAM,IAAMmC,GAE9D,KAAK,IAAK,KAAK,IAAK,KAAK,IAAK,KAAK,IAClCR,GAAM1D,EAAOwE,EAAWA,EAAWb,GAAQvC,EAAOuD,GAAQ3E,EAAOwE,EAAWA,EAAW,EAAG,EAAGZ,EAAOG,EAAQ5B,EAAMyB,EAAOxB,EAAQ,GAAInC,GAASoC,GAAWuB,EAAOvB,EAAUpC,EAAQ8D,EAAQJ,EAAOvB,EAAQC,GACzM,MACD,QACCqB,GAAM3B,EAAYyC,EAAWA,EAAWA,EAAW,CAAC,IAAKnC,EAAU,EAAG0B,EAAQ1B,IAIpFzB,EAAQqD,EAASE,EAAW,EAAGE,EAAWE,EAAY,EAAGpC,EAAOJ,EAAa,GAAI9B,EAAS6D,EAC1F,MAED,KAAK,GACJ7D,EAAS,EAAIiB,EAAOa,GAAaoC,EAAWC,EAC7C,QACC,GAAIC,EAAW,EACd,GAAIvC,GAAa,MACduC,OACE,GAAIvC,GAAa,KAAOuC,KAAc,GAAK5B,KAAU,IACzD,SAEF,OAAQV,GAAcrC,EAAKoC,GAAYA,EAAYuC,GAElD,KAAK,GACJE,EAAYN,EAAS,EAAI,GAAKlC,GAAc,MAAO,GACnD,MAED,KAAK,GACJgC,EAAOnD,MAAYM,EAAOa,GAAc,GAAKwC,EAAWA,EAAY,EACpE,MAED,KAAK,GAEJ,GAAI5B,MAAW,GACdZ,GAAciB,EAAQN,KAEvBwB,EAASvB,IAAQsB,EAAShE,EAASiB,EAAOiB,EAAOJ,GAAcsB,GAAWT,MAAWd,IACrF,MAED,KAAK,GACJ,GAAIsC,IAAa,IAAMlD,EAAOa,IAAe,EAC5CsC,EAAW,GAIjB,OAAOR,EAiBR,SAAgBc,GAAS3E,EAAOiC,EAAMC,EAAQtB,EAAOqD,EAAQL,EAAOG,EAAQ5B,EAAMC,EAAOC,EAAUpC,GAClG,IAAI2E,EAAOX,EAAS,EACpB,IAAIN,EAAOM,IAAW,EAAIL,EAAQ,CAAC,IACnC,IAAIiB,EAAO1D,EAAOwC,GAElB,IAAK,IAAImB,EAAI,EAAGC,EAAI,EAAGC,EAAI,EAAGF,EAAIlE,IAASkE,EAC1C,IAAK,IAAIG,EAAI,EAAGC,EAAIpE,EAAOd,EAAO4E,EAAO,EAAGA,EAAOpF,EAAIuF,EAAIhB,EAAOe,KAAMK,EAAInF,EAAOiF,EAAIJ,IAAQI,EAC9F,GAAIE,EAAIhF,EAAK4E,EAAI,EAAIpB,EAAKsB,GAAK,IAAMC,EAAI3E,EAAQ2E,EAAG,OAAQvB,EAAKsB,KAChE7C,EAAM4C,KAAOG,EAEhB,OAAOnD,EAAKhC,EAAOiC,EAAMC,EAAQ+B,IAAW,EAAIxF,EAAU0D,EAAMC,EAAOC,EAAUpC,GASlF,SAAgBwE,GAASzE,EAAOiC,EAAMC,GACrC,OAAOF,EAAKhC,EAAOiC,EAAMC,EAAQ1D,EAASkB,EAAK8C,KAAS1B,EAAOd,EAAO,GAAI,GAAI,GAU/E,SAAgB0E,GAAa1E,EAAOiC,EAAMC,EAAQjC,GACjD,OAAO+B,EAAKhC,EAAOiC,EAAMC,EAAQxD,EAAaoC,EAAOd,EAAO,EAAGC,GAASa,EAAOd,EAAOC,EAAS,GAAI,GAAIA,GCpLxG,SAAgBmF,GAAQpF,EAAOC,EAAQoC,GACtC,OAAQtC,EAAKC,EAAOC,IAEnB,KAAK,KACJ,OAAO1B,EAAS,SAAWyB,EAAQA,EAEpC,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAEvE,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAE5D,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAE5D,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAC3D,OAAOzB,EAASyB,EAAQA,EAEzB,KAAK,KACJ,OAAO1B,EAAM0B,EAAQA,EAEtB,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAChD,OAAOzB,EAASyB,EAAQ1B,EAAM0B,EAAQ3B,EAAK2B,EAAQA,EAEpD,KAAK,KACJ,OAAQE,EAAOF,EAAOC,EAAS,KAE9B,KAAK,IACJ,OAAO1B,EAASyB,EAAQ3B,EAAKkC,EAAQP,EAAO,qBAAsB,MAAQA,EAE3E,KAAK,IACJ,OAAOzB,EAASyB,EAAQ3B,EAAKkC,EAAQP,EAAO,qBAAsB,SAAWA,EAE9E,KAAK,GACJ,OAAOzB,EAASyB,EAAQ3B,EAAKkC,EAAQP,EAAO,qBAAsB,MAAQA,EAI7E,KAAK,KAAM,KAAK,KAAM,KAAK,KAC1B,OAAOzB,EAASyB,EAAQ3B,EAAK2B,EAAQA,EAEtC,KAAK,KACJ,OAAOzB,EAASyB,EAAQ3B,EAAK,QAAU2B,EAAQA,EAEhD,KAAK,KACJ,OAAOzB,EAASyB,EAAQO,EAAQP,EAAO,iBAAkBzB,EAAS,WAAaF,EAAK,aAAe2B,EAEpG,KAAK,KACJ,OAAOzB,EAASyB,EAAQ3B,EAAK,aAAekC,EAAQP,EAAO,eAAgB,MAAQI,EAAMJ,EAAO,kBAAoB3B,EAAK,YAAckC,EAAQP,EAAO,eAAgB,IAAM,IAAMA,EAEnL,KAAK,KACJ,OAAOzB,EAASyB,EAAQ3B,EAAK,iBAAmBkC,EAAQP,EAAO,6BAA8B,IAAMA,EAEpG,KAAK,KACJ,OAAOzB,EAASyB,EAAQ3B,EAAKkC,EAAQP,EAAO,SAAU,YAAcA,EAErE,KAAK,KACJ,OAAOzB,EAASyB,EAAQ3B,EAAKkC,EAAQP,EAAO,QAAS,kBAAoBA,EAE1E,KAAK,KACJ,OAAOzB,EAAS,OAASgC,EAAQP,EAAO,QAAS,IAAMzB,EAASyB,EAAQ3B,EAAKkC,EAAQP,EAAO,OAAQ,YAAcA,EAEnH,KAAK,KACJ,OAAOzB,EAASgC,EAAQP,EAAO,qBAAsB,KAAOzB,EAAS,MAAQyB,EAE9E,KAAK,KACJ,OAAOO,EAAQA,EAAQA,EAAQP,EAAO,eAAgBzB,EAAS,MAAO,cAAeA,EAAS,MAAOyB,EAAO,IAAMA,EAEnH,KAAK,KAAM,KAAK,KACf,OAAOO,EAAQP,EAAO,oBAAqBzB,EAAS,KAAO,QAE5D,KAAK,KACJ,OAAOgC,EAAQA,EAAQP,EAAO,oBAAqBzB,EAAS,cAAgBF,EAAK,gBAAiB,aAAc,WAAaE,EAASyB,EAAQA,EAE/I,KAAK,KACJ,IAAKI,EAAMJ,EAAO,kBAAmB,OAAO3B,EAAK,oBAAsByC,EAAOd,EAAOC,GAAUD,EAC/F,MAED,KAAK,KAAM,KAAK,KACf,OAAO3B,EAAKkC,EAAQP,EAAO,YAAa,IAAMA,EAE/C,KAAK,KAAM,KAAK,KACf,GAAIqC,GAAYA,EAASgD,MAAK,SAAUC,EAAS1E,GAAS,OAAOX,EAASW,EAAOR,EAAMkF,EAAQlD,MAAO,mBAAoB,CACzH,OAAQ3B,EAAQT,GAASqC,EAAWA,EAASpC,GAAQD,OAAQ,QAAUA,EAAS3B,EAAKkC,EAAQP,EAAO,SAAU,IAAMA,EAAQ3B,EAAK,mBAAqBoC,EAAQ4B,EAAU,QAAUjC,EAAMiC,EAAU,QAAUjC,EAAMiC,EAAU,QAAUjC,EAAMJ,EAAO,QAAU,IAE9P,OAAO3B,EAAKkC,EAAQP,EAAO,SAAU,IAAMA,EAE5C,KAAK,KAAM,KAAK,KACf,OAAQqC,GAAYA,EAASgD,MAAK,SAAUC,GAAW,OAAOlF,EAAMkF,EAAQlD,MAAO,qBAAwBpC,EAAQ3B,EAAKkC,EAAQA,EAAQP,EAAO,OAAQ,SAAU,QAAS,IAAMA,EAEjL,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KACrC,OAAOO,EAAQP,EAAO,kBAAmBzB,EAAS,QAAUyB,EAE7D,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KACtC,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KACtC,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAErC,GAAIkB,EAAOlB,GAAS,EAAIC,EAAS,EAChC,OAAQC,EAAOF,EAAOC,EAAS,IAE9B,KAAK,IAEJ,GAAIC,EAAOF,EAAOC,EAAS,KAAO,GACjC,MAEF,KAAK,IACJ,OAAOM,EAAQP,EAAO,mBAAoB,KAAOzB,EAAS,QAAU,KAAOD,GAAO4B,EAAOF,EAAOC,EAAS,IAAM,IAAM,KAAO,UAAYD,EAEzI,KAAK,IACJ,OAAQS,EAAQT,EAAO,WAAaoF,GAAO7E,EAAQP,EAAO,UAAW,kBAAmBC,EAAQoC,GAAYrC,EAAQA,EAEvH,MAED,KAAK,KAAM,KAAK,KACf,OAAOO,EAAQP,EAAO,6CAA6C,SAAUuF,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,GAAK,OAAQxH,EAAKmH,EAAI,IAAMC,EAAII,GAAMH,EAAKrH,EAAKmH,EAAI,UAAYG,EAAIC,GAAKA,GAAKH,GAAMI,EAAI,IAAM7F,KAE9L,KAAK,KAEJ,GAAIE,EAAOF,EAAOC,EAAS,KAAO,IACjC,OAAOM,EAAQP,EAAO,IAAK,IAAMzB,GAAUyB,EAC5C,MAED,KAAK,KACJ,OAAQE,EAAOF,EAAOE,EAAOF,EAAO,MAAQ,GAAK,GAAK,KAErD,KAAK,IACJ,OAAOO,EAAQP,EAAO,gCAAiC,KAAOzB,GAAU2B,EAAOF,EAAO,MAAQ,GAAK,UAAY,IAAM,QAAU,KAAOzB,EAAS,OAAS,KAAOF,EAAK,WAAa2B,EAElL,KAAK,IACJ,OAAOO,EAAQP,EAAO,IAAK,IAAM3B,GAAM2B,EAEzC,MAED,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAAM,KAAK,KAChD,OAAOO,EAAQP,EAAO,UAAW,gBAAkBA,EAGrD,OAAOA,ECvID,SAAS8F,GAAWzD,EAAUb,GACpC,IAAIuE,EAAS,GACb,IAAI9F,EAASkB,EAAOkB,GAEpB,IAAK,IAAIyC,EAAI,EAAGA,EAAI7E,EAAQ6E,IAC3BiB,GAAUvE,EAASa,EAASyC,GAAIA,EAAGzC,EAAUb,IAAa,GAE3D,OAAOuE,EAUR,SAAgBC,GAAWV,EAAS1E,EAAOyB,EAAUb,GACpD,OAAQ8D,EAAQnD,MACf,KAAK5C,EAAO,GAAI+F,EAAQjD,SAASpC,OAAQ,MACzC,KAAKpB,EAAQ,KAAKH,EAAa,OAAO4G,EAAQhD,OAASgD,EAAQhD,QAAUgD,EAAQtF,MACjF,KAAKxB,EAAS,MAAO,GACrB,KAAKW,EAAW,OAAOmG,EAAQhD,OAASgD,EAAQtF,MAAQ,IAAM8F,GAAUR,EAAQjD,SAAUb,GAAY,IACtG,KAAK/C,EAAS6G,EAAQtF,MAAQsF,EAAQlD,MAAMV,KAAK,KAGlD,OAAOR,EAAOmB,EAAWyD,GAAUR,EAAQjD,SAAUb,IAAa8D,EAAQhD,OAASgD,EAAQtF,MAAQ,IAAMqC,EAAW,IAAM,GCxBpH,SAAS4D,GAAYC,GAC3B,IAAIjG,EAASkB,EAAO+E,GAEpB,OAAO,SAAUZ,EAAS1E,EAAOyB,EAAUb,GAC1C,IAAIuE,EAAS,GAEb,IAAK,IAAIjB,EAAI,EAAGA,EAAI7E,EAAQ6E,IAC3BiB,GAAUG,EAAWpB,GAAGQ,EAAS1E,EAAOyB,EAAUb,IAAa,GAEhE,OAAOuE,GAQF,SAASI,GAAW3E,GAC1B,OAAO,SAAU8D,GAChB,IAAKA,EAAQrD,KACZ,GAAIqD,EAAUA,EAAQhD,OACrBd,EAAS8D,IAUb,SAAgBc,GAAUd,EAAS1E,EAAOyB,EAAUb,GACnD,GAAI8D,EAAQrF,QAAU,EACrB,IAAKqF,EAAQhD,OACZ,OAAQgD,EAAQnD,MACf,KAAKzD,EAAa4G,EAAQhD,OAAS8C,GAAOE,EAAQtF,MAAOsF,EAAQrF,OAAQoC,GACxE,OACD,KAAKlD,EACJ,OAAO2G,GAAU,CAACvD,EAAK+C,EAAS,CAACtF,MAAOO,EAAQ+E,EAAQtF,MAAO,IAAK,IAAMzB,MAAYiD,GACvF,KAAK/C,EACJ,GAAI6G,EAAQrF,OACX,OAAOsB,EAAQ+D,EAAQlD,OAAO,SAAUpC,GACvC,OAAQI,EAAMJ,EAAO,0BAEpB,IAAK,aAAc,IAAK,cACvB,OAAO8F,GAAU,CAACvD,EAAK+C,EAAS,CAAClD,MAAO,CAAC7B,EAAQP,EAAO,cAAe,IAAM1B,EAAM,UAAWkD,GAE/F,IAAK,gBACJ,OAAOsE,GAAU,CAChBvD,EAAK+C,EAAS,CAAClD,MAAO,CAAC7B,EAAQP,EAAO,aAAc,IAAMzB,EAAS,eACnEgE,EAAK+C,EAAS,CAAClD,MAAO,CAAC7B,EAAQP,EAAO,aAAc,IAAM1B,EAAM,SAChEiE,EAAK+C,EAAS,CAAClD,MAAO,CAAC7B,EAAQP,EAAO,aAAc3B,EAAK,gBACvDmD,GAGL,MAAO,OAUP,SAAS6E,GAAWf,GAC1B,OAAQA,EAAQnD,MACf,KAAK1D,EACJ6G,EAAQlD,MAAQkD,EAAQlD,MAAMX,KAAI,SAAUzB,GAC3C,OAAOuB,EAAQ2B,EAASlD,IAAQ,SAAUA,EAAOY,EAAOyB,GACvD,OAAQnC,EAAOF,EAAO,IAErB,KAAK,GACJ,OAAOc,EAAOd,EAAO,EAAGkB,EAAOlB,IAEhC,KAAK,EAAG,KAAK,GAAI,KAAK,GAAI,KAAK,GAAI,KAAK,IACvC,OAAOA,EAER,KAAK,GACJ,GAAIqC,IAAWzB,KAAW,SACzByB,EAASzB,GAAS,GAAIyB,IAAWzB,GAAS,KAAOE,EAAOuB,EAASzB,GAAQA,EAAQ,GAAI,GAEvF,KAAK,GACJ,OAAOA,IAAU,EAAI,GAAKZ,EAC3B,QACC,OAAQY,GACP,KAAK,EAAG0E,EAAUtF,EACjB,OAAOmB,EAAOkB,GAAY,EAAI,GAAKrC,EACpC,KAAKY,EAAQO,EAAOkB,GAAY,EAAG,KAAK,EACvC,OAAOzB,IAAU,EAAIZ,EAAQsF,EAAUA,EAAUtF,EAAQsF,EAC1D,QACC,OAAOtF"}