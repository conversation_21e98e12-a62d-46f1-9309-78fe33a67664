{"name": "stylis", "version": "4.2.0", "license": "MIT", "description": "A Light–weight CSS Preprocessor", "homepage": "https://github.com/thysultan/stylis.js", "author": "<PERSON> <sultan<PERSON><PERSON>@me.com>", "repository": "https://github.com/thysultan/stylis.js", "bugs": "https://github.com/thysultan/stylis.js/issues", "sideEffects": false, "type": "module", "main": "dist/umd/stylis.js", "module": "dist/stylis.mjs", "react-native": "./index.js", "exports": {".": {"import": "./index.js", "require": "./dist/umd/stylis.js"}, "./package.json": "./package.json"}, "files": ["index.js", "dist/", "src/"], "scripts": {"lint": "eslint ./", "pretest": "npm run lint && npm run build", "test": "nyc npm run spec", "spec": "mocha --harmony --require esm script/setup.js --recursive test", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "rollup --config script/build.js --configSrc ./", "start": "npm run build -- --watch", "prepare": "npm run build", "postversion": "git push --follow-tags && npm publish", "release-major": "npm version major -m '%s'", "release-minor": "npm version minor -m '%s'", "release-patch": "npm version patch -m '%s'"}, "devDependencies": {"chai": "4.3.4", "eslint": "6.8.0", "esm": "3.2.25", "mocha": "9.1.1", "nyc": "15.1.0", "rimraf": "3.0.2", "rollup": "1.28.0", "rollup-plugin-size": "0.2.1", "rollup-plugin-terser": "5.1.3", "stylis": "./"}, "nyc": {"temp-dir": "./coverage/.nyc_output", "exclude": ["**/dist/", "**/test/", "**/script/"], "reporter": ["lcov", "text"]}, "esm": {"cjs": true, "cache": false}, "eslintIgnore": ["script/", "test/", "dist/", "docs/"], "eslintConfig": {"env": {"commonjs": true, "browser": true, "node": true, "es6": true}, "extends": "eslint:recommended", "parserOptions": {"ecmaVersion": 7, "sourceType": "module", "ecmaFeatures": {"impliedStrict": true}}, "rules": {"indent": ["error", "tab", {"SwitchCase": 1}], "linebreak-style": ["error", "unix"], "quotes": ["error", "single"], "semi": ["error", "never"], "no-cond-assign": ["off"], "no-redeclare": ["off"], "no-fallthrough": ["off"], "no-console": ["off"], "no-unsafe-finally": ["off"], "no-shadow-restricted-names": ["error"], "no-whitespace-before-property": ["error"], "no-else-return": ["error"], "eol-last": ["error"], "func-call-spacing": ["error", "never"], "brace-style": ["error", "1tbs", {"allowSingleLine": true}], "require-jsdoc": ["error", {"require": {"FunctionDeclaration": true}}], "no-trailing-spaces": ["error", {"skipBlankLines": true}], "no-constant-condition": ["off"]}}}