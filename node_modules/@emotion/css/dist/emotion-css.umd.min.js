!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports):"function"==typeof define&&define.amd?define(["exports"],r):r((e="undefined"!=typeof globalThis?globalThis:e||self).emotion={})}(this,(function(e){"use strict";var r=function(){function e(e){var r=this;this._insertTag=function(e){var t;t=0===r.tags.length?r.insertionPoint?r.insertionPoint.nextSibling:r.prepend?r.container.firstChild:r.before:r.tags[r.tags.length-1].nextSibling,r.container.insertBefore(e,t),r.tags.push(e)},this.isSpeedy=void 0===e.speedy||e.speedy,this.tags=[],this.ctr=0,this.nonce=e.nonce,this.key=e.key,this.container=e.container,this.prepend=e.prepend,this.insertionPoint=e.insertionPoint,this.before=null}var r=e.prototype;return r.hydrate=function(e){e.forEach(this._insertTag)},r.insert=function(e){this.ctr%(this.isSpeedy?65e3:1)==0&&this._insertTag(function(e){var r=document.createElement("style");return r.setAttribute("data-emotion",e.key),void 0!==e.nonce&&r.setAttribute("nonce",e.nonce),r.appendChild(document.createTextNode("")),r.setAttribute("data-s",""),r}(this));var r=this.tags[this.tags.length-1];if(this.isSpeedy){var t=function(e){if(e.sheet)return e.sheet;for(var r=0;r<document.styleSheets.length;r++)if(document.styleSheets[r].ownerNode===e)return document.styleSheets[r]}(r);try{t.insertRule(e,t.cssRules.length)}catch(e){}}else r.appendChild(document.createTextNode(e));this.ctr++},r.flush=function(){this.tags.forEach((function(e){var r;return null==(r=e.parentNode)?void 0:r.removeChild(e)})),this.tags=[],this.ctr=0},e}(),t="-ms-",n="-moz-",a="-webkit-",s="comm",i="rule",c="decl",o="@keyframes",u=Math.abs,l=String.fromCharCode,f=Object.assign;function d(e){return e.trim()}function h(e,r,t){return e.replace(r,t)}function p(e,r){return e.indexOf(r)}function v(e,r){return 0|e.charCodeAt(r)}function g(e,r,t){return e.slice(r,t)}function y(e){return e.length}function m(e){return e.length}function b(e,r){return r.push(e),e}var w=1,k=1,x=0,$=0,A=0,C="";function S(e,r,t,n,a,s,i){return{value:e,root:r,parent:t,type:n,props:a,children:s,line:w,column:k,length:i,return:""}}function O(e,r){return f(S("",null,null,"",null,null,0),e,{length:-e.length},r)}function E(){return A=$>0?v(C,--$):0,k--,10===A&&(k=1,w--),A}function _(){return A=$<x?v(C,$++):0,k++,10===A&&(k=1,w++),A}function j(){return v(C,$)}function R(){return $}function G(e,r){return g(C,e,r)}function N(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function P(e){return w=k=1,x=y(C=e),$=0,[]}function T(e){return C="",e}function M(e){return d(G($-1,W(91===e?e+2:40===e?e+1:e)))}function z(e){for(;(A=j())&&A<33;)_();return N(e)>2||N(A)>3?"":" "}function I(e,r){for(;--r&&_()&&!(A<48||A>102||A>57&&A<65||A>70&&A<97););return G(e,R()+(r<6&&32==j()&&32==_()))}function W(e){for(;_();)switch(A){case e:return $;case 34:case 39:34!==e&&39!==e&&W(A);break;case 40:41===e&&W(e);break;case 92:_()}return $}function L(e,r){for(;_()&&e+A!==57&&(e+A!==84||47!==j()););return"/*"+G(r,$-1)+"*"+l(47===e?e:_())}function q(e){for(;!N(j());)_();return G(e,$)}function D(e){return T(F("",null,null,null,[""],e=P(e),0,[0],e))}function F(e,r,t,n,a,s,i,c,o){for(var u=0,f=0,d=i,g=0,m=0,w=0,k=1,x=1,$=1,A=0,C="",S=a,O=s,G=n,N=C;x;)switch(w=A,A=_()){case 40:if(108!=w&&58==v(N,d-1)){-1!=p(N+=h(M(A),"&","&\f"),"&\f")&&($=-1);break}case 34:case 39:case 91:N+=M(A);break;case 9:case 10:case 13:case 32:N+=z(w);break;case 92:N+=I(R()-1,7);continue;case 47:switch(j()){case 42:case 47:b(H(L(_(),R()),r,t),o);break;default:N+="/"}break;case 123*k:c[u++]=y(N)*$;case 125*k:case 59:case 0:switch(A){case 0:case 125:x=0;case 59+f:-1==$&&(N=h(N,/\f/g,"")),m>0&&y(N)-d&&b(m>32?Z(N+";",n,t,d-1):Z(h(N," ","")+";",n,t,d-2),o);break;case 59:N+=";";default:if(b(G=B(N,r,t,u,f,a,c,C,S=[],O=[],d),s),123===A)if(0===f)F(N,r,G,G,S,s,d,c,O);else switch(99===g&&110===v(N,3)?100:g){case 100:case 108:case 109:case 115:F(e,G,G,n&&b(B(e,G,G,0,0,a,c,C,a,S=[],d),O),a,O,d,c,n?S:O);break;default:F(N,G,G,G,[""],O,0,c,O)}}u=f=m=0,k=$=1,C=N="",d=i;break;case 58:d=1+y(N),m=w;default:if(k<1)if(123==A)--k;else if(125==A&&0==k++&&125==E())continue;switch(N+=l(A),A*k){case 38:$=f>0?1:(N+="\f",-1);break;case 44:c[u++]=(y(N)-1)*$,$=1;break;case 64:45===j()&&(N+=M(_())),g=j(),f=d=y(C=N+=q(R())),A++;break;case 45:45===w&&2==y(N)&&(k=0)}}return s}function B(e,r,t,n,a,s,c,o,l,f,p){for(var v=a-1,y=0===a?s:[""],b=m(y),w=0,k=0,x=0;w<n;++w)for(var $=0,A=g(e,v+1,v=u(k=c[w])),C=e;$<b;++$)(C=d(k>0?y[$]+" "+A:h(A,/&\f/g,y[$])))&&(l[x++]=C);return S(e,r,t,0===a?i:o,l,f,p)}function H(e,r,t){return S(e,r,t,s,l(A),g(e,2,-2),0)}function Z(e,r,t,n){return S(e,r,t,c,g(e,0,n),g(e,n+1,-1),n)}function J(e,r){for(var t="",n=m(e),a=0;a<n;a++)t+=r(e[a],a,e,r)||"";return t}function K(e,r,t,n){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case c:return e.return=e.return||e.value;case s:return"";case o:return e.return=e.value+"{"+J(e.children,n)+"}";case i:e.value=e.props.join(",")}return y(t=J(e.children,n))?e.return=e.value+"{"+t+"}":""}function Q(e){var r=Object.create(null);return function(t){return void 0===r[t]&&(r[t]=e(t)),r[t]}}var U=function(e,r,t){for(var n=0,a=0;n=a,a=j(),38===n&&12===a&&(r[t]=1),!N(a);)_();return G(e,$)},V=function(e,r){return T(function(e,r){var t=-1,n=44;do{switch(N(n)){case 0:38===n&&12===j()&&(r[t]=1),e[t]+=U($-1,r,t);break;case 2:e[t]+=M(n);break;case 4:if(44===n){e[++t]=58===j()?"&\f":"",r[t]=e[t].length;break}default:e[t]+=l(n)}}while(n=_());return e}(P(e),r))},X=new WeakMap,Y=function(e){if("rule"===e.type&&e.parent&&!(e.length<1)){for(var r=e.value,t=e.parent,n=e.column===t.column&&e.line===t.line;"rule"!==t.type;)if(!(t=t.parent))return;if((1!==e.props.length||58===r.charCodeAt(0)||X.get(t))&&!n){X.set(e,!0);for(var a=[],s=V(r,a),i=t.props,c=0,o=0;c<s.length;c++)for(var u=0;u<i.length;u++,o++)e.props[o]=a[c]?s[c].replace(/&\f/g,i[u]):i[u]+" "+s[c]}}},ee=function(e){if("decl"===e.type){var r=e.value;108===r.charCodeAt(0)&&98===r.charCodeAt(2)&&(e.return="",e.value="")}};function re(e,r){switch(function(e,r){return 45^v(e,0)?(((r<<2^v(e,0))<<2^v(e,1))<<2^v(e,2))<<2^v(e,3):0}(e,r)){case 5103:return a+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return a+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return a+e+n+e+t+e+e;case 6828:case 4268:return a+e+t+e+e;case 6165:return a+e+t+"flex-"+e+e;case 5187:return a+e+h(e,/(\w+).+(:[^]+)/,a+"box-$1$2"+t+"flex-$1$2")+e;case 5443:return a+e+t+"flex-item-"+h(e,/flex-|-self/,"")+e;case 4675:return a+e+t+"flex-line-pack"+h(e,/align-content|flex-|-self/,"")+e;case 5548:return a+e+t+h(e,"shrink","negative")+e;case 5292:return a+e+t+h(e,"basis","preferred-size")+e;case 6060:return a+"box-"+h(e,"-grow","")+a+e+t+h(e,"grow","positive")+e;case 4554:return a+h(e,/([^-])(transform)/g,"$1"+a+"$2")+e;case 6187:return h(h(h(e,/(zoom-|grab)/,a+"$1"),/(image-set)/,a+"$1"),e,"")+e;case 5495:case 3959:return h(e,/(image-set\([^]*)/,a+"$1$`$1");case 4968:return h(h(e,/(.+:)(flex-)?(.*)/,a+"box-pack:$3"+t+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+a+e+e;case 4095:case 3583:case 4068:case 2532:return h(e,/(.+)-inline(.+)/,a+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(y(e)-1-r>6)switch(v(e,r+1)){case 109:if(45!==v(e,r+4))break;case 102:return h(e,/(.+:)(.+)-([^]+)/,"$1"+a+"$2-$3$1"+n+(108==v(e,r+3)?"$3":"$2-$3"))+e;case 115:return~p(e,"stretch")?re(h(e,"stretch","fill-available"),r)+e:e}break;case 4949:if(115!==v(e,r+1))break;case 6444:switch(v(e,y(e)-3-(~p(e,"!important")&&10))){case 107:return h(e,":",":"+a)+e;case 101:return h(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+a+(45===v(e,14)?"inline-":"")+"box$3$1"+a+"$2$3$1"+t+"$2box$3")+e}break;case 5936:switch(v(e,r+11)){case 114:return a+e+t+h(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return a+e+t+h(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return a+e+t+h(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return a+e+t+e+e}return e}var te=[function(e,r,s,u){if(e.length>-1&&!e.return)switch(e.type){case c:e.return=re(e.value,e.length);break;case o:return J([O(e,{value:h(e.value,"@","@"+a)})],u);case i:if(e.length)return function(e,r){return e.map(r).join("")}(e.props,(function(r){switch(function(e,r){return(e=r.exec(e))?e[0]:e}(r,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return J([O(e,{props:[h(r,/:(read-\w+)/,":"+n+"$1")]})],u);case"::placeholder":return J([O(e,{props:[h(r,/:(plac\w+)/,":"+a+"input-$1")]}),O(e,{props:[h(r,/:(plac\w+)/,":"+n+"$1")]}),O(e,{props:[h(r,/:(plac\w+)/,t+"input-$1")]})],u)}return""}))}}],ne=function(e){var t=e.key;if("css"===t){var n=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(n,(function(e){-1!==e.getAttribute("data-emotion").indexOf(" ")&&(document.head.appendChild(e),e.setAttribute("data-s",""))}))}var a,s,i=e.stylisPlugins||te,c={},o=[];a=e.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+t+' "]'),(function(e){for(var r=e.getAttribute("data-emotion").split(" "),t=1;t<r.length;t++)c[r[t]]=!0;o.push(e)}));var u,l,f=[K,(l=function(e){u.insert(e)},function(e){e.root||(e=e.return)&&l(e)})],d=function(e){var r=m(e);return function(t,n,a,s){for(var i="",c=0;c<r;c++)i+=e[c](t,n,a,s)||"";return i}}([Y,ee].concat(i,f));s=function(e,r,t,n){u=t,J(D(e?e+"{"+r.styles+"}":r.styles),d),n&&(h.inserted[r.name]=!0)};var h={key:t,sheet:new r({key:t,container:a,nonce:e.nonce,speedy:e.speedy,prepend:e.prepend,insertionPoint:e.insertionPoint}),nonce:e.nonce,inserted:c,registered:{},insert:s};return h.sheet.hydrate(o),h};var ae={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},se=!1,ie=/[A-Z]|^ms/g,ce=/_EMO_([^_]+?)_([^]*?)_EMO_/g,oe=function(e){return 45===e.charCodeAt(1)},ue=function(e){return null!=e&&"boolean"!=typeof e},le=Q((function(e){return oe(e)?e:e.replace(ie,"-$&").toLowerCase()})),fe=function(e,r){switch(e){case"animation":case"animationName":if("string"==typeof r)return r.replace(ce,(function(e,r,t){return pe={name:r,styles:t,next:pe},r}))}return 1===ae[e]||oe(e)||"number"!=typeof r||0===r?r:r+"px"},de="Component selectors can only be used in conjunction with @emotion/babel-plugin, the swc Emotion plugin, or another Emotion-aware compiler transform.";function he(e,r,t){if(null==t)return"";var n=t;if(void 0!==n.__emotion_styles)return n;switch(typeof t){case"boolean":return"";case"object":var a=t;if(1===a.anim)return pe={name:a.name,styles:a.styles,next:pe},a.name;var s=t;if(void 0!==s.styles){var i=s.next;if(void 0!==i)for(;void 0!==i;)pe={name:i.name,styles:i.styles,next:pe},i=i.next;return s.styles+";"}return function(e,r,t){var n="";if(Array.isArray(t))for(var a=0;a<t.length;a++)n+=he(e,r,t[a])+";";else for(var s in t){var i=t[s];if("object"!=typeof i){var c=i;null!=r&&void 0!==r[c]?n+=s+"{"+r[c]+"}":ue(c)&&(n+=le(s)+":"+fe(s,c)+";")}else{if("NO_COMPONENT_SELECTOR"===s&&se)throw new Error(de);if(!Array.isArray(i)||"string"!=typeof i[0]||null!=r&&void 0!==r[i[0]]){var o=he(e,r,i);switch(s){case"animation":case"animationName":n+=le(s)+":"+o+";";break;default:n+=s+"{"+o+"}"}}else for(var u=0;u<i.length;u++)ue(i[u])&&(n+=le(s)+":"+fe(s,i[u])+";")}}return n}(e,r,t);case"function":if(void 0!==e){var c=pe,o=t(e);return pe=c,he(e,r,o)}}var u=t;if(null==r)return u;var l=r[u];return void 0!==l?l:u}var pe,ve=/label:\s*([^\s;{]+)\s*(;|$)/g;function ge(e,r,t){if(1===e.length&&"object"==typeof e[0]&&null!==e[0]&&void 0!==e[0].styles)return e[0];var n=!0,a="";pe=void 0;var s=e[0];null==s||void 0===s.raw?(n=!1,a+=he(t,r,s)):a+=s[0];for(var i=1;i<e.length;i++){if(a+=he(t,r,e[i]),n)a+=s[i]}ve.lastIndex=0;for(var c,o="";null!==(c=ve.exec(a));)o+="-"+c[1];var u=function(e){for(var r,t=0,n=0,a=e.length;a>=4;++n,a-=4)r=1540483477*(65535&(r=255&e.charCodeAt(n)|(255&e.charCodeAt(++n))<<8|(255&e.charCodeAt(++n))<<16|(255&e.charCodeAt(++n))<<24))+(59797*(r>>>16)<<16),t=1540483477*(65535&(r^=r>>>24))+(59797*(r>>>16)<<16)^1540483477*(65535&t)+(59797*(t>>>16)<<16);switch(a){case 3:t^=(255&e.charCodeAt(n+2))<<16;case 2:t^=(255&e.charCodeAt(n+1))<<8;case 1:t=1540483477*(65535&(t^=255&e.charCodeAt(n)))+(59797*(t>>>16)<<16)}return(((t=1540483477*(65535&(t^=t>>>13))+(59797*(t>>>16)<<16))^t>>>15)>>>0).toString(36)}(a)+o;return{name:u,styles:a,next:pe}}function ye(e,r,t){var n="";return t.split(" ").forEach((function(t){void 0!==e[t]?r.push(e[t]+";"):t&&(n+=t+" ")})),n}function me(e,r){if(void 0===e.inserted[r.name])return e.insert("",r,e.sheet,!0)}function be(e,r,t){var n=[],a=ye(e,n,t);return n.length<2?t:a+r(n)}var we=function e(r){for(var t="",n=0;n<r.length;n++){var a=r[n];if(null!=a){var s=void 0;switch(typeof a){case"boolean":break;case"object":if(Array.isArray(a))s=e(a);else for(var i in s="",a)a[i]&&i&&(s&&(s+=" "),s+=i);break;default:s=a}s&&(t&&(t+=" "),t+=s)}}return t},ke=function(e){var r=ne(e);r.sheet.speedy=function(e){this.isSpeedy=e},r.compat=!0;var t=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var a=ge(t,r.registered,void 0);return function(e,r,t){!function(e,r,t){var n=e.key+"-"+r.name;!1===t&&void 0===e.registered[n]&&(e.registered[n]=r.styles)}(e,r,t);var n=e.key+"-"+r.name;if(void 0===e.inserted[r.name]){var a=r;do{e.insert(r===a?"."+n:"",a,e.sheet,!0),a=a.next}while(void 0!==a)}}(r,a,!1),r.key+"-"+a.name};return{css:t,cx:function(){for(var e=arguments.length,n=new Array(e),a=0;a<e;a++)n[a]=arguments[a];return be(r.registered,t,we(n))},injectGlobal:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var a=ge(t,r.registered);me(r,a)},keyframes:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var a=ge(t,r.registered),s="animation-"+a.name;return me(r,{name:a.name,styles:"@keyframes "+s+"{"+a.styles+"}"}),s},hydrate:function(e){e.forEach((function(e){r.inserted[e]=!0}))},flush:function(){r.registered={},r.inserted={},r.sheet.flush()},sheet:r.sheet,cache:r,getRegisteredStyles:ye.bind(null,r.registered),merge:be.bind(null,r.registered,t)}}({key:"css"}),xe=ke.flush,$e=ke.hydrate,Ae=ke.cx,Ce=ke.merge,Se=ke.getRegisteredStyles,Oe=ke.injectGlobal,Ee=ke.keyframes,_e=ke.css,je=ke.sheet,Re=ke.cache;e.cache=Re,e.css=_e,e.cx=Ae,e.flush=xe,e.getRegisteredStyles=Se,e.hydrate=$e,e.injectGlobal=Oe,e.keyframes=Ee,e.merge=Ce,e.sheet=je,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=emotion-css.umd.min.js.map
