!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?module.exports=r():"function"==typeof define&&define.amd?define(r):(e="undefined"!=typeof globalThis?globalThis:e||self).createEmotion=r()}(this,(function(){"use strict";var e=function(){function e(e){var r=this;this._insertTag=function(e){var t;t=0===r.tags.length?r.insertionPoint?r.insertionPoint.nextSibling:r.prepend?r.container.firstChild:r.before:r.tags[r.tags.length-1].nextSibling,r.container.insertBefore(e,t),r.tags.push(e)},this.isSpeedy=void 0===e.speedy||e.speedy,this.tags=[],this.ctr=0,this.nonce=e.nonce,this.key=e.key,this.container=e.container,this.prepend=e.prepend,this.insertionPoint=e.insertionPoint,this.before=null}var r=e.prototype;return r.hydrate=function(e){e.forEach(this._insertTag)},r.insert=function(e){this.ctr%(this.isSpeedy?65e3:1)==0&&this._insertTag(function(e){var r=document.createElement("style");return r.setAttribute("data-emotion",e.key),void 0!==e.nonce&&r.setAttribute("nonce",e.nonce),r.appendChild(document.createTextNode("")),r.setAttribute("data-s",""),r}(this));var r=this.tags[this.tags.length-1];if(this.isSpeedy){var t=function(e){if(e.sheet)return e.sheet;for(var r=0;r<document.styleSheets.length;r++)if(document.styleSheets[r].ownerNode===e)return document.styleSheets[r]}(r);try{t.insertRule(e,t.cssRules.length)}catch(e){}}else r.appendChild(document.createTextNode(e));this.ctr++},r.flush=function(){this.tags.forEach((function(e){var r;return null==(r=e.parentNode)?void 0:r.removeChild(e)})),this.tags=[],this.ctr=0},e}(),r="-ms-",t="-moz-",n="-webkit-",a="comm",s="rule",i="decl",c="@keyframes",o=Math.abs,u=String.fromCharCode,l=Object.assign;function f(e){return e.trim()}function d(e,r,t){return e.replace(r,t)}function h(e,r){return e.indexOf(r)}function p(e,r){return 0|e.charCodeAt(r)}function v(e,r,t){return e.slice(r,t)}function g(e){return e.length}function m(e){return e.length}function y(e,r){return r.push(e),e}var b=1,w=1,k=0,x=0,$=0,A="";function C(e,r,t,n,a,s,i){return{value:e,root:r,parent:t,type:n,props:a,children:s,line:b,column:w,length:i,return:""}}function S(e,r){return l(C("",null,null,"",null,null,0),e,{length:-e.length},r)}function E(){return $=x>0?p(A,--x):0,w--,10===$&&(w=1,b--),$}function O(){return $=x<k?p(A,x++):0,w++,10===$&&(w=1,b++),$}function _(){return p(A,x)}function j(){return x}function R(e,r){return v(A,e,r)}function N(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function P(e){return b=w=1,k=g(A=e),x=0,[]}function T(e){return A="",e}function G(e){return f(R(x-1,M(91===e?e+2:40===e?e+1:e)))}function z(e){for(;($=_())&&$<33;)O();return N(e)>2||N($)>3?"":" "}function I(e,r){for(;--r&&O()&&!($<48||$>102||$>57&&$<65||$>70&&$<97););return R(e,j()+(r<6&&32==_()&&32==O()))}function M(e){for(;O();)switch($){case e:return x;case 34:case 39:34!==e&&39!==e&&M($);break;case 40:41===e&&M(e);break;case 92:O()}return x}function W(e,r){for(;O()&&e+$!==57&&(e+$!==84||47!==_()););return"/*"+R(r,x-1)+"*"+u(47===e?e:O())}function L(e){for(;!N(_());)O();return R(e,x)}function q(e){return T(D("",null,null,null,[""],e=P(e),0,[0],e))}function D(e,r,t,n,a,s,i,c,o){for(var l=0,f=0,v=i,m=0,b=0,w=0,k=1,x=1,$=1,A=0,C="",S=a,R=s,N=n,P=C;x;)switch(w=A,A=O()){case 40:if(108!=w&&58==p(P,v-1)){-1!=h(P+=d(G(A),"&","&\f"),"&\f")&&($=-1);break}case 34:case 39:case 91:P+=G(A);break;case 9:case 10:case 13:case 32:P+=z(w);break;case 92:P+=I(j()-1,7);continue;case 47:switch(_()){case 42:case 47:y(B(W(O(),j()),r,t),o);break;default:P+="/"}break;case 123*k:c[l++]=g(P)*$;case 125*k:case 59:case 0:switch(A){case 0:case 125:x=0;case 59+f:-1==$&&(P=d(P,/\f/g,"")),b>0&&g(P)-v&&y(b>32?H(P+";",n,t,v-1):H(d(P," ","")+";",n,t,v-2),o);break;case 59:P+=";";default:if(y(N=F(P,r,t,l,f,a,c,C,S=[],R=[],v),s),123===A)if(0===f)D(P,r,N,N,S,s,v,c,R);else switch(99===m&&110===p(P,3)?100:m){case 100:case 108:case 109:case 115:D(e,N,N,n&&y(F(e,N,N,0,0,a,c,C,a,S=[],v),R),a,R,v,c,n?S:R);break;default:D(P,N,N,N,[""],R,0,c,R)}}l=f=b=0,k=$=1,C=P="",v=i;break;case 58:v=1+g(P),b=w;default:if(k<1)if(123==A)--k;else if(125==A&&0==k++&&125==E())continue;switch(P+=u(A),A*k){case 38:$=f>0?1:(P+="\f",-1);break;case 44:c[l++]=(g(P)-1)*$,$=1;break;case 64:45===_()&&(P+=G(O())),m=_(),f=v=g(C=P+=L(j())),A++;break;case 45:45===w&&2==g(P)&&(k=0)}}return s}function F(e,r,t,n,a,i,c,u,l,h,p){for(var g=a-1,y=0===a?i:[""],b=m(y),w=0,k=0,x=0;w<n;++w)for(var $=0,A=v(e,g+1,g=o(k=c[w])),S=e;$<b;++$)(S=f(k>0?y[$]+" "+A:d(A,/&\f/g,y[$])))&&(l[x++]=S);return C(e,r,t,0===a?s:u,l,h,p)}function B(e,r,t){return C(e,r,t,a,u($),v(e,2,-2),0)}function H(e,r,t,n){return C(e,r,t,i,v(e,0,n),v(e,n+1,-1),n)}function Z(e,r){for(var t="",n=m(e),a=0;a<n;a++)t+=r(e[a],a,e,r)||"";return t}function J(e,r,t,n){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case i:return e.return=e.return||e.value;case a:return"";case c:return e.return=e.value+"{"+Z(e.children,n)+"}";case s:e.value=e.props.join(",")}return g(t=Z(e.children,n))?e.return=e.value+"{"+t+"}":""}function K(e){var r=Object.create(null);return function(t){return void 0===r[t]&&(r[t]=e(t)),r[t]}}var Q=function(e,r,t){for(var n=0,a=0;n=a,a=_(),38===n&&12===a&&(r[t]=1),!N(a);)O();return R(e,x)},U=function(e,r){return T(function(e,r){var t=-1,n=44;do{switch(N(n)){case 0:38===n&&12===_()&&(r[t]=1),e[t]+=Q(x-1,r,t);break;case 2:e[t]+=G(n);break;case 4:if(44===n){e[++t]=58===_()?"&\f":"",r[t]=e[t].length;break}default:e[t]+=u(n)}}while(n=O());return e}(P(e),r))},V=new WeakMap,X=function(e){if("rule"===e.type&&e.parent&&!(e.length<1)){for(var r=e.value,t=e.parent,n=e.column===t.column&&e.line===t.line;"rule"!==t.type;)if(!(t=t.parent))return;if((1!==e.props.length||58===r.charCodeAt(0)||V.get(t))&&!n){V.set(e,!0);for(var a=[],s=U(r,a),i=t.props,c=0,o=0;c<s.length;c++)for(var u=0;u<i.length;u++,o++)e.props[o]=a[c]?s[c].replace(/&\f/g,i[u]):i[u]+" "+s[c]}}},Y=function(e){if("decl"===e.type){var r=e.value;108===r.charCodeAt(0)&&98===r.charCodeAt(2)&&(e.return="",e.value="")}};function ee(e,a){switch(function(e,r){return 45^p(e,0)?(((r<<2^p(e,0))<<2^p(e,1))<<2^p(e,2))<<2^p(e,3):0}(e,a)){case 5103:return n+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return n+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return n+e+t+e+r+e+e;case 6828:case 4268:return n+e+r+e+e;case 6165:return n+e+r+"flex-"+e+e;case 5187:return n+e+d(e,/(\w+).+(:[^]+)/,n+"box-$1$2"+r+"flex-$1$2")+e;case 5443:return n+e+r+"flex-item-"+d(e,/flex-|-self/,"")+e;case 4675:return n+e+r+"flex-line-pack"+d(e,/align-content|flex-|-self/,"")+e;case 5548:return n+e+r+d(e,"shrink","negative")+e;case 5292:return n+e+r+d(e,"basis","preferred-size")+e;case 6060:return n+"box-"+d(e,"-grow","")+n+e+r+d(e,"grow","positive")+e;case 4554:return n+d(e,/([^-])(transform)/g,"$1"+n+"$2")+e;case 6187:return d(d(d(e,/(zoom-|grab)/,n+"$1"),/(image-set)/,n+"$1"),e,"")+e;case 5495:case 3959:return d(e,/(image-set\([^]*)/,n+"$1$`$1");case 4968:return d(d(e,/(.+:)(flex-)?(.*)/,n+"box-pack:$3"+r+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+n+e+e;case 4095:case 3583:case 4068:case 2532:return d(e,/(.+)-inline(.+)/,n+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(g(e)-1-a>6)switch(p(e,a+1)){case 109:if(45!==p(e,a+4))break;case 102:return d(e,/(.+:)(.+)-([^]+)/,"$1"+n+"$2-$3$1"+t+(108==p(e,a+3)?"$3":"$2-$3"))+e;case 115:return~h(e,"stretch")?ee(d(e,"stretch","fill-available"),a)+e:e}break;case 4949:if(115!==p(e,a+1))break;case 6444:switch(p(e,g(e)-3-(~h(e,"!important")&&10))){case 107:return d(e,":",":"+n)+e;case 101:return d(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+n+(45===p(e,14)?"inline-":"")+"box$3$1"+n+"$2$3$1"+r+"$2box$3")+e}break;case 5936:switch(p(e,a+11)){case 114:return n+e+r+d(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return n+e+r+d(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return n+e+r+d(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return n+e+r+e+e}return e}var re=[function(e,a,o,u){if(e.length>-1&&!e.return)switch(e.type){case i:e.return=ee(e.value,e.length);break;case c:return Z([S(e,{value:d(e.value,"@","@"+n)})],u);case s:if(e.length)return function(e,r){return e.map(r).join("")}(e.props,(function(a){switch(function(e,r){return(e=r.exec(e))?e[0]:e}(a,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return Z([S(e,{props:[d(a,/:(read-\w+)/,":"+t+"$1")]})],u);case"::placeholder":return Z([S(e,{props:[d(a,/:(plac\w+)/,":"+n+"input-$1")]}),S(e,{props:[d(a,/:(plac\w+)/,":"+t+"$1")]}),S(e,{props:[d(a,/:(plac\w+)/,r+"input-$1")]})],u)}return""}))}}],te=function(r){var t=r.key;if("css"===t){var n=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(n,(function(e){-1!==e.getAttribute("data-emotion").indexOf(" ")&&(document.head.appendChild(e),e.setAttribute("data-s",""))}))}var a,s,i=r.stylisPlugins||re,c={},o=[];a=r.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+t+' "]'),(function(e){for(var r=e.getAttribute("data-emotion").split(" "),t=1;t<r.length;t++)c[r[t]]=!0;o.push(e)}));var u,l,f=[J,(l=function(e){u.insert(e)},function(e){e.root||(e=e.return)&&l(e)})],d=function(e){var r=m(e);return function(t,n,a,s){for(var i="",c=0;c<r;c++)i+=e[c](t,n,a,s)||"";return i}}([X,Y].concat(i,f));s=function(e,r,t,n){u=t,Z(q(e?e+"{"+r.styles+"}":r.styles),d),n&&(h.inserted[r.name]=!0)};var h={key:t,sheet:new e({key:t,container:a,nonce:r.nonce,speedy:r.speedy,prepend:r.prepend,insertionPoint:r.insertionPoint}),nonce:r.nonce,inserted:c,registered:{},insert:s};return h.sheet.hydrate(o),h};var ne={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},ae=!1,se=/[A-Z]|^ms/g,ie=/_EMO_([^_]+?)_([^]*?)_EMO_/g,ce=function(e){return 45===e.charCodeAt(1)},oe=function(e){return null!=e&&"boolean"!=typeof e},ue=K((function(e){return ce(e)?e:e.replace(se,"-$&").toLowerCase()})),le=function(e,r){switch(e){case"animation":case"animationName":if("string"==typeof r)return r.replace(ie,(function(e,r,t){return he={name:r,styles:t,next:he},r}))}return 1===ne[e]||ce(e)||"number"!=typeof r||0===r?r:r+"px"},fe="Component selectors can only be used in conjunction with @emotion/babel-plugin, the swc Emotion plugin, or another Emotion-aware compiler transform.";function de(e,r,t){if(null==t)return"";var n=t;if(void 0!==n.__emotion_styles)return n;switch(typeof t){case"boolean":return"";case"object":var a=t;if(1===a.anim)return he={name:a.name,styles:a.styles,next:he},a.name;var s=t;if(void 0!==s.styles){var i=s.next;if(void 0!==i)for(;void 0!==i;)he={name:i.name,styles:i.styles,next:he},i=i.next;return s.styles+";"}return function(e,r,t){var n="";if(Array.isArray(t))for(var a=0;a<t.length;a++)n+=de(e,r,t[a])+";";else for(var s in t){var i=t[s];if("object"!=typeof i){var c=i;null!=r&&void 0!==r[c]?n+=s+"{"+r[c]+"}":oe(c)&&(n+=ue(s)+":"+le(s,c)+";")}else{if("NO_COMPONENT_SELECTOR"===s&&ae)throw new Error(fe);if(!Array.isArray(i)||"string"!=typeof i[0]||null!=r&&void 0!==r[i[0]]){var o=de(e,r,i);switch(s){case"animation":case"animationName":n+=ue(s)+":"+o+";";break;default:n+=s+"{"+o+"}"}}else for(var u=0;u<i.length;u++)oe(i[u])&&(n+=ue(s)+":"+le(s,i[u])+";")}}return n}(e,r,t);case"function":if(void 0!==e){var c=he,o=t(e);return he=c,de(e,r,o)}}var u=t;if(null==r)return u;var l=r[u];return void 0!==l?l:u}var he,pe=/label:\s*([^\s;{]+)\s*(;|$)/g;function ve(e,r,t){if(1===e.length&&"object"==typeof e[0]&&null!==e[0]&&void 0!==e[0].styles)return e[0];var n=!0,a="";he=void 0;var s=e[0];null==s||void 0===s.raw?(n=!1,a+=de(t,r,s)):a+=s[0];for(var i=1;i<e.length;i++){if(a+=de(t,r,e[i]),n)a+=s[i]}pe.lastIndex=0;for(var c,o="";null!==(c=pe.exec(a));)o+="-"+c[1];var u=function(e){for(var r,t=0,n=0,a=e.length;a>=4;++n,a-=4)r=1540483477*(65535&(r=255&e.charCodeAt(n)|(255&e.charCodeAt(++n))<<8|(255&e.charCodeAt(++n))<<16|(255&e.charCodeAt(++n))<<24))+(59797*(r>>>16)<<16),t=1540483477*(65535&(r^=r>>>24))+(59797*(r>>>16)<<16)^1540483477*(65535&t)+(59797*(t>>>16)<<16);switch(a){case 3:t^=(255&e.charCodeAt(n+2))<<16;case 2:t^=(255&e.charCodeAt(n+1))<<8;case 1:t=1540483477*(65535&(t^=255&e.charCodeAt(n)))+(59797*(t>>>16)<<16)}return(((t=1540483477*(65535&(t^=t>>>13))+(59797*(t>>>16)<<16))^t>>>15)>>>0).toString(36)}(a)+o;return{name:u,styles:a,next:he}}function ge(e,r,t){var n="";return t.split(" ").forEach((function(t){void 0!==e[t]?r.push(e[t]+";"):t&&(n+=t+" ")})),n}function me(e,r){if(void 0===e.inserted[r.name])return e.insert("",r,e.sheet,!0)}function ye(e,r,t){var n=[],a=ge(e,n,t);return n.length<2?t:a+r(n)}var be=function e(r){for(var t="",n=0;n<r.length;n++){var a=r[n];if(null!=a){var s=void 0;switch(typeof a){case"boolean":break;case"object":if(Array.isArray(a))s=e(a);else for(var i in s="",a)a[i]&&i&&(s&&(s+=" "),s+=i);break;default:s=a}s&&(t&&(t+=" "),t+=s)}}return t};return function(e){var r=te(e);r.sheet.speedy=function(e){this.isSpeedy=e},r.compat=!0;var t=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var a=ve(t,r.registered,void 0);return function(e,r,t){!function(e,r,t){var n=e.key+"-"+r.name;!1===t&&void 0===e.registered[n]&&(e.registered[n]=r.styles)}(e,r,t);var n=e.key+"-"+r.name;if(void 0===e.inserted[r.name]){var a=r;do{e.insert(r===a?"."+n:"",a,e.sheet,!0),a=a.next}while(void 0!==a)}}(r,a,!1),r.key+"-"+a.name};return{css:t,cx:function(){for(var e=arguments.length,n=new Array(e),a=0;a<e;a++)n[a]=arguments[a];return ye(r.registered,t,be(n))},injectGlobal:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var a=ve(t,r.registered);me(r,a)},keyframes:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var a=ve(t,r.registered),s="animation-"+a.name;return me(r,{name:a.name,styles:"@keyframes "+s+"{"+a.styles+"}"}),s},hydrate:function(e){e.forEach((function(e){r.inserted[e]=!0}))},flush:function(){r.registered={},r.inserted={},r.sheet.flush()},sheet:r.sheet,cache:r,getRegisteredStyles:ge.bind(null,r.registered),merge:ye.bind(null,r.registered,t)}}}));
//# sourceMappingURL=emotion-css-create-instance.umd.min.js.map
