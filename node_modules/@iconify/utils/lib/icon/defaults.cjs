'use strict';

const defaultIconDimensions = Object.freeze(
  {
    left: 0,
    top: 0,
    width: 16,
    height: 16
  }
);
const defaultIconTransformations = Object.freeze({
  rotate: 0,
  vFlip: false,
  hFlip: false
});
const defaultIconProps = Object.freeze({
  ...defaultIconDimensions,
  ...defaultIconTransformations
});
const defaultExtendedIconProps = Object.freeze({
  ...defaultIconProps,
  body: "",
  hidden: false
});

exports.defaultExtendedIconProps = defaultExtendedIconProps;
exports.defaultIconDimensions = defaultIconDimensions;
exports.defaultIconProps = defaultIconProps;
exports.defaultIconTransformations = defaultIconTransformations;
