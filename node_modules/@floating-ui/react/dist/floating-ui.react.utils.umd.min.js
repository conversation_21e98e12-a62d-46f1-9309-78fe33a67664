!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).FloatingUIReactUtils={},t.React)}(this,(function(t,e){"use strict";function n(t){var e=Object.create(null);return t&&Object.keys(t).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(t,n);Object.defineProperty(e,n,r.get?r:{enumerable:!0,get:function(){return t[n]}})}})),e.default=t,Object.freeze(e)}var r=n(e);function o(){return"undefined"!=typeof window}function i(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function a(){const t=navigator.userAgentData;return null!=t&&t.platform?t.platform:navigator.platform}function u(){const t=navigator.userAgentData;return t&&Array.isArray(t.brands)?t.brands.map((t=>{let{brand:e,version:n}=t;return e+"/"+n})).join(" "):navigator.userAgent}function d(){const t=/android/i;return t.test(a())||t.test(u())}function c(){return u().includes("jsdom/")}const l="data-floating-ui-focusable",s="ArrowLeft",f="ArrowRight";function p(t){let e=t.activeElement;for(;null!=(null==(n=e)||null==(n=n.shadowRoot)?void 0:n.activeElement);){var n;e=e.shadowRoot.activeElement}return e}function h(t,e){if(!t||!e)return!1;const n=null==e.getRootNode?void 0:e.getRootNode();if(t.contains(e))return!0;if(n&&(r=n,o()&&"undefined"!=typeof ShadowRoot&&(r instanceof ShadowRoot||r instanceof i(r).ShadowRoot))){let n=e;for(;n;){if(t===n)return!0;n=n.parentNode||n.host}}var r;return!1}function g(t){return(null==t?void 0:t.ownerDocument)||document}function b(t){return e=t,!!o()&&(e instanceof HTMLElement||e instanceof i(e).HTMLElement)&&t.matches("input:not([type='hidden']):not([disabled]),[contenteditable]:not([contenteditable='false']),textarea:not([disabled])");var e}function v(t,e,n){void 0===n&&(n=!0);return t.filter((t=>{var r;return t.parentId===e&&(!n||(null==(r=t.context)?void 0:r.open))})).flatMap((e=>[e,...v(t,e.id,n)]))}function m(t){t.preventDefault(),t.stopPropagation()}var y="undefined"!=typeof document?e.useLayoutEffect:function(){};const I={...r}.useInsertionEffect||(t=>t());const w=Math.floor;function x(t,e,n){return Math.floor(t/e)!==n}function E(t,e){return e<0||e>=t.current.length}function S(t,e){let{startingIndex:n=-1,decrement:r=!1,disabledIndices:o,amount:i=1}=void 0===e?{}:e,a=n;do{a+=r?-i:i}while(a>=0&&a<=t.current.length-1&&A(t,a,o));return a}function A(t,e,n){if("function"==typeof n)return n(e);if(n)return n.includes(e);const r=t.current[e];return null==r||r.hasAttribute("disabled")||"true"===r.getAttribute("aria-disabled")}
/*!
  * tabbable 6.2.0
  * @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
  */var R=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"].join(","),T="undefined"==typeof Element,N=T?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,D=!T&&Element.prototype.getRootNode?function(t){var e;return null==t||null===(e=t.getRootNode)||void 0===e?void 0:e.call(t)}:function(t){return null==t?void 0:t.ownerDocument},O=function t(e,n){var r;void 0===n&&(n=!0);var o=null==e||null===(r=e.getAttribute)||void 0===r?void 0:r.call(e,"inert");return""===o||"true"===o||n&&e&&t(e.parentNode)},C=function t(e,n,r){for(var o=[],i=Array.from(e);i.length;){var a=i.shift();if(!O(a,!1))if("SLOT"===a.tagName){var u=a.assignedElements(),d=t(u.length?u:a.children,!0,r);r.flatten?o.push.apply(o,d):o.push({scopeParent:a,candidates:d})}else{N.call(a,R)&&r.filter(a)&&(n||!e.includes(a))&&o.push(a);var c=a.shadowRoot||"function"==typeof r.getShadowRoot&&r.getShadowRoot(a),l=!O(c,!1)&&(!r.shadowRootFilter||r.shadowRootFilter(a));if(c&&l){var s=t(!0===c?a.children:c.children,!0,r);r.flatten?o.push.apply(o,s):o.push({scopeParent:a,candidates:s})}else i.unshift.apply(i,a.children)}}return o},L=function(t){return!isNaN(parseInt(t.getAttribute("tabindex"),10))},P=function(t){if(!t)throw new Error("No node provided");return t.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(t.tagName)||function(t){var e,n=null==t||null===(e=t.getAttribute)||void 0===e?void 0:e.call(t,"contenteditable");return""===n||"true"===n}(t))&&!L(t)?0:t.tabIndex},M=function(t,e){return t.tabIndex===e.tabIndex?t.documentOrder-e.documentOrder:t.tabIndex-e.tabIndex},k=function(t){return"INPUT"===t.tagName},F=function(t){return function(t){return k(t)&&"radio"===t.type}(t)&&!function(t){if(!t.name)return!0;var e,n=t.form||D(t),r=function(t){return n.querySelectorAll('input[type="radio"][name="'+t+'"]')};if("undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)e=r(window.CSS.escape(t.name));else try{e=r(t.name)}catch(t){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",t.message),!1}var o=function(t,e){for(var n=0;n<t.length;n++)if(t[n].checked&&t[n].form===e)return t[n]}(e,t.form);return!o||o===t}(t)},j=function(t){var e=t.getBoundingClientRect(),n=e.width,r=e.height;return 0===n&&0===r},U=function(t,e){var n=e.displayCheck,r=e.getShadowRoot;if("hidden"===getComputedStyle(t).visibility)return!0;var o=N.call(t,"details>summary:first-of-type")?t.parentElement:t;if(N.call(o,"details:not([open]) *"))return!0;if(n&&"full"!==n&&"legacy-full"!==n){if("non-zero-area"===n)return j(t)}else{if("function"==typeof r){for(var i=t;t;){var a=t.parentElement,u=D(t);if(a&&!a.shadowRoot&&!0===r(a))return j(t);t=t.assignedSlot?t.assignedSlot:a||u===t.ownerDocument?a:u.host}t=i}if(function(t){var e,n,r,o,i=t&&D(t),a=null===(e=i)||void 0===e?void 0:e.host,u=!1;if(i&&i!==t)for(u=!!(null!==(n=a)&&void 0!==n&&null!==(r=n.ownerDocument)&&void 0!==r&&r.contains(a)||null!=t&&null!==(o=t.ownerDocument)&&void 0!==o&&o.contains(t));!u&&a;){var d,c,l;u=!(null===(c=a=null===(d=i=D(a))||void 0===d?void 0:d.host)||void 0===c||null===(l=c.ownerDocument)||void 0===l||!l.contains(a))}return u}(t))return!t.getClientRects().length;if("legacy-full"!==n)return!0}return!1},G=function(t,e){return!(e.disabled||O(e)||function(t){return k(t)&&"hidden"===t.type}(e)||U(e,t)||function(t){return"DETAILS"===t.tagName&&Array.prototype.slice.apply(t.children).some((function(t){return"SUMMARY"===t.tagName}))}(e)||function(t){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(t.tagName))for(var e=t.parentElement;e;){if("FIELDSET"===e.tagName&&e.disabled){for(var n=0;n<e.children.length;n++){var r=e.children.item(n);if("LEGEND"===r.tagName)return!!N.call(e,"fieldset[disabled] *")||!r.contains(t)}return!0}e=e.parentElement}return!1}(e))},q=function(t,e){return!(F(e)||P(e)<0||!G(t,e))},z=function(t){var e=parseInt(t.getAttribute("tabindex"),10);return!!(isNaN(e)||e>=0)},V=function t(e){var n=[],r=[];return e.forEach((function(e,o){var i=!!e.scopeParent,a=i?e.scopeParent:e,u=function(t,e){var n=P(t);return n<0&&e&&!L(t)?0:n}(a,i),d=i?t(e.candidates):a;0===u?i?n.push.apply(n,d):n.push(a):r.push({documentOrder:o,tabIndex:u,item:e,isScope:i,content:d})})),r.sort(M).reduce((function(t,e){return e.isScope?t.push.apply(t,e.content):t.push(e.content),t}),[]).concat(n)},B=function(t,e){var n;return n=(e=e||{}).getShadowRoot?C([t],e.includeContainer,{filter:q.bind(null,e),flatten:!1,getShadowRoot:e.getShadowRoot,shadowRootFilter:z}):function(t,e,n){if(O(t))return[];var r=Array.prototype.slice.apply(t.querySelectorAll(R));return e&&N.call(t,R)&&r.unshift(t),r.filter(n)}(t,e.includeContainer,q.bind(null,e)),V(n)};const H=()=>({getShadowRoot:!0,displayCheck:"function"==typeof ResizeObserver&&ResizeObserver.toString().includes("[native code]")?"full":"none"});function W(t,e){const n=B(t,H()),r=n.length;if(0===r)return;const o=p(g(t)),i=n.indexOf(o);return n[-1===i?1===e?0:r-1:i+e]}t.activeElement=p,t.contains=h,t.createGridCellMap=function(t,e,n){const r=[];let o=0;return t.forEach(((t,i)=>{let{width:a,height:u}=t,d=!1;for(n&&(o=0);!d;){const t=[];for(let n=0;n<a;n++)for(let r=0;r<u;r++)t.push(o+n+r*e);o%e+a<=e&&t.every((t=>null==r[t]))?(t.forEach((t=>{r[t]=i})),d=!0):o++}})),[...r]},t.disableFocusInside=function(t){B(t,H()).forEach((t=>{t.dataset.tabindex=t.getAttribute("tabindex")||"",t.setAttribute("tabindex","-1")}))},t.enableFocusInside=function(t){t.querySelectorAll("[data-tabindex]").forEach((t=>{const e=t.dataset.tabindex;delete t.dataset.tabindex,e?t.setAttribute("tabindex",e):t.removeAttribute("tabindex")}))},t.findNonDisabledListIndex=S,t.getDeepestNode=function(t,e){let n,r=-1;return function e(o,i){i>r&&(n=o,r=i),v(t,o).forEach((t=>{e(t.id,i+1)}))}(e,0),t.find((t=>t.id===n))},t.getDocument=g,t.getFloatingFocusElement=function(t){return t?t.hasAttribute(l)?t:t.querySelector("["+l+"]")||t:null},t.getGridCellIndexOfCorner=function(t,e,n,r,o){if(-1===t)return-1;const i=n.indexOf(t),a=e[t];switch(o){case"tl":return i;case"tr":return a?i+a.width-1:i;case"bl":return a?i+(a.height-1)*r:i;case"br":return n.lastIndexOf(t)}},t.getGridCellIndices=function(t,e){return e.flatMap(((e,n)=>t.includes(e)?[n]:[]))},t.getGridNavigatedIndex=function(t,e){let{event:n,orientation:r,loop:o,rtl:i,cols:a,disabledIndices:u,minIndex:d,maxIndex:c,prevIndex:l,stopEvent:p=!1}=e,h=l;if("ArrowUp"===n.key){if(p&&m(n),-1===l)h=c;else if(h=S(t,{startingIndex:h,amount:a,decrement:!0,disabledIndices:u}),o&&(l-a<d||h<0)){const t=l%a,e=c%a,n=c-(e-t);h=e===t?c:e>t?n:n-a}E(t,h)&&(h=l)}if("ArrowDown"===n.key&&(p&&m(n),-1===l?h=d:(h=S(t,{startingIndex:l,amount:a,disabledIndices:u}),o&&l+a>c&&(h=S(t,{startingIndex:l%a-a,amount:a,disabledIndices:u}))),E(t,h)&&(h=l)),"both"===r){const e=w(l/a);n.key===(i?s:f)&&(p&&m(n),l%a!=a-1?(h=S(t,{startingIndex:l,disabledIndices:u}),o&&x(h,a,e)&&(h=S(t,{startingIndex:l-l%a-1,disabledIndices:u}))):o&&(h=S(t,{startingIndex:l-l%a-1,disabledIndices:u})),x(h,a,e)&&(h=l)),n.key===(i?f:s)&&(p&&m(n),l%a!=0?(h=S(t,{startingIndex:l,decrement:!0,disabledIndices:u}),o&&x(h,a,e)&&(h=S(t,{startingIndex:l+(a-l%a),decrement:!0,disabledIndices:u}))):o&&(h=S(t,{startingIndex:l+(a-l%a),decrement:!0,disabledIndices:u})),x(h,a,e)&&(h=l));const r=w(c/a)===e;E(t,h)&&(h=o&&r?n.key===(i?f:s)?c:S(t,{startingIndex:l-l%a-1,disabledIndices:u}):l)}return h},t.getMaxListIndex=function(t,e){return S(t,{decrement:!0,startingIndex:t.current.length,disabledIndices:e})},t.getMinListIndex=function(t,e){return S(t,{disabledIndices:e})},t.getNextTabbable=function(t){return W(g(t).body,1)||t},t.getNodeAncestors=function(t,e){var n;let r=[],o=null==(n=t.find((t=>t.id===e)))?void 0:n.parentId;for(;o;){const e=t.find((t=>t.id===o));o=null==e?void 0:e.parentId,e&&(r=r.concat(e))}return r},t.getNodeChildren=v,t.getPlatform=a,t.getPreviousTabbable=function(t){return W(g(t).body,-1)||t},t.getTabbableOptions=H,t.getTarget=function(t){return"composedPath"in t?t.composedPath()[0]:t.target},t.getUserAgent=u,t.isAndroid=d,t.isDifferentGridRow=x,t.isEventTargetWithin=function(t,e){if(null==e)return!1;if("composedPath"in t)return t.composedPath().includes(e);const n=t;return null!=n.target&&e.contains(n.target)},t.isIndexOutOfListBounds=E,t.isJSDOM=c,t.isListIndexDisabled=A,t.isMac=function(){return a().toLowerCase().startsWith("mac")&&!navigator.maxTouchPoints},t.isMouseLikePointerType=function(t,e){const n=["mouse","pen"];return e||n.push("",void 0),n.includes(t)},t.isOutsideEvent=function(t,e){const n=e||t.currentTarget,r=t.relatedTarget;return!r||!h(n,r)},t.isReactEvent=function(t){return"nativeEvent"in t},t.isRootElement=function(t){return t.matches("html,body")},t.isSafari=function(){return/apple/i.test(navigator.vendor)},t.isTypeableCombobox=function(t){return!!t&&("combobox"===t.getAttribute("role")&&b(t))},t.isTypeableElement=b,t.isVirtualClick=function(t){return!(0!==t.mozInputSource||!t.isTrusted)||(d()&&t.pointerType?"click"===t.type&&1===t.buttons:0===t.detail&&!t.pointerType)},t.isVirtualPointerEvent=function(t){return!c()&&(!d()&&0===t.width&&0===t.height||d()&&1===t.width&&1===t.height&&0===t.pressure&&0===t.detail&&"mouse"===t.pointerType||t.width<1&&t.height<1&&0===t.pressure&&0===t.detail&&"touch"===t.pointerType)},t.matchesFocusVisible=function(t){if(!t||c())return!0;try{return t.matches(":focus-visible")}catch(t){return!0}},t.stopEvent=m,t.useEffectEvent=function(t){const e=r.useRef((()=>{}));return I((()=>{e.current=t})),r.useCallback((function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return null==e.current?void 0:e.current(...n)}),[])},t.useLatestRef=function(t){const e=r.useRef(t);return y((()=>{e.current=t})),e},t.useModernLayoutEffect=y}));
