!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-dom"),require("@floating-ui/react-dom")):"function"==typeof define&&define.amd?define(["exports","react","react-dom","@floating-ui/react-dom"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).FloatingUIReact={},e.<PERSON><PERSON>,e.ReactDOM,e.FloatingUIReactDOM)}(this,(function(e,t,n,r){"use strict";function o(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var u=o(t),i=o(n);function l(){return"undefined"!=typeof window}function c(e){return a(e)?(e.nodeName||"").toLowerCase():"#document"}function s(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function a(e){return!!l()&&(e instanceof Node||e instanceof s(e).Node)}function f(e){return!!l()&&(e instanceof Element||e instanceof s(e).Element)}function d(e){return!!l()&&(e instanceof HTMLElement||e instanceof s(e).HTMLElement)}function m(e){return!(!l()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof s(e).ShadowRoot)}const v=new Set(["html","body","#document"]);function p(e){return v.has(c(e))}function g(e){return s(e).getComputedStyle(e)}function h(e){if("html"===c(e))return e;const t=e.assignedSlot||e.parentNode||m(e)&&e.host||function(e){var t;return null==(t=(a(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}(e);return m(t)?t.host:t}const y=Math.min,b=Math.max,w=Math.round,E=Math.floor;
/*!
  * tabbable 6.2.0
  * @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
  */
var R=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"].join(","),x="undefined"==typeof Element,I=x?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,k=!x&&Element.prototype.getRootNode?function(e){var t;return null==e||null===(t=e.getRootNode)||void 0===t?void 0:t.call(e)}:function(e){return null==e?void 0:e.ownerDocument},C=function e(t,n){var r;void 0===n&&(n=!0);var o=null==t||null===(r=t.getAttribute)||void 0===r?void 0:r.call(t,"inert");return""===o||"true"===o||n&&t&&e(t.parentNode)},M=function(e,t,n){if(C(e))return[];var r=Array.prototype.slice.apply(e.querySelectorAll(R));return t&&I.call(e,R)&&r.unshift(e),r=r.filter(n)},O=function e(t,n,r){for(var o=[],u=Array.from(t);u.length;){var i=u.shift();if(!C(i,!1))if("SLOT"===i.tagName){var l=i.assignedElements(),c=e(l.length?l:i.children,!0,r);r.flatten?o.push.apply(o,c):o.push({scopeParent:i,candidates:c})}else{I.call(i,R)&&r.filter(i)&&(n||!t.includes(i))&&o.push(i);var s=i.shadowRoot||"function"==typeof r.getShadowRoot&&r.getShadowRoot(i),a=!C(s,!1)&&(!r.shadowRootFilter||r.shadowRootFilter(i));if(s&&a){var f=e(!0===s?i.children:s.children,!0,r);r.flatten?o.push.apply(o,f):o.push({scopeParent:i,candidates:f})}else u.unshift.apply(u,i.children)}}return o},S=function(e){return!isNaN(parseInt(e.getAttribute("tabindex"),10))},P=function(e){if(!e)throw new Error("No node provided");return e.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||function(e){var t,n=null==e||null===(t=e.getAttribute)||void 0===t?void 0:t.call(e,"contenteditable");return""===n||"true"===n}(e))&&!S(e)?0:e.tabIndex},T=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},A=function(e){return"INPUT"===e.tagName},L=function(e){return function(e){return A(e)&&"radio"===e.type}(e)&&!function(e){if(!e.name)return!0;var t,n=e.form||k(e),r=function(e){return n.querySelectorAll('input[type="radio"][name="'+e+'"]')};if("undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)t=r(window.CSS.escape(e.name));else try{t=r(e.name)}catch(e){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",e.message),!1}var o=function(e,t){for(var n=0;n<e.length;n++)if(e[n].checked&&e[n].form===t)return e[n]}(t,e.form);return!o||o===e}(e)},N=function(e){var t=e.getBoundingClientRect(),n=t.width,r=t.height;return 0===n&&0===r},D=function(e,t){var n=t.displayCheck,r=t.getShadowRoot;if("hidden"===getComputedStyle(e).visibility)return!0;var o=I.call(e,"details>summary:first-of-type")?e.parentElement:e;if(I.call(o,"details:not([open]) *"))return!0;if(n&&"full"!==n&&"legacy-full"!==n){if("non-zero-area"===n)return N(e)}else{if("function"==typeof r){for(var u=e;e;){var i=e.parentElement,l=k(e);if(i&&!i.shadowRoot&&!0===r(i))return N(e);e=e.assignedSlot?e.assignedSlot:i||l===e.ownerDocument?i:l.host}e=u}if(function(e){var t,n,r,o,u=e&&k(e),i=null===(t=u)||void 0===t?void 0:t.host,l=!1;if(u&&u!==e)for(l=!!(null!==(n=i)&&void 0!==n&&null!==(r=n.ownerDocument)&&void 0!==r&&r.contains(i)||null!=e&&null!==(o=e.ownerDocument)&&void 0!==o&&o.contains(e));!l&&i;){var c,s,a;l=!(null===(s=i=null===(c=u=k(i))||void 0===c?void 0:c.host)||void 0===s||null===(a=s.ownerDocument)||void 0===a||!a.contains(i))}return l}(e))return!e.getClientRects().length;if("legacy-full"!==n)return!0}return!1},F=function(e,t){return!(t.disabled||C(t)||function(e){return A(e)&&"hidden"===e.type}(t)||D(t,e)||function(e){return"DETAILS"===e.tagName&&Array.prototype.slice.apply(e.children).some((function(e){return"SUMMARY"===e.tagName}))}(t)||function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if("FIELDSET"===t.tagName&&t.disabled){for(var n=0;n<t.children.length;n++){var r=t.children.item(n);if("LEGEND"===r.tagName)return!!I.call(t,"fieldset[disabled] *")||!r.contains(e)}return!0}t=t.parentElement}return!1}(t))},j=function(e,t){return!(L(t)||P(t)<0||!F(e,t))},K=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!(isNaN(t)||t>=0)},H=function e(t){var n=[],r=[];return t.forEach((function(t,o){var u=!!t.scopeParent,i=u?t.scopeParent:t,l=function(e,t){var n=P(e);return n<0&&t&&!S(e)?0:n}(i,u),c=u?e(t.candidates):i;0===l?u?n.push.apply(n,c):n.push(i):r.push({documentOrder:o,tabIndex:l,item:t,isScope:u,content:c})})),r.sort(T).reduce((function(e,t){return t.isScope?e.push.apply(e,t.content):e.push(t.content),e}),[]).concat(n)},W=function(e,t){var n;return n=(t=t||{}).getShadowRoot?O([e],t.includeContainer,{filter:j.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:K}):M(e,t.includeContainer,j.bind(null,t)),H(n)},q=function(e,t){if(t=t||{},!e)throw new Error("No node provided");return!1!==I.call(e,R)&&j(t,e)};function _(){const e=navigator.userAgentData;return null!=e&&e.platform?e.platform:navigator.platform}function B(){const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map((e=>{let{brand:t,version:n}=e;return t+"/"+n})).join(" "):navigator.userAgent}function U(){return/apple/i.test(navigator.vendor)}function z(){const e=/android/i;return e.test(_())||e.test(B())}function X(){return B().includes("jsdom/")}const Y="data-floating-ui-focusable",V="ArrowLeft",G="ArrowRight";function Z(e){let t=e.activeElement;for(;null!=(null==(n=t)||null==(n=n.shadowRoot)?void 0:n.activeElement);){var n;t=t.shadowRoot.activeElement}return t}function $(e,t){if(!e||!t)return!1;const n=null==t.getRootNode?void 0:t.getRootNode();if(e.contains(t))return!0;if(n&&m(n)){let n=t;for(;n;){if(e===n)return!0;n=n.parentNode||n.host}}return!1}function Q(e){return"composedPath"in e?e.composedPath()[0]:e.target}function J(e,t){if(null==t)return!1;if("composedPath"in e)return e.composedPath().includes(t);const n=e;return null!=n.target&&t.contains(n.target)}function ee(e){return(null==e?void 0:e.ownerDocument)||document}function te(e){return d(e)&&e.matches("input:not([type='hidden']):not([disabled]),[contenteditable]:not([contenteditable='false']),textarea:not([disabled])")}function ne(e){return!!e&&("combobox"===e.getAttribute("role")&&te(e))}function re(e){return e?e.hasAttribute(Y)?e:e.querySelector("["+Y+"]")||e:null}function oe(e,t,n){void 0===n&&(n=!0);return e.filter((e=>{var r;return e.parentId===t&&(!n||(null==(r=e.context)?void 0:r.open))})).flatMap((t=>[t,...oe(e,t.id,n)]))}function ue(e,t){var n;let r=[],o=null==(n=e.find((e=>e.id===t)))?void 0:n.parentId;for(;o;){const t=e.find((e=>e.id===o));o=null==t?void 0:t.parentId,t&&(r=r.concat(t))}return r}function ie(e){e.preventDefault(),e.stopPropagation()}function le(e){return!(0!==e.mozInputSource||!e.isTrusted)||(z()&&e.pointerType?"click"===e.type&&1===e.buttons:0===e.detail&&!e.pointerType)}function ce(e){return!X()&&(!z()&&0===e.width&&0===e.height||z()&&1===e.width&&1===e.height&&0===e.pressure&&0===e.detail&&"mouse"===e.pointerType||e.width<1&&e.height<1&&0===e.pressure&&0===e.detail&&"touch"===e.pointerType)}function se(e,t){const n=["mouse","pen"];return t||n.push("",void 0),n.includes(e)}var ae="undefined"!=typeof document?t.useLayoutEffect:function(){};function fe(e){const t=u.useRef(e);return ae((()=>{t.current=e})),t}const de={...u}.useInsertionEffect||(e=>e());function me(e){const t=u.useRef((()=>{if("production"!==process.env.NODE_ENV)throw new Error("Cannot call an event handler while rendering.")}));return de((()=>{t.current=e})),u.useCallback((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)}),[])}function ve(e,t,n){return Math.floor(e/t)!==n}function pe(e,t){return t<0||t>=e.current.length}function ge(e,t){return ye(e,{disabledIndices:t})}function he(e,t){return ye(e,{decrement:!0,startingIndex:e.current.length,disabledIndices:t})}function ye(e,t){let{startingIndex:n=-1,decrement:r=!1,disabledIndices:o,amount:u=1}=void 0===t?{}:t,i=n;do{i+=r?-u:u}while(i>=0&&i<=e.current.length-1&&xe(e,i,o));return i}function be(e,t){let{event:n,orientation:r,loop:o,rtl:u,cols:i,disabledIndices:l,minIndex:c,maxIndex:s,prevIndex:a,stopEvent:f=!1}=t,d=a;if("ArrowUp"===n.key){if(f&&ie(n),-1===a)d=s;else if(d=ye(e,{startingIndex:d,amount:i,decrement:!0,disabledIndices:l}),o&&(a-i<c||d<0)){const e=a%i,t=s%i,n=s-(t-e);d=t===e?s:t>e?n:n-i}pe(e,d)&&(d=a)}if("ArrowDown"===n.key&&(f&&ie(n),-1===a?d=c:(d=ye(e,{startingIndex:a,amount:i,disabledIndices:l}),o&&a+i>s&&(d=ye(e,{startingIndex:a%i-i,amount:i,disabledIndices:l}))),pe(e,d)&&(d=a)),"both"===r){const t=E(a/i);n.key===(u?V:G)&&(f&&ie(n),a%i!=i-1?(d=ye(e,{startingIndex:a,disabledIndices:l}),o&&ve(d,i,t)&&(d=ye(e,{startingIndex:a-a%i-1,disabledIndices:l}))):o&&(d=ye(e,{startingIndex:a-a%i-1,disabledIndices:l})),ve(d,i,t)&&(d=a)),n.key===(u?G:V)&&(f&&ie(n),a%i!=0?(d=ye(e,{startingIndex:a,decrement:!0,disabledIndices:l}),o&&ve(d,i,t)&&(d=ye(e,{startingIndex:a+(i-a%i),decrement:!0,disabledIndices:l}))):o&&(d=ye(e,{startingIndex:a+(i-a%i),decrement:!0,disabledIndices:l})),ve(d,i,t)&&(d=a));const r=E(s/i)===t;pe(e,d)&&(d=o&&r?n.key===(u?G:V)?s:ye(e,{startingIndex:a-a%i-1,disabledIndices:l}):a)}return d}function we(e,t,n){const r=[];let o=0;return e.forEach(((e,u)=>{let{width:i,height:l}=e;if(i>t&&"production"!==process.env.NODE_ENV)throw new Error("[Floating UI]: Invalid grid - item width at index "+u+" is greater than grid columns");let c=!1;for(n&&(o=0);!c;){const e=[];for(let n=0;n<i;n++)for(let r=0;r<l;r++)e.push(o+n+r*t);o%t+i<=t&&e.every((e=>null==r[e]))?(e.forEach((e=>{r[e]=u})),c=!0):o++}})),[...r]}function Ee(e,t,n,r,o){if(-1===e)return-1;const u=n.indexOf(e),i=t[e];switch(o){case"tl":return u;case"tr":return i?u+i.width-1:u;case"bl":return i?u+(i.height-1)*r:u;case"br":return n.lastIndexOf(e)}}function Re(e,t){return t.flatMap(((t,n)=>e.includes(t)?[n]:[]))}function xe(e,t,n){if("function"==typeof n)return n(t);if(n)return n.includes(t);const r=e.current[t];return null==r||r.hasAttribute("disabled")||"true"===r.getAttribute("aria-disabled")}const Ie=()=>({getShadowRoot:!0,displayCheck:"function"==typeof ResizeObserver&&ResizeObserver.toString().includes("[native code]")?"full":"none"});function ke(e,t){const n=W(e,Ie()),r=n.length;if(0===r)return;const o=Z(ee(e)),u=n.indexOf(o);return n[-1===u?1===t?0:r-1:u+t]}function Ce(e){return ke(ee(e).body,1)||e}function Me(e){return ke(ee(e).body,-1)||e}function Oe(e,t){const n=t||e.currentTarget,r=e.relatedTarget;return!r||!$(n,r)}function Se(e){W(e,Ie()).forEach((e=>{e.dataset.tabindex=e.getAttribute("tabindex")||"",e.setAttribute("tabindex","-1")}))}function Pe(e){e.querySelectorAll("[data-tabindex]").forEach((e=>{const t=e.dataset.tabindex;delete e.dataset.tabindex,t?e.setAttribute("tabindex",t):e.removeAttribute("tabindex")}))}function Te(e){const t=u.useRef(void 0),n=u.useCallback((t=>{const n=e.map((e=>{if(null!=e){if("function"==typeof e){const n=e,r=n(t);return"function"==typeof r?r:()=>{n(null)}}return e.current=t,()=>{e.current=null}}}));return()=>{n.forEach((e=>null==e?void 0:e()))}}),e);return u.useMemo((()=>e.every((e=>null==e))?null:e=>{t.current&&(t.current(),t.current=void 0),null!=e&&(t.current=n(e))}),e)}function Ae(e,t){const n=e.compareDocumentPosition(t);return n&Node.DOCUMENT_POSITION_FOLLOWING||n&Node.DOCUMENT_POSITION_CONTAINED_BY?-1:n&Node.DOCUMENT_POSITION_PRECEDING||n&Node.DOCUMENT_POSITION_CONTAINS?1:0}const Le=u.createContext({register:()=>{},unregister:()=>{},map:new Map,elementsRef:{current:[]}});function Ne(e){const{children:t,elementsRef:n,labelsRef:r}=e,[o,i]=u.useState((()=>new Set)),l=u.useCallback((e=>{i((t=>new Set(t).add(e)))}),[]),c=u.useCallback((e=>{i((t=>{const n=new Set(t);return n.delete(e),n}))}),[]),s=u.useMemo((()=>{const e=new Map;return Array.from(o.keys()).sort(Ae).forEach(((t,n)=>{e.set(t,n)})),e}),[o]);return u.createElement(Le.Provider,{value:u.useMemo((()=>({register:l,unregister:c,map:s,elementsRef:n,labelsRef:r})),[l,c,s,n,r])},t)}function De(e){void 0===e&&(e={});const{label:t}=e,{register:n,unregister:r,map:o,elementsRef:i,labelsRef:l}=u.useContext(Le),[c,s]=u.useState(null),a=u.useRef(null),f=u.useCallback((e=>{if(a.current=e,null!==c&&(i.current[c]=e,l)){var n;const r=void 0!==t;l.current[c]=r?t:null!=(n=null==e?void 0:e.textContent)?n:null}}),[c,i,l,t]);return ae((()=>{const e=a.current;if(e)return n(e),()=>{r(e)}}),[n,r]),ae((()=>{const e=a.current?o.get(a.current):null;null!=e&&s(e)}),[o]),u.useMemo((()=>({ref:f,index:null==c?-1:c})),[c,f])}const Fe="data-floating-ui-focusable",je="active",Ke="selected",He="ArrowLeft",We="ArrowRight",qe="ArrowUp",_e="ArrowDown";function Be(e,t){return"function"==typeof e?e(t):e?u.cloneElement(e,t):u.createElement("div",t)}const Ue=u.createContext({activeIndex:0,onNavigate:()=>{}}),ze=[He,We],Xe=[qe,_e],Ye=[...ze,...Xe],Ve=u.forwardRef((function(e,t){const{render:n,orientation:r="both",loop:o=!0,rtl:i=!1,cols:l=1,disabledIndices:c,activeIndex:s,onNavigate:a,itemSizes:f,dense:d=!1,...m}=e,[v,p]=u.useState(0),g=null!=s?s:v,h=me(null!=a?a:p),y=u.useRef([]),b=n&&"function"!=typeof n?n.props:{},w=u.useMemo((()=>({activeIndex:g,onNavigate:h})),[g,h]),E=l>1;const R={...m,...b,ref:t,"aria-orientation":"both"===r?void 0:r,onKeyDown(e){null==m.onKeyDown||m.onKeyDown(e),null==b.onKeyDown||b.onKeyDown(e),function(e){if(!Ye.includes(e.key))return;let t=g;const n=ge(y,c),u=he(y,c),s=i?He:We,a=i?We:He;if(E){const a=f||Array.from({length:y.current.length},(()=>({width:1,height:1}))),m=we(a,l,d),v=m.findIndex((e=>null!=e&&!xe(y,e,c))),p=m.reduce(((e,t,n)=>null==t||xe(y,t,c)?e:n),-1),h=m[be({current:m.map((e=>e?y.current[e]:null))},{event:e,orientation:r,loop:o,rtl:i,cols:l,disabledIndices:Re([...("function"!=typeof c?c:null)||y.current.map(((e,t)=>xe(y,t,c)?t:void 0)),void 0],m),minIndex:v,maxIndex:p,prevIndex:Ee(g>u?n:g,a,m,l,e.key===_e?"bl":e.key===s?"tr":"tl")})];null!=h&&(t=h)}const m={horizontal:[s],vertical:[_e],both:[s,_e]}[r],v={horizontal:[a],vertical:[qe],both:[a,qe]}[r],p=E?Ye:{horizontal:ze,vertical:Xe,both:Ye}[r];var b;t===g&&[...m,...v].includes(e.key)&&(t=o&&t===u&&m.includes(e.key)?n:o&&t===n&&v.includes(e.key)?u:ye(y,{startingIndex:t,decrement:v.includes(e.key),disabledIndices:c})),t===g||pe(y,t)||(e.stopPropagation(),p.includes(e.key)&&e.preventDefault(),h(t),null==(b=y.current[t])||b.focus())}(e)}};return u.createElement(Ue.Provider,{value:w},u.createElement(Ne,{elementsRef:y},Be(n,R)))})),Ge=u.forwardRef((function(e,t){const{render:n,...r}=e,o=n&&"function"!=typeof n?n.props:{},{activeIndex:i,onNavigate:l}=u.useContext(Ue),{ref:c,index:s}=De(),a=Te([c,t,o.ref]),f=i===s;return Be(n,{...r,...o,ref:a,tabIndex:f?0:-1,"data-active":f?"":void 0,onFocus(e){null==r.onFocus||r.onFocus(e),null==o.onFocus||o.onFocus(e),l(s)}})}));function Ze(){return Ze=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ze.apply(null,arguments)}const $e={...u};let Qe=!1,Je=0;const et=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+Je++;const tt=$e.useId||function(){const[e,t]=u.useState((()=>Qe?et():void 0));return ae((()=>{null==e&&t(et())}),[]),u.useEffect((()=>{Qe=!0}),[]),e},nt=u.forwardRef((function(e,t){const{context:{placement:n,elements:{floating:r},middlewareData:{arrow:o,shift:i}},width:l=14,height:c=7,tipRadius:s=0,strokeWidth:a=0,staticOffset:f,stroke:d,d:m,style:{transform:v,...p}={},...h}=e,y=tt(),[b,w]=u.useState(!1);if(ae((()=>{if(!r)return;"rtl"===g(r).direction&&w(!0)}),[r]),!r)return null;const[E,R]=n.split("-"),x="top"===E||"bottom"===E;let I=f;(x&&null!=i&&i.x||!x&&null!=i&&i.y)&&(I=null);const k=2*a,C=k/2,M=l/2*(s/-8+1),O=c/2*s/4,S=!!m,P=I&&"end"===R?"bottom":"top";let T=I&&"end"===R?"right":"left";I&&b&&(T="end"===R?"left":"right");const A=null!=(null==o?void 0:o.x)?I||o.x:"",L=null!=(null==o?void 0:o.y)?I||o.y:"",N=m||"M0,0 H"+l+" L"+(l-M)+","+(c-O)+" Q"+l/2+","+c+" "+M+","+(c-O)+" Z",D={top:S?"rotate(180deg)":"",left:S?"rotate(90deg)":"rotate(-90deg)",bottom:S?"":"rotate(180deg)",right:S?"rotate(-90deg)":"rotate(90deg)"}[E];return u.createElement("svg",Ze({},h,{"aria-hidden":!0,ref:t,width:S?l:l+k,height:l,viewBox:"0 0 "+l+" "+(c>l?c:l),style:{position:"absolute",pointerEvents:"none",[T]:A,[P]:L,[E]:x||S?"100%":"calc(100% - "+k/2+"px)",transform:[D,v].filter((e=>!!e)).join(" "),...p}}),k>0&&u.createElement("path",{clipPath:"url(#"+y+")",fill:"none",stroke:d,strokeWidth:k+(m?0:1),d:N}),u.createElement("path",{stroke:k&&!m?h.fill:"none",d:N}),u.createElement("clipPath",{id:y},u.createElement("rect",{x:-C,y:C*(S?-1:1),width:l+k,height:l})))}));function rt(){const e=new Map;return{emit(t,n){var r;null==(r=e.get(t))||r.forEach((e=>e(n)))},on(t,n){e.has(t)||e.set(t,new Set),e.get(t).add(n)},off(t,n){var r;null==(r=e.get(t))||r.delete(n)}}}const ot=u.createContext(null),ut=u.createContext(null),it=()=>{var e;return(null==(e=u.useContext(ot))?void 0:e.id)||null},lt=()=>u.useContext(ut);function ct(e){return"data-floating-ui-"+e}function st(e){-1!==e.current&&(clearTimeout(e.current),e.current=-1)}const at=ct("safe-polygon");function ft(e,t,n){if(n&&!se(n))return 0;if("number"==typeof e)return e;if("function"==typeof e){const n=e();return"number"==typeof n?n:null==n?void 0:n[t]}return null==e?void 0:e[t]}function dt(e){return"function"==typeof e?e():e}const mt=()=>{},vt=u.createContext({delay:0,initialDelay:0,timeoutMs:0,currentId:null,setCurrentId:mt,setState:mt,isInstantPhase:!1}),pt=()=>u.useContext(vt);const gt=u.createContext({hasProvider:!1,timeoutMs:0,delayRef:{current:0},initialDelayRef:{current:0},timeoutIdRef:{current:-1},currentIdRef:{current:null},currentContextRef:{current:null}});let ht=0;function yt(e,t){void 0===t&&(t={});const{preventScroll:n=!1,cancelPrevious:r=!0,sync:o=!1}=t;r&&cancelAnimationFrame(ht);const u=()=>null==e?void 0:e.focus({preventScroll:n});o?u():ht=requestAnimationFrame(u)}function bt(e,t){if(!e||!t)return!1;const n=null==t.getRootNode?void 0:t.getRootNode();if(e.contains(t))return!0;if(n&&m(n)){let n=t;for(;n;){if(e===n)return!0;n=n.parentNode||n.host}}return!1}const wt={inert:new WeakMap,"aria-hidden":new WeakMap,none:new WeakMap};function Et(e){return"inert"===e?wt.inert:"aria-hidden"===e?wt["aria-hidden"]:wt.none}let Rt=new WeakSet,xt={},It=0;const kt=e=>e&&(e.host||kt(e.parentNode));function Ct(e,t,n,r){const o="data-floating-ui-inert",u=r?"inert":n?"aria-hidden":null,i=(l=t,e.map((e=>{if(l.contains(e))return e;const t=kt(e);return l.contains(t)?t:null})).filter((e=>null!=e)));var l;const s=new Set,a=new Set(i),f=[];xt[o]||(xt[o]=new WeakMap);const d=xt[o];return i.forEach((function e(t){if(!t||s.has(t))return;s.add(t),t.parentNode&&e(t.parentNode)})),function e(t){if(!t||a.has(t))return;[].forEach.call(t.children,(t=>{if("script"!==c(t))if(s.has(t))e(t);else{const e=u?t.getAttribute(u):null,n=null!==e&&"false"!==e,r=Et(u),i=(r.get(t)||0)+1,l=(d.get(t)||0)+1;r.set(t,i),d.set(t,l),f.push(t),1===i&&n&&Rt.add(t),1===l&&t.setAttribute(o,""),!n&&u&&t.setAttribute(u,"inert"===u?"":"true")}}))}(t),s.clear(),It++,()=>{f.forEach((e=>{const t=Et(u),n=(t.get(e)||0)-1,r=(d.get(e)||0)-1;t.set(e,n),d.set(e,r),n||(!Rt.has(e)&&u&&e.removeAttribute(u),Rt.delete(e)),r||e.removeAttribute(o)})),It--,It||(wt.inert=new WeakMap,wt["aria-hidden"]=new WeakMap,wt.none=new WeakMap,Rt=new WeakSet,xt={})}}function Mt(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);const r=(o=e[0],(null==o?void 0:o.ownerDocument)||document).body;var o;return Ct(e.concat(Array.from(r.querySelectorAll('[aria-live],[role="status"],output'))),r,t,n)}const Ot={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"fixed",whiteSpace:"nowrap",width:"1px",top:0,left:0},St=u.forwardRef((function(e,t){const[n,r]=u.useState();ae((()=>{U()&&r("button")}),[]);const o={ref:t,tabIndex:0,role:n,"aria-hidden":!n||void 0,[ct("focus-guard")]:"",style:Ot};return u.createElement("span",Ze({},e,o))})),Pt=u.createContext(null),Tt=ct("portal");function At(e){void 0===e&&(e={});const{id:t,root:n}=e,r=tt(),o=Lt(),[i,l]=u.useState(null),c=u.useRef(null);return ae((()=>()=>{null==i||i.remove(),queueMicrotask((()=>{c.current=null}))}),[i]),ae((()=>{if(!r)return;if(c.current)return;const e=t?document.getElementById(t):null;if(!e)return;const n=document.createElement("div");n.id=r,n.setAttribute(Tt,""),e.appendChild(n),c.current=n,l(n)}),[t,r]),ae((()=>{if(null===n)return;if(!r)return;if(c.current)return;let e=n||(null==o?void 0:o.portalNode);e&&!f(e)&&(e=e.current),e=e||document.body;let u=null;t&&(u=document.createElement("div"),u.id=t,e.appendChild(u));const i=document.createElement("div");i.id=r,i.setAttribute(Tt,""),e=u||e,e.appendChild(i),c.current=i,l(i)}),[t,n,r,o]),i}const Lt=()=>u.useContext(Pt);function Nt(e){return u.useMemo((()=>t=>{e.forEach((e=>{e&&(e.current=t)}))}),e)}let Dt=[];function Ft(){return Dt.slice().reverse().find((e=>e.isConnected))}function jt(e,t){var n;if(!(t.current.includes("floating")||null!=(n=e.getAttribute("role"))&&n.includes("dialog")))return;const r=Ie(),o=function(e,t){return(t=t||{}).getShadowRoot?O([e],t.includeContainer,{filter:F.bind(null,t),flatten:!0,getShadowRoot:t.getShadowRoot}):M(e,t.includeContainer,F.bind(null,t))}(e,r),u=o.filter((e=>{const t=e.getAttribute("data-tabindex")||"";return q(e,r)||e.hasAttribute("data-tabindex")&&!t.startsWith("-")})),i=e.getAttribute("tabindex");t.current.includes("floating")||0===u.length?"0"!==i&&e.setAttribute("tabindex","0"):("-1"!==i||e.hasAttribute("data-tabindex")&&"-1"!==e.getAttribute("data-tabindex"))&&(e.setAttribute("tabindex","-1"),e.setAttribute("data-tabindex","-1"))}const Kt=u.forwardRef((function(e,t){return u.createElement("button",Ze({},e,{type:"button",ref:t,tabIndex:-1,style:Ot}))}));let Ht=0;const Wt="--floating-ui-scrollbar-width";let qt=()=>{};const _t=u.forwardRef((function(e,t){const{lockScroll:n=!1,...r}=e;return ae((()=>{if(n)return Ht++,1===Ht&&(qt=function(){const e=_(),t=/iP(hone|ad|od)|iOS/.test(e)||"MacIntel"===e&&navigator.maxTouchPoints>1,n=document.body.style,r=Math.round(document.documentElement.getBoundingClientRect().left)+document.documentElement.scrollLeft?"paddingLeft":"paddingRight",o=window.innerWidth-document.documentElement.clientWidth,u=n.left?parseFloat(n.left):window.scrollX,i=n.top?parseFloat(n.top):window.scrollY;if(n.overflow="hidden",n.setProperty(Wt,o+"px"),o&&(n[r]=o+"px"),t){var l,c;const e=(null==(l=window.visualViewport)?void 0:l.offsetLeft)||0,t=(null==(c=window.visualViewport)?void 0:c.offsetTop)||0;Object.assign(n,{position:"fixed",top:-(i-Math.floor(t))+"px",left:-(u-Math.floor(e))+"px",right:"0"})}return()=>{Object.assign(n,{overflow:"",[r]:""}),n.removeProperty(Wt),t&&(Object.assign(n,{position:"",top:"",left:"",right:""}),window.scrollTo(u,i))}}()),()=>{Ht--,0===Ht&&qt()}}),[n]),u.createElement("div",Ze({ref:t},r,{style:{position:"fixed",overflow:"auto",top:0,right:0,bottom:0,left:0,...r.style}}))}));function Bt(e){return d(e.target)&&"BUTTON"===e.target.tagName}function Ut(e){return te(e)}function zt(e){return null!=e&&null!=e.clientX}const Xt={pointerdown:"onPointerDown",mousedown:"onMouseDown",click:"onClick"},Yt={pointerdown:"onPointerDownCapture",mousedown:"onMouseDownCapture",click:"onClickCapture"},Vt=e=>{var t,n;return{escapeKey:"boolean"==typeof e?e:null!=(t=null==e?void 0:e.escapeKey)&&t,outsidePress:"boolean"==typeof e?e:null==(n=null==e?void 0:e.outsidePress)||n}};function Gt(e){const{open:t=!1,onOpenChange:n,elements:r}=e,o=tt(),i=u.useRef({}),[l]=u.useState((()=>rt())),c=null!=it(),[s,a]=u.useState(r.reference),f=me(((e,t,r)=>{i.current.openEvent=e?t:void 0,l.emit("openchange",{open:e,event:t,reason:r,nested:c}),null==n||n(e,t,r)})),d=u.useMemo((()=>({setPositionReference:a})),[]),m=u.useMemo((()=>({reference:s||r.reference||null,floating:r.floating||null,domReference:r.reference})),[s,r.reference,r.floating]);return u.useMemo((()=>({dataRef:i,open:t,onOpenChange:f,elements:m,events:l,floatingId:o,refs:d})),[t,f,m,l,o,d])}function Zt(){return _().toLowerCase().startsWith("mac")&&!navigator.maxTouchPoints&&U()}function $t(e,t,n){const r=new Map,o="item"===n;let u=e;if(o&&e){const{[je]:t,[Ke]:n,...r}=e;u=r}return{..."floating"===n&&{tabIndex:-1,[Fe]:""},...u,...t.map((t=>{const r=t?t[n]:null;return"function"==typeof r?e?r(e):null:r})).concat(e).reduce(((e,t)=>t?(Object.entries(t).forEach((t=>{let[n,u]=t;var i;o&&[je,Ke].includes(n)||(0===n.indexOf("on")?(r.has(n)||r.set(n,[]),"function"==typeof u&&(null==(i=r.get(n))||i.push(u),e[n]=function(){for(var e,t=arguments.length,o=new Array(t),u=0;u<t;u++)o[u]=arguments[u];return null==(e=r.get(n))?void 0:e.map((e=>e(...o))).find((e=>void 0!==e))})):e[n]=u)})),e):e),{})}}function Qt(e,t,n){switch(e){case"vertical":return t;case"horizontal":return n;default:return t||n}}function Jt(e,t){return Qt(t,e===qe||e===_e,e===He||e===We)}function en(e,t,n){return Qt(t,e===_e,n?e===He:e===We)||"Enter"===e||" "===e||""===e}function tn(e,t,n){return Qt(t,n?e===He:e===We,e===_e)}function nn(e,t,n,r){return"both"===t||"horizontal"===t&&r&&r>1?"Escape"===e:Qt(t,n?e===We:e===He,e===qe)}const rn=new Map([["select","listbox"],["combobox","listbox"],["label",!1]]);const on=e=>e.replace(/[A-Z]+(?![a-z])|[A-Z]/g,((e,t)=>(t?"-":"")+e.toLowerCase()));function un(e,t){return"function"==typeof e?e(t):e}function ln(e,t){void 0===t&&(t={});const{open:n,elements:{floating:r}}=e,{duration:o=250}=t,l=("number"==typeof o?o:o.close)||0,[c,s]=u.useState("unmounted"),a=function(e,t){const[n,r]=u.useState(e);return e&&!n&&r(!0),u.useEffect((()=>{if(!e&&n){const e=setTimeout((()=>r(!1)),t);return()=>clearTimeout(e)}}),[e,n,t]),n}(n,l);return a||"close"!==c||s("unmounted"),ae((()=>{if(r){if(n){s("initial");const e=requestAnimationFrame((()=>{i.flushSync((()=>{s("open")}))}));return()=>{cancelAnimationFrame(e)}}s("close")}}),[n,r]),{isMounted:a,status:c}}function cn(e,t){return{...e,rects:{...e.rects,floating:{...e.rects.floating,height:t}}}}function sn(e,t,n){void 0===n&&(n=!0);return e.filter((e=>{var r;return e.parentId===t&&(!n||(null==(r=e.context)?void 0:r.open))})).flatMap((t=>[t,...sn(e,t.id,n)]))}function an(e,t){const[n,r]=e;let o=!1;const u=t.length;for(let e=0,i=u-1;e<u;i=e++){const[u,l]=t[e]||[0,0],[c,s]=t[i]||[0,0];l>=r!=s>=r&&n<=(c-u)*(r-l)/(s-l)+u&&(o=!o)}return o}Object.defineProperty(e,"arrow",{enumerable:!0,get:function(){return r.arrow}}),Object.defineProperty(e,"autoPlacement",{enumerable:!0,get:function(){return r.autoPlacement}}),Object.defineProperty(e,"autoUpdate",{enumerable:!0,get:function(){return r.autoUpdate}}),Object.defineProperty(e,"computePosition",{enumerable:!0,get:function(){return r.computePosition}}),Object.defineProperty(e,"detectOverflow",{enumerable:!0,get:function(){return r.detectOverflow}}),Object.defineProperty(e,"flip",{enumerable:!0,get:function(){return r.flip}}),Object.defineProperty(e,"getOverflowAncestors",{enumerable:!0,get:function(){return r.getOverflowAncestors}}),Object.defineProperty(e,"hide",{enumerable:!0,get:function(){return r.hide}}),Object.defineProperty(e,"inline",{enumerable:!0,get:function(){return r.inline}}),Object.defineProperty(e,"limitShift",{enumerable:!0,get:function(){return r.limitShift}}),Object.defineProperty(e,"offset",{enumerable:!0,get:function(){return r.offset}}),Object.defineProperty(e,"platform",{enumerable:!0,get:function(){return r.platform}}),Object.defineProperty(e,"shift",{enumerable:!0,get:function(){return r.shift}}),Object.defineProperty(e,"size",{enumerable:!0,get:function(){return r.size}}),e.Composite=Ve,e.CompositeItem=Ge,e.FloatingArrow=nt,e.FloatingDelayGroup=function(e){const{children:t,delay:n,timeoutMs:r=0}=e,[o,i]=u.useReducer(((e,t)=>({...e,...t})),{delay:n,timeoutMs:r,initialDelay:n,currentId:null,isInstantPhase:!1}),l=u.useRef(null),c=u.useCallback((e=>{i({currentId:e})}),[]);return ae((()=>{o.currentId?null===l.current?l.current=o.currentId:o.isInstantPhase||i({isInstantPhase:!0}):(o.isInstantPhase&&i({isInstantPhase:!1}),l.current=null)}),[o.currentId,o.isInstantPhase]),u.createElement(vt.Provider,{value:u.useMemo((()=>({...o,setState:i,setCurrentId:c})),[o,c])},t)},e.FloatingFocusManager=function(e){const{context:t,children:n,disabled:r=!1,order:o=["content"],guards:i=!0,initialFocus:l=0,returnFocus:s=!0,restoreFocus:a=!1,modal:f=!0,visuallyHiddenDismiss:m=!1,closeOnFocusOut:v=!0,outsideElementsInert:p=!1,getInsideElements:g=()=>[]}=e,{open:h,onOpenChange:y,events:b,dataRef:w,elements:{domReference:E,floating:R}}=t,x=me((()=>{var e;return null==(e=w.current.floatingContext)?void 0:e.nodeId})),I=me(g),k="number"==typeof l&&l<0,C=ne(E)&&k,M="undefined"!=typeof HTMLElement&&"inert"in HTMLElement.prototype,O=!M||i,S=!O||M&&p,P=fe(o),T=fe(l),A=fe(s),L=lt(),N=Lt(),D=u.useRef(null),F=u.useRef(null),j=u.useRef(!1),K=u.useRef(!1),H=u.useRef(-1),_=null!=N,B=re(R),U=me((function(e){return void 0===e&&(e=B),e?W(e,Ie()):[]})),z=me((e=>{const t=U(e);return P.current.map((e=>E&&"reference"===e?E:B&&"floating"===e?B:t)).filter(Boolean).flat()}));u.useEffect((()=>{if(r)return;if(!f)return;function e(e){if("Tab"===e.key){$(B,Z(ee(B)))&&0===U().length&&!C&&ie(e);const t=z(),n=Q(e);"reference"===P.current[0]&&n===E&&(ie(e),e.shiftKey?yt(t[t.length-1]):yt(t[1])),"floating"===P.current[1]&&n===B&&e.shiftKey&&(ie(e),yt(t[0]))}}const t=ee(B);return t.addEventListener("keydown",e),()=>{t.removeEventListener("keydown",e)}}),[r,E,B,f,P,C,U,z]),u.useEffect((()=>{if(!r&&R)return R.addEventListener("focusin",e),()=>{R.removeEventListener("focusin",e)};function e(e){const t=Q(e),n=U().indexOf(t);-1!==n&&(H.current=n)}}),[r,R,U]),u.useEffect((()=>{if(!r&&v)return R&&d(E)?(E.addEventListener("focusout",t),E.addEventListener("pointerdown",e),R.addEventListener("focusout",t),()=>{E.removeEventListener("focusout",t),E.removeEventListener("pointerdown",e),R.removeEventListener("focusout",t)}):void 0;function e(){K.current=!0,setTimeout((()=>{K.current=!1}))}function t(e){const t=e.relatedTarget,n=e.currentTarget,r=Q(e);queueMicrotask((()=>{const o=x(),u=!($(E,t)||$(R,t)||$(t,R)||$(null==N?void 0:N.portalNode,t)||null!=t&&t.hasAttribute(ct("focus-guard"))||L&&(oe(L.nodesRef.current,o).find((e=>{var n,r;return $(null==(n=e.context)?void 0:n.elements.floating,t)||$(null==(r=e.context)?void 0:r.elements.domReference,t)}))||ue(L.nodesRef.current,o).find((e=>{var n,r,o;return[null==(n=e.context)?void 0:n.elements.floating,re(null==(r=e.context)?void 0:r.elements.floating)].includes(t)||(null==(o=e.context)?void 0:o.elements.domReference)===t}))));if(n===E&&B&&jt(B,P),a&&n!==E&&(null==r||!r.isConnected)&&Z(ee(B))===ee(B).body){d(B)&&B.focus();const e=H.current,t=U(),n=t[e]||t[t.length-1]||B;d(n)&&n.focus()}w.current.insideReactTree?w.current.insideReactTree=!1:!C&&f||!t||!u||K.current||t===Ft()||(j.current=!0,y(!1,e,"focus-out"))}))}}),[r,E,R,B,f,L,N,y,v,a,U,C,x,P,w]);const X=u.useRef(null),Y=u.useRef(null),V=Nt([X,null==N?void 0:N.beforeInsideRef]),G=Nt([Y,null==N?void 0:N.afterInsideRef]);function J(e){return!r&&m&&f?u.createElement(Kt,{ref:"start"===e?D:F,onClick:e=>y(!1,e.nativeEvent)},"string"==typeof m?m:"Dismiss"):null}u.useEffect((()=>{var e,t;if(r)return;if(!R)return;const n=Array.from((null==N||null==(e=N.portalNode)?void 0:e.querySelectorAll("["+ct("portal")+"]"))||[]),o=L?ue(L.nodesRef.current,x()):[],u=L&&!f?o.map((e=>{var t;return null==(t=e.context)?void 0:t.elements.floating})):[],i=null==(t=o.find((e=>{var t;return ne((null==(t=e.context)?void 0:t.elements.domReference)||null)})))||null==(t=t.context)?void 0:t.elements.domReference,l=[R,i,...n,...u,...I(),D.current,F.current,X.current,Y.current,null==N?void 0:N.beforeOutsideRef.current,null==N?void 0:N.afterOutsideRef.current,P.current.includes("reference")||C?E:null].filter((e=>null!=e)),c=f||C?Mt(l,!S,S):Mt(l);return()=>{c()}}),[r,E,R,f,P,N,C,O,S,L,x,I]),ae((()=>{if(r||!d(B))return;const e=Z(ee(B));queueMicrotask((()=>{const t=z(B),n=T.current,r=("number"==typeof n?t[n]:n.current)||B,o=$(B,e);k||o||!h||yt(r,{preventScroll:r===B})}))}),[r,h,B,k,z,T]),ae((()=>{if(r||!B)return;const e=ee(B),t=Z(e);var n;function o(e){let{reason:t,event:n,nested:r}=e;if(["hover","safe-polygon"].includes(t)&&"mouseleave"===n.type&&(j.current=!0),"outside-press"===t)if(r)j.current=!1;else if(le(n)||ce(n))j.current=!1;else{let e=!1;document.createElement("div").focus({get preventScroll(){return e=!0,!1}}),j.current=!e}}n=t,Dt=Dt.filter((e=>e.isConnected)),n&&"body"!==c(n)&&(Dt.push(n),Dt.length>20&&(Dt=Dt.slice(-20))),b.on("openchange",o);const u=e.createElement("span");return u.setAttribute("tabindex","-1"),u.setAttribute("aria-hidden","true"),Object.assign(u.style,Ot),_&&E&&E.insertAdjacentElement("afterend",u),()=>{b.off("openchange",o);const t=Z(e),n=$(R,t)||L&&oe(L.nodesRef.current,x(),!1).some((e=>{var n;return $(null==(n=e.context)?void 0:n.elements.floating,t)})),r=function(){if("boolean"==typeof A.current){const e=E||Ft();return e&&e.isConnected?e:u}return A.current.current||u}();queueMicrotask((()=>{const o=function(e){const t=Ie();return q(e,t)?e:W(e,t)[0]||e}(r);A.current&&!j.current&&d(o)&&(o===t||t===e.body||n)&&o.focus({preventScroll:!0}),u.remove()}))}}),[r,R,B,A,w,b,L,_,E,x]),u.useEffect((()=>{queueMicrotask((()=>{j.current=!1}))}),[r]),ae((()=>{if(!r&&N)return N.setFocusManagerState({modal:f,closeOnFocusOut:v,open:h,onOpenChange:y,domReference:E}),()=>{N.setFocusManagerState(null)}}),[r,N,f,h,y,v,E]),ae((()=>{r||B&&jt(B,P)}),[r,B,P]);const te=!r&&O&&(!f||!C)&&(_||f);return u.createElement(u.Fragment,null,te&&u.createElement(St,{"data-type":"inside",ref:V,onFocus:e=>{if(f){const e=z();yt("reference"===o[0]?e[0]:e[e.length-1])}else if(null!=N&&N.preserveTabOrder&&N.portalNode)if(j.current=!1,Oe(e,N.portalNode)){const e=Ce(E);null==e||e.focus()}else{var t;null==(t=N.beforeOutsideRef.current)||t.focus()}}}),!C&&J("start"),n,J("end"),te&&u.createElement(St,{"data-type":"inside",ref:G,onFocus:e=>{if(f)yt(z()[0]);else if(null!=N&&N.preserveTabOrder&&N.portalNode)if(v&&(j.current=!0),Oe(e,N.portalNode)){const e=Me(E);null==e||e.focus()}else{var t;null==(t=N.afterOutsideRef.current)||t.focus()}}}))},e.FloatingList=Ne,e.FloatingNode=function(e){const{children:t,id:n}=e,r=it();return u.createElement(ot.Provider,{value:u.useMemo((()=>({id:n,parentId:r})),[n,r])},t)},e.FloatingOverlay=_t,e.FloatingPortal=function(e){const{children:t,id:n,root:r,preserveTabOrder:o=!0}=e,l=At({id:n,root:r}),[c,s]=u.useState(null),a=u.useRef(null),f=u.useRef(null),d=u.useRef(null),m=u.useRef(null),v=null==c?void 0:c.modal,p=null==c?void 0:c.open,g=!!c&&!c.modal&&c.open&&o&&!(!r&&!l);return u.useEffect((()=>{if(l&&o&&!v)return l.addEventListener("focusin",e,!0),l.addEventListener("focusout",e,!0),()=>{l.removeEventListener("focusin",e,!0),l.removeEventListener("focusout",e,!0)};function e(e){if(l&&Oe(e)){("focusin"===e.type?Pe:Se)(l)}}}),[l,o,v]),u.useEffect((()=>{l&&(p||Pe(l))}),[p,l]),u.createElement(Pt.Provider,{value:u.useMemo((()=>({preserveTabOrder:o,beforeOutsideRef:a,afterOutsideRef:f,beforeInsideRef:d,afterInsideRef:m,portalNode:l,setFocusManagerState:s})),[o,l])},g&&l&&u.createElement(St,{"data-type":"outside",ref:a,onFocus:e=>{if(Oe(e,l)){var t;null==(t=d.current)||t.focus()}else{const e=Me(c?c.domReference:null);null==e||e.focus()}}}),g&&l&&u.createElement("span",{"aria-owns":l.id,style:Ot}),l&&i.createPortal(t,l),g&&l&&u.createElement(St,{"data-type":"outside",ref:f,onFocus:e=>{if(Oe(e,l)){var t;null==(t=m.current)||t.focus()}else{const t=Ce(c?c.domReference:null);null==t||t.focus(),(null==c?void 0:c.closeOnFocusOut)&&(null==c||c.onOpenChange(!1,e.nativeEvent,"focus-out"))}}}))},e.FloatingTree=function(e){const{children:t}=e,n=u.useRef([]),r=u.useCallback((e=>{n.current=[...n.current,e]}),[]),o=u.useCallback((e=>{n.current=n.current.filter((t=>t!==e))}),[]),[i]=u.useState((()=>rt()));return u.createElement(ut.Provider,{value:u.useMemo((()=>({nodesRef:n,addNode:r,removeNode:o,events:i})),[r,o,i])},t)},e.NextFloatingDelayGroup=function(e){const{children:t,delay:n,timeoutMs:r=0}=e,o=u.useRef(n),i=u.useRef(n),l=u.useRef(null),c=u.useRef(null),s=u.useRef(-1);return u.createElement(gt.Provider,{value:u.useMemo((()=>({hasProvider:!0,delayRef:o,initialDelayRef:i,currentIdRef:l,timeoutMs:r,currentContextRef:c,timeoutIdRef:s})),[r])},t)},e.inner=e=>({name:"inner",options:e,async fn(t){const{listRef:n,overflowRef:o,onFallbackChange:u,offset:l=0,index:c=0,minItemsVisible:s=4,referenceOverflowThreshold:a=0,scrollRef:f,...d}=(v=t,"function"==typeof(m=e)?m(v):m);var m,v;const{rects:p,elements:{floating:g}}=t,h=n.current[c],E=(null==f?void 0:f.current)||g,R=g.clientTop||E.clientTop,x=0!==g.clientTop,I=0!==E.clientTop,k=g===E;if(!h)return{};const C={...t,...await r.offset(-h.offsetTop-g.clientTop-p.reference.height/2-h.offsetHeight/2-l).fn(t)},M=await r.detectOverflow(cn(C,E.scrollHeight+R+g.clientTop),d),O=await r.detectOverflow(C,{...d,elementContext:"reference"}),S=b(0,M.top),P=C.y+S,T=(E.scrollHeight>E.clientHeight?e=>e:w)(b(0,E.scrollHeight+(x&&k||I?2*R:0)-S-b(0,M.bottom)));if(E.style.maxHeight=T+"px",E.scrollTop=S,u){const e=E.offsetHeight<h.offsetHeight*y(s,n.current.length)-1||O.top>=-a||O.bottom>=-a;i.flushSync((()=>u(e)))}return o&&(o.current=await r.detectOverflow(cn({...C,y:P},E.offsetHeight+R+g.clientTop),d)),{y:P}}}),e.safePolygon=function(e){void 0===e&&(e={});const{buffer:t=.5,blockPointerEvents:n=!1,requireIntent:r=!0}=e,o={current:-1};let u=!1,i=null,l=null,c=performance.now();const s=e=>{let{x:n,y:s,placement:a,elements:d,onClose:m,nodeId:v,tree:p}=e;return function(e){function g(){st(o),m()}if(st(o),!d.domReference||!d.floating||null==a||null==n||null==s)return;const{clientX:h,clientY:y}=e,b=[h,y],w=function(e){return"composedPath"in e?e.composedPath()[0]:e.target}(e),E="mouseleave"===e.type,R=bt(d.floating,w),x=bt(d.domReference,w),I=d.domReference.getBoundingClientRect(),k=d.floating.getBoundingClientRect(),C=a.split("-")[0],M=n>k.right-k.width/2,O=s>k.bottom-k.height/2,S=function(e,t){return e[0]>=t.x&&e[0]<=t.x+t.width&&e[1]>=t.y&&e[1]<=t.y+t.height}(b,I),P=k.width>I.width,T=k.height>I.height,A=(P?I:k).left,L=(P?I:k).right,N=(T?I:k).top,D=(T?I:k).bottom;if(R&&(u=!0,!E))return;if(x&&(u=!1),x&&!E)return void(u=!0);if(E&&f(e.relatedTarget)&&bt(d.floating,e.relatedTarget))return;if(p&&sn(p.nodesRef.current,v).length)return;if("top"===C&&s>=I.bottom-1||"bottom"===C&&s<=I.top+1||"left"===C&&n>=I.right-1||"right"===C&&n<=I.left+1)return g();let F=[];switch(C){case"top":F=[[A,I.top+1],[A,k.bottom-1],[L,k.bottom-1],[L,I.top+1]];break;case"bottom":F=[[A,k.top+1],[A,I.bottom-1],[L,I.bottom-1],[L,k.top+1]];break;case"left":F=[[k.right-1,D],[k.right-1,N],[I.left+1,N],[I.left+1,D]];break;case"right":F=[[I.right-1,D],[I.right-1,N],[k.left+1,N],[k.left+1,D]]}if(!an([h,y],F)){if(u&&!S)return g();if(!E&&r){const t=function(e,t){const n=performance.now(),r=n-c;if(null===i||null===l||0===r)return i=e,l=t,c=n,null;const o=e-i,u=t-l,s=Math.sqrt(o*o+u*u);return i=e,l=t,c=n,s/r}(e.clientX,e.clientY);if(null!==t&&t<.1)return g()}an([h,y],function(e){let[n,r]=e;switch(C){case"top":return[[P?n+t/2:M?n+4*t:n-4*t,r+t+1],[P?n-t/2:M?n+4*t:n-4*t,r+t+1],...[[k.left,M||P?k.bottom-t:k.top],[k.right,M?P?k.bottom-t:k.top:k.bottom-t]]];case"bottom":return[[P?n+t/2:M?n+4*t:n-4*t,r-t],[P?n-t/2:M?n+4*t:n-4*t,r-t],...[[k.left,M||P?k.top+t:k.bottom],[k.right,M?P?k.top+t:k.bottom:k.top+t]]];case"left":{const e=[n+t+1,T?r+t/2:O?r+4*t:r-4*t],o=[n+t+1,T?r-t/2:O?r+4*t:r-4*t];return[...[[O||T?k.right-t:k.left,k.top],[O?T?k.right-t:k.left:k.right-t,k.bottom]],e,o]}case"right":return[[n-t,T?r+t/2:O?r+4*t:r-4*t],[n-t,T?r-t/2:O?r+4*t:r-4*t],...[[O||T?k.left+t:k.right,k.top],[O?T?k.left+t:k.right:k.left+t,k.bottom]]]}}([n,s]))?!u&&r&&(o.current=window.setTimeout(g,40)):g()}}};return s.__options={blockPointerEvents:n},s},e.useClick=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,dataRef:o,elements:{domReference:i}}=e,{enabled:l=!0,event:c="click",toggle:s=!0,ignoreMouse:a=!1,keyboardHandlers:f=!0,stickIfOpen:m=!0}=t,v=u.useRef(),p=u.useRef(!1),g=u.useMemo((()=>({onPointerDown(e){v.current=e.pointerType},onMouseDown(e){const t=v.current;0===e.button&&"click"!==c&&(se(t,!0)&&a||(!n||!s||o.current.openEvent&&m&&"mousedown"!==o.current.openEvent.type?(e.preventDefault(),r(!0,e.nativeEvent,"click")):r(!1,e.nativeEvent,"click")))},onClick(e){const t=v.current;"mousedown"===c&&v.current?v.current=void 0:se(t,!0)&&a||(!n||!s||o.current.openEvent&&m&&"click"!==o.current.openEvent.type?r(!0,e.nativeEvent,"click"):r(!1,e.nativeEvent,"click"))},onKeyDown(e){v.current=void 0,e.defaultPrevented||!f||Bt(e)||(" "!==e.key||Ut(i)||(e.preventDefault(),p.current=!0),function(e){return d(e.target)&&"A"===e.target.tagName}(e)||"Enter"===e.key&&r(!n||!s,e.nativeEvent,"click"))},onKeyUp(e){e.defaultPrevented||!f||Bt(e)||Ut(i)||" "===e.key&&p.current&&(p.current=!1,r(!n||!s,e.nativeEvent,"click"))}})),[o,i,c,a,f,r,n,m,s]);return u.useMemo((()=>l?{reference:g}:{}),[l,g])},e.useClientPoint=function(e,t){void 0===t&&(t={});const{open:n,dataRef:r,elements:{floating:o,domReference:i},refs:l}=e,{enabled:c=!0,axis:a="both",x:f=null,y:d=null}=t,m=u.useRef(!1),v=u.useRef(null),[p,g]=u.useState(),[h,y]=u.useState([]),b=me(((e,t)=>{m.current||r.current.openEvent&&!zt(r.current.openEvent)||l.setPositionReference(function(e,t){let n=null,r=null,o=!1;return{contextElement:e||void 0,getBoundingClientRect(){var u;const i=(null==e?void 0:e.getBoundingClientRect())||{width:0,height:0,x:0,y:0},l="x"===t.axis||"both"===t.axis,c="y"===t.axis||"both"===t.axis,s=["mouseenter","mousemove"].includes((null==(u=t.dataRef.current.openEvent)?void 0:u.type)||"")&&"touch"!==t.pointerType;let a=i.width,f=i.height,d=i.x,m=i.y;return null==n&&t.x&&l&&(n=i.x-t.x),null==r&&t.y&&c&&(r=i.y-t.y),d-=n||0,m-=r||0,a=0,f=0,!o||s?(a="y"===t.axis?i.width:0,f="x"===t.axis?i.height:0,d=l&&null!=t.x?t.x:d,m=c&&null!=t.y?t.y:m):o&&!s&&(f="x"===t.axis?i.height:f,a="y"===t.axis?i.width:a),o=!0,{width:a,height:f,x:d,y:m,top:m,right:d+a,bottom:m+f,left:d}}}}(i,{x:e,y:t,axis:a,dataRef:r,pointerType:p}))})),w=me((e=>{null==f&&null==d&&(n?v.current||y([]):b(e.clientX,e.clientY))})),E=se(p)?o:n,R=u.useCallback((()=>{if(!E||!c||null!=f||null!=d)return;const e=s(o);function t(n){const r=Q(n);$(o,r)?(e.removeEventListener("mousemove",t),v.current=null):b(n.clientX,n.clientY)}if(!r.current.openEvent||zt(r.current.openEvent)){e.addEventListener("mousemove",t);const n=()=>{e.removeEventListener("mousemove",t),v.current=null};return v.current=n,n}l.setPositionReference(i)}),[E,c,f,d,o,r,l,i,b]);u.useEffect((()=>R()),[R,h]),u.useEffect((()=>{c&&!o&&(m.current=!1)}),[c,o]),u.useEffect((()=>{!c&&n&&(m.current=!0)}),[c,n]),ae((()=>{!c||null==f&&null==d||(m.current=!1,b(f,d))}),[c,f,d,b]);const x=u.useMemo((()=>{function e(e){let{pointerType:t}=e;g(t)}return{onPointerDown:e,onPointerEnter:e,onMouseMove:w,onMouseEnter:w}}),[w]);return u.useMemo((()=>c?{reference:x}:{}),[c,x])},e.useDelayGroup=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,floatingId:o}=e,{id:u,enabled:i=!0}=t,l=null!=u?u:o,c=pt(),{currentId:s,setCurrentId:a,initialDelay:f,setState:d,timeoutMs:m}=c;return ae((()=>{i&&s&&(d({delay:{open:1,close:ft(f,"close")}}),s!==l&&r(!1))}),[i,l,r,d,s,f]),ae((()=>{function e(){r(!1),d({delay:f,currentId:null})}if(i&&s&&!n&&s===l){if(m){const t=window.setTimeout(e,m);return()=>{clearTimeout(t)}}e()}}),[i,n,d,s,l,r,f,m]),ae((()=>{i&&a!==mt&&n&&a(l)}),[i,n,a,l]),c},e.useDelayGroupContext=pt,e.useDismiss=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:o,elements:i,dataRef:l}=e,{enabled:c=!0,escapeKey:s=!0,outsidePress:a=!0,outsidePressEvent:m="pointerdown",referencePress:v=!1,referencePressEvent:y="pointerdown",ancestorScroll:b=!1,bubbles:w,capture:E}=t,R=lt(),x=me("function"==typeof a?a:()=>!1),I="function"==typeof a?x:a,k=u.useRef(!1),{escapeKey:C,outsidePress:M}=Vt(w),{escapeKey:O,outsidePress:S}=Vt(E),P=u.useRef(!1),T=u.useRef(-1),A=me((e=>{var t;if(!n||!c||!s||"Escape"!==e.key)return;if(P.current)return;const r=null==(t=l.current.floatingContext)?void 0:t.nodeId,u=R?oe(R.nodesRef.current,r):[];if(!C&&(e.stopPropagation(),u.length>0)){let e=!0;if(u.forEach((t=>{var n;null==(n=t.context)||!n.open||t.context.dataRef.current.__escapeKeyBubbles||(e=!1)})),!e)return}o(!1,function(e){return"nativeEvent"in e}(e)?e.nativeEvent:e,"escape-key")})),L=me((e=>{var t;const n=()=>{var t;A(e),null==(t=Q(e))||t.removeEventListener("keydown",n)};null==(t=Q(e))||t.addEventListener("keydown",n)})),N=me((e=>{var t;const n=l.current.insideReactTree;l.current.insideReactTree=!1;const r=k.current;if(k.current=!1,"click"===m&&r)return;if(n)return;if("function"==typeof I&&!I(e))return;const u=Q(e),c="["+ct("inert")+"]",s=ee(i.floating).querySelectorAll(c);let a=f(u)?u:null;for(;a&&!p(a);){const e=h(a);if(p(e)||!f(e))break;a=e}if(s.length&&f(u)&&!u.matches("html,body")&&!$(u,i.floating)&&Array.from(s).every((e=>!$(a,e))))return;if(d(u)&&j){const t=p(u),n=g(u),r=/auto|scroll/,o=t||r.test(n.overflowX),i=t||r.test(n.overflowY),l=o&&u.clientWidth>0&&u.scrollWidth>u.clientWidth,c=i&&u.clientHeight>0&&u.scrollHeight>u.clientHeight,s="rtl"===n.direction,a=c&&(s?e.offsetX<=u.offsetWidth-u.clientWidth:e.offsetX>u.clientWidth),f=l&&e.offsetY>u.clientHeight;if(a||f)return}const v=null==(t=l.current.floatingContext)?void 0:t.nodeId,y=R&&oe(R.nodesRef.current,v).some((t=>{var n;return J(e,null==(n=t.context)?void 0:n.elements.floating)}));if(J(e,i.floating)||J(e,i.domReference)||y)return;const b=R?oe(R.nodesRef.current,v):[];if(b.length>0){let e=!0;if(b.forEach((t=>{var n;null==(n=t.context)||!n.open||t.context.dataRef.current.__outsidePressBubbles||(e=!1)})),!e)return}o(!1,e,"outside-press")})),D=me((e=>{var t;const n=()=>{var t;N(e),null==(t=Q(e))||t.removeEventListener(m,n)};null==(t=Q(e))||t.addEventListener(m,n)}));u.useEffect((()=>{if(!n||!c)return;l.current.__escapeKeyBubbles=C,l.current.__outsidePressBubbles=M;let e=-1;function t(e){o(!1,e,"ancestor-scroll")}function u(){window.clearTimeout(e),P.current=!0}function a(){e=window.setTimeout((()=>{P.current=!1}),"undefined"!=typeof CSS&&CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")?5:0)}const d=ee(i.floating);s&&(d.addEventListener("keydown",O?L:A,O),d.addEventListener("compositionstart",u),d.addEventListener("compositionend",a)),I&&d.addEventListener(m,S?D:N,S);let v=[];return b&&(f(i.domReference)&&(v=r.getOverflowAncestors(i.domReference)),f(i.floating)&&(v=v.concat(r.getOverflowAncestors(i.floating))),!f(i.reference)&&i.reference&&i.reference.contextElement&&(v=v.concat(r.getOverflowAncestors(i.reference.contextElement)))),v=v.filter((e=>{var t;return e!==(null==(t=d.defaultView)?void 0:t.visualViewport)})),v.forEach((e=>{e.addEventListener("scroll",t,{passive:!0})})),()=>{s&&(d.removeEventListener("keydown",O?L:A,O),d.removeEventListener("compositionstart",u),d.removeEventListener("compositionend",a)),I&&d.removeEventListener(m,S?D:N,S),v.forEach((e=>{e.removeEventListener("scroll",t)})),window.clearTimeout(e)}}),[l,i,s,I,m,n,o,b,c,C,M,A,O,L,N,S,D]),u.useEffect((()=>{l.current.insideReactTree=!1}),[l,I,m]);const F=u.useMemo((()=>({onKeyDown:A,...v&&{[Xt[y]]:e=>{o(!1,e.nativeEvent,"reference-press")},..."click"!==y&&{onClick(e){o(!1,e.nativeEvent,"reference-press")}}}})),[A,o,v,y]),j=u.useMemo((()=>({onKeyDown:A,onMouseDown(){k.current=!0},onMouseUp(){k.current=!0},[Yt[m]]:()=>{l.current.insideReactTree=!0},onBlurCapture(){R||(st(T),l.current.insideReactTree=!0,T.current=window.setTimeout((()=>{l.current.insideReactTree=!1})))}})),[A,m,l,R]);return u.useMemo((()=>c?{reference:F,floating:j}:{}),[c,F,j])},e.useFloating=function(e){void 0===e&&(e={});const{nodeId:t}=e,n=Gt({...e,elements:{reference:null,floating:null,...e.elements}}),o=e.rootContext||n,i=o.elements,[l,c]=u.useState(null),[s,a]=u.useState(null),d=(null==i?void 0:i.domReference)||l,m=u.useRef(null),v=lt();ae((()=>{d&&(m.current=d)}),[d]);const p=r.useFloating({...e,elements:{...i,...s&&{reference:s}}}),g=u.useCallback((e=>{const t=f(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),getClientRects:()=>e.getClientRects(),contextElement:e}:e;a(t),p.refs.setReference(t)}),[p.refs]),h=u.useCallback((e=>{(f(e)||null===e)&&(m.current=e,c(e)),(f(p.refs.reference.current)||null===p.refs.reference.current||null!==e&&!f(e))&&p.refs.setReference(e)}),[p.refs]),y=u.useMemo((()=>({...p.refs,setReference:h,setPositionReference:g,domReference:m})),[p.refs,h,g]),b=u.useMemo((()=>({...p.elements,domReference:d})),[p.elements,d]),w=u.useMemo((()=>({...p,...o,refs:y,elements:b,nodeId:t})),[p,y,b,t,o]);return ae((()=>{o.dataRef.current.floatingContext=w;const e=null==v?void 0:v.nodesRef.current.find((e=>e.id===t));e&&(e.context=w)})),u.useMemo((()=>({...p,context:w,refs:y,elements:b})),[p,y,b,w])},e.useFloatingNodeId=function(e){const t=tt(),n=lt(),r=it(),o=e||r;return ae((()=>{if(!t)return;const e={id:t,parentId:o};return null==n||n.addNode(e),()=>{null==n||n.removeNode(e)}}),[n,t,o]),t},e.useFloatingParentNodeId=it,e.useFloatingPortalNode=At,e.useFloatingRootContext=Gt,e.useFloatingTree=lt,e.useFocus=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,events:o,dataRef:i,elements:l}=e,{enabled:c=!0,visibleOnly:a=!0}=t,m=u.useRef(!1),v=u.useRef(-1),p=u.useRef(!0);u.useEffect((()=>{if(!c)return;const e=s(l.domReference);function t(){!n&&d(l.domReference)&&l.domReference===Z(ee(l.domReference))&&(m.current=!0)}function r(){p.current=!0}function o(){p.current=!1}return e.addEventListener("blur",t),Zt()&&(e.addEventListener("keydown",r,!0),e.addEventListener("pointerdown",o,!0)),()=>{e.removeEventListener("blur",t),Zt()&&(e.removeEventListener("keydown",r,!0),e.removeEventListener("pointerdown",o,!0))}}),[l.domReference,n,c]),u.useEffect((()=>{if(c)return o.on("openchange",e),()=>{o.off("openchange",e)};function e(e){let{reason:t}=e;"reference-press"!==t&&"escape-key"!==t||(m.current=!0)}}),[o,c]),u.useEffect((()=>()=>{st(v)}),[]);const g=u.useMemo((()=>({onMouseLeave(){m.current=!1},onFocus(e){if(m.current)return;const t=Q(e.nativeEvent);if(a&&f(t))if(Zt()&&!e.relatedTarget){if(!p.current&&!te(t))return}else if(!function(e){if(!e||X())return!0;try{return e.matches(":focus-visible")}catch(e){return!0}}(t))return;r(!0,e.nativeEvent,"focus")},onBlur(e){m.current=!1;const t=e.relatedTarget,n=e.nativeEvent,o=f(t)&&t.hasAttribute(ct("focus-guard"))&&"outside"===t.getAttribute("data-type");v.current=window.setTimeout((()=>{var e;const u=Z(l.domReference?l.domReference.ownerDocument:document);(t||u!==l.domReference)&&($(null==(e=i.current.floatingContext)?void 0:e.refs.floating.current,u)||$(l.domReference,u)||o||r(!1,n,"focus"))}))}})),[i,l.domReference,r,a]);return u.useMemo((()=>c?{reference:g}:{}),[c,g])},e.useHover=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,dataRef:o,events:i,elements:l}=e,{enabled:c=!0,delay:s=0,handleClose:a=null,mouseOnly:d=!1,restMs:m=0,move:v=!0}=t,p=lt(),g=it(),h=fe(a),y=fe(s),b=fe(n),w=fe(m),E=u.useRef(),R=u.useRef(-1),x=u.useRef(),I=u.useRef(-1),k=u.useRef(!0),C=u.useRef(!1),M=u.useRef((()=>{})),O=u.useRef(!1),S=me((()=>{var e;const t=null==(e=o.current.openEvent)?void 0:e.type;return(null==t?void 0:t.includes("mouse"))&&"mousedown"!==t}));u.useEffect((()=>{if(c)return i.on("openchange",e),()=>{i.off("openchange",e)};function e(e){let{open:t}=e;t||(st(R),st(I),k.current=!0,O.current=!1)}}),[c,i]),u.useEffect((()=>{if(!c)return;if(!h.current)return;if(!n)return;function e(e){S()&&r(!1,e,"hover")}const t=ee(l.floating).documentElement;return t.addEventListener("mouseleave",e),()=>{t.removeEventListener("mouseleave",e)}}),[l.floating,n,r,c,h,S]);const P=u.useCallback((function(e,t,n){void 0===t&&(t=!0),void 0===n&&(n="hover");const o=ft(y.current,"close",E.current);o&&!x.current?(st(R),R.current=window.setTimeout((()=>r(!1,e,n)),o)):t&&(st(R),r(!1,e,n))}),[y,r]),T=me((()=>{M.current(),x.current=void 0})),A=me((()=>{if(C.current){const e=ee(l.floating).body;e.style.pointerEvents="",e.removeAttribute(at),C.current=!1}})),L=me((()=>!!o.current.openEvent&&["click","mousedown"].includes(o.current.openEvent.type)));u.useEffect((()=>{if(c&&f(l.domReference)){const r=l.domReference,o=l.floating;return n&&r.addEventListener("mouseleave",u),v&&r.addEventListener("mousemove",e,{once:!0}),r.addEventListener("mouseenter",e),r.addEventListener("mouseleave",t),o&&(o.addEventListener("mouseleave",u),o.addEventListener("mouseenter",i),o.addEventListener("mouseleave",s)),()=>{n&&r.removeEventListener("mouseleave",u),v&&r.removeEventListener("mousemove",e),r.removeEventListener("mouseenter",e),r.removeEventListener("mouseleave",t),o&&(o.removeEventListener("mouseleave",u),o.removeEventListener("mouseenter",i),o.removeEventListener("mouseleave",s))}}function e(e){if(st(R),k.current=!1,d&&!se(E.current)||dt(w.current)>0&&!ft(y.current,"open"))return;const t=ft(y.current,"open",E.current);t?R.current=window.setTimeout((()=>{b.current||r(!0,e,"hover")}),t):n||r(!0,e,"hover")}function t(e){if(L())return void A();M.current();const t=ee(l.floating);if(st(I),O.current=!1,h.current&&o.current.floatingContext){n||st(R),x.current=h.current({...o.current.floatingContext,tree:p,x:e.clientX,y:e.clientY,onClose(){A(),T(),L()||P(e,!0,"safe-polygon")}});const r=x.current;return t.addEventListener("mousemove",r),void(M.current=()=>{t.removeEventListener("mousemove",r)})}("touch"!==E.current||!$(l.floating,e.relatedTarget))&&P(e)}function u(e){L()||o.current.floatingContext&&(null==h.current||h.current({...o.current.floatingContext,tree:p,x:e.clientX,y:e.clientY,onClose(){A(),T(),L()||P(e)}})(e))}function i(){st(R)}function s(e){L()||P(e,!1)}}),[l,c,e,d,v,P,T,A,r,n,b,p,y,h,o,L,w]),ae((()=>{var e;if(c&&n&&null!=(e=h.current)&&null!=(e=e.__options)&&e.blockPointerEvents&&S()){C.current=!0;const e=l.floating;if(f(l.domReference)&&e){var t;const n=ee(l.floating).body;n.setAttribute(at,"");const r=l.domReference,o=null==p||null==(t=p.nodesRef.current.find((e=>e.id===g)))||null==(t=t.context)?void 0:t.elements.floating;return o&&(o.style.pointerEvents=""),n.style.pointerEvents="none",r.style.pointerEvents="auto",e.style.pointerEvents="auto",()=>{n.style.pointerEvents="",r.style.pointerEvents="",e.style.pointerEvents=""}}}}),[c,n,g,l,p,h,S]),ae((()=>{n||(E.current=void 0,O.current=!1,T(),A())}),[n,T,A]),u.useEffect((()=>()=>{T(),st(R),st(I),A()}),[c,l.domReference,T,A]);const N=u.useMemo((()=>{function e(e){E.current=e.pointerType}return{onPointerDown:e,onPointerEnter:e,onMouseMove(e){const{nativeEvent:t}=e;function o(){k.current||b.current||r(!0,t,"hover")}d&&!se(E.current)||n||0===dt(w.current)||O.current&&e.movementX**2+e.movementY**2<2||(st(I),"touch"===E.current?o():(O.current=!0,I.current=window.setTimeout(o,dt(w.current))))}}}),[d,r,n,b,w]);return u.useMemo((()=>c?{reference:N}:{}),[c,N])},e.useId=tt,e.useInnerOffset=function(e,t){const{open:n,elements:r}=e,{enabled:o=!0,overflowRef:l,scrollRef:c,onChange:s}=t,a=me(s),f=u.useRef(!1),d=u.useRef(null),m=u.useRef(null);u.useEffect((()=>{if(!o)return;function e(e){if(e.ctrlKey||!t||null==l.current)return;const n=e.deltaY,r=l.current.top>=-.5,o=l.current.bottom>=-.5,u=t.scrollHeight-t.clientHeight,c=n<0?-1:1,s=n<0?"max":"min";t.scrollHeight<=t.clientHeight||(!r&&n>0||!o&&n<0?(e.preventDefault(),i.flushSync((()=>{a((e=>e+Math[s](n,u*c)))}))):/firefox/i.test(B())&&(t.scrollTop+=n))}const t=(null==c?void 0:c.current)||r.floating;return n&&t?(t.addEventListener("wheel",e),requestAnimationFrame((()=>{d.current=t.scrollTop,null!=l.current&&(m.current={...l.current})})),()=>{d.current=null,m.current=null,t.removeEventListener("wheel",e)}):void 0}),[o,n,r.floating,l,c,a]);const v=u.useMemo((()=>({onKeyDown(){f.current=!0},onWheel(){f.current=!1},onPointerMove(){f.current=!1},onScroll(){const e=(null==c?void 0:c.current)||r.floating;if(l.current&&e&&f.current){if(null!==d.current){const t=e.scrollTop-d.current;(l.current.bottom<-.5&&t<-1||l.current.top<-.5&&t>1)&&i.flushSync((()=>a((e=>e+t))))}requestAnimationFrame((()=>{d.current=e.scrollTop}))}}})),[r.floating,a,l,c]);return u.useMemo((()=>o?{floating:v}:{}),[o,v])},e.useInteractions=function(e){void 0===e&&(e=[]);const t=e.map((e=>null==e?void 0:e.reference)),n=e.map((e=>null==e?void 0:e.floating)),r=e.map((e=>null==e?void 0:e.item)),o=u.useCallback((t=>$t(t,e,"reference")),t),i=u.useCallback((t=>$t(t,e,"floating")),n),l=u.useCallback((t=>$t(t,e,"item")),r);return u.useMemo((()=>({getReferenceProps:o,getFloatingProps:i,getItemProps:l})),[o,i,l])},e.useListItem=De,e.useListNavigation=function(e,t){const{open:n,onOpenChange:r,elements:o,floatingId:i}=e,{listRef:l,activeIndex:c,onNavigate:s=()=>{},enabled:a=!0,selectedIndex:f=null,allowEscape:m=!1,loop:v=!1,nested:p=!1,rtl:g=!1,virtual:h=!1,focusItemOnOpen:y="auto",focusItemOnHover:b=!0,openOnArrowKeyDown:w=!0,disabledIndices:E,orientation:R="vertical",parentOrientation:x,cols:I=1,scrollItemIntoView:k=!0,virtualItemRef:C,itemSizes:M,dense:O=!1}=t,S=fe(re(o.floating)),P=it(),T=lt();ae((()=>{e.dataRef.current.orientation=R}),[e,R]);const A=me((()=>{s(-1===D.current?null:D.current)})),L=ne(o.domReference),N=u.useRef(y),D=u.useRef(null!=f?f:-1),F=u.useRef(null),j=u.useRef(!0),K=u.useRef(A),H=u.useRef(!!o.floating),W=u.useRef(n),q=u.useRef(!1),_=u.useRef(!1),B=fe(E),U=fe(n),z=fe(k),X=fe(f),[Y,V]=u.useState(),[G,Q]=u.useState(),J=me((()=>{function e(e){var t;h?(null!=(t=e.id)&&t.endsWith("-fui-option")&&(e.id=i+"-"+Math.random().toString(16).slice(2,10)),V(e.id),null==T||T.events.emit("virtualfocus",e),C&&(C.current=e)):yt(e,{sync:q.current,preventScroll:!0})}const t=l.current[D.current],n=_.current;t&&e(t);(q.current?e=>e():requestAnimationFrame)((()=>{const r=l.current[D.current]||t;if(!r)return;t||e(r);const o=z.current;o&&ue&&(n||!j.current)&&(null==r.scrollIntoView||r.scrollIntoView("boolean"==typeof o?{block:"nearest",inline:"nearest"}:o))}))}));ae((()=>{a&&(n&&o.floating?N.current&&null!=f&&(_.current=!0,D.current=f,A()):H.current&&(D.current=-1,K.current()))}),[a,n,o.floating,f,A]),ae((()=>{if(a&&n&&o.floating)if(null==c){if(q.current=!1,null!=X.current)return;if(H.current&&(D.current=-1,J()),(!W.current||!H.current)&&N.current&&(null!=F.current||!0===N.current&&null==F.current)){let e=0;const t=()=>{if(null==l.current[0]){if(e<2){(e?requestAnimationFrame:queueMicrotask)(t)}e++}else D.current=null==F.current||en(F.current,R,g)||p?ge(l,B.current):he(l,B.current),F.current=null,A()};t()}}else pe(l,c)||(D.current=c,J(),_.current=!1)}),[a,n,o.floating,c,X,p,l,R,g,A,J,B]),ae((()=>{var e;if(!a||o.floating||!T||h||!H.current)return;const t=T.nodesRef.current,n=null==(e=t.find((e=>e.id===P)))||null==(e=e.context)?void 0:e.elements.floating,r=Z(ee(o.floating)),u=t.some((e=>e.context&&$(e.context.elements.floating,r)));n&&!u&&j.current&&n.focus({preventScroll:!0})}),[a,o.floating,T,P,h]),ae((()=>{if(a&&T&&h&&!P)return T.events.on("virtualfocus",e),()=>{T.events.off("virtualfocus",e)};function e(e){Q(e.id),C&&(C.current=e)}}),[a,T,h,P,C]),ae((()=>{K.current=A,W.current=n,H.current=!!o.floating})),ae((()=>{n||(F.current=null,N.current=y)}),[n,y]);const te=null!=c,ue=u.useMemo((()=>{function e(e){if(!U.current)return;const t=l.current.indexOf(e);-1!==t&&D.current!==t&&(D.current=t,A())}return{onFocus(t){let{currentTarget:n}=t;q.current=!0,e(n)},onClick:e=>{let{currentTarget:t}=e;return t.focus({preventScroll:!0})},...b&&{onMouseMove(t){let{currentTarget:n}=t;q.current=!0,_.current=!1,e(n)},onPointerLeave(e){let{pointerType:t}=e;var n;j.current&&"touch"!==t&&(q.current=!0,D.current=-1,A(),h||null==(n=S.current)||n.focus({preventScroll:!0}))}}}}),[U,S,b,l,A,h]),se=u.useCallback((()=>{var e;return null!=x?x:null==T||null==(e=T.nodesRef.current.find((e=>e.id===P)))||null==(e=e.context)||null==(e=e.dataRef)?void 0:e.current.orientation}),[P,T,x]),de=me((e=>{if(j.current=!1,q.current=!0,229===e.which)return;if(!U.current&&e.currentTarget===S.current)return;if(p&&nn(e.key,R,g,I))return Jt(e.key,se())||ie(e),r(!1,e.nativeEvent,"list-navigation"),void(d(o.domReference)&&(h?null==T||T.events.emit("virtualfocus",o.domReference):o.domReference.focus()));const t=D.current,u=ge(l,E),i=he(l,E);if(L||("Home"===e.key&&(ie(e),D.current=u,A()),"End"===e.key&&(ie(e),D.current=i,A())),I>1){const t=M||Array.from({length:l.current.length},(()=>({width:1,height:1}))),n=we(t,I,O),r=n.findIndex((e=>null!=e&&!xe(l,e,E))),o=n.reduce(((e,t,n)=>null==t||xe(l,t,E)?e:n),-1),c=n[be({current:n.map((e=>null!=e?l.current[e]:null))},{event:e,orientation:R,loop:v,rtl:g,cols:I,disabledIndices:Re([...("function"!=typeof E?E:null)||l.current.map(((e,t)=>xe(l,t,E)?t:void 0)),void 0],n),minIndex:r,maxIndex:o,prevIndex:Ee(D.current>i?u:D.current,t,n,I,e.key===_e?"bl":e.key===(g?He:We)?"tr":"tl"),stopEvent:!0})];if(null!=c&&(D.current=c,A()),"both"===R)return}if(Jt(e.key,R)){if(ie(e),n&&!h&&Z(e.currentTarget.ownerDocument)===e.currentTarget)return D.current=en(e.key,R,g)?u:i,void A();en(e.key,R,g)?D.current=v?t>=i?m&&t!==l.current.length?-1:u:ye(l,{startingIndex:t,disabledIndices:E}):Math.min(i,ye(l,{startingIndex:t,disabledIndices:E})):D.current=v?t<=u?m&&-1!==t?l.current.length:i:ye(l,{startingIndex:t,decrement:!0,disabledIndices:E}):Math.max(u,ye(l,{startingIndex:t,decrement:!0,disabledIndices:E})),pe(l,D.current)&&(D.current=-1),A()}})),ve=u.useMemo((()=>h&&n&&te&&{"aria-activedescendant":G||Y}),[h,n,te,G,Y]),Ie=u.useMemo((()=>({"aria-orientation":"both"===R?void 0:R,...L?{}:ve,onKeyDown:de,onPointerMove(){j.current=!0}})),[ve,de,R,L]),ke=u.useMemo((()=>{function e(e){"auto"===y&&le(e.nativeEvent)&&(N.current=!0)}function t(e){N.current=y,"auto"===y&&ce(e.nativeEvent)&&(N.current=!0)}return{...ve,onKeyDown(e){j.current=!1;const t=e.key.startsWith("Arrow"),o=["Home","End"].includes(e.key),u=t||o,i=tn(e.key,R,g),c=nn(e.key,R,g,I),s=tn(e.key,se(),g),a=Jt(e.key,R),d=(p?s:a)||"Enter"===e.key||""===e.key.trim();if(h&&n){const t=null==T?void 0:T.nodesRef.current.find((e=>null==e.parentId)),n=T&&t?function(e,t){let n,r=-1;return function t(o,u){u>r&&(n=o,r=u),oe(e,o).forEach((e=>{t(e.id,u+1)}))}(t,0),e.find((e=>e.id===n))}(T.nodesRef.current,t.id):null;if(u&&n&&C){const t=new KeyboardEvent("keydown",{key:e.key,bubbles:!0});if(i||c){var m,v;const r=(null==(m=n.context)?void 0:m.elements.domReference)===e.currentTarget,o=c&&!r?null==(v=n.context)?void 0:v.elements.domReference:i?l.current.find((e=>(null==e?void 0:e.id)===Y)):null;o&&(ie(e),o.dispatchEvent(t),Q(void 0))}var y;if((a||o)&&n.context)if(n.context.open&&n.parentId&&e.currentTarget!==n.context.elements.domReference)return ie(e),void(null==(y=n.context.elements.domReference)||y.dispatchEvent(t))}return de(e)}if(n||w||!t){if(d){const t=Jt(e.key,se());F.current=p&&t?null:e.key}p?s&&(ie(e),n?(D.current=ge(l,B.current),A()):r(!0,e.nativeEvent,"list-navigation")):a&&(null!=f&&(D.current=f),ie(e),!n&&w?r(!0,e.nativeEvent,"list-navigation"):de(e),n&&A())}},onFocus(){n&&!h&&(D.current=-1,A())},onPointerDown:t,onPointerEnter:t,onMouseDown:e,onClick:e}}),[Y,ve,I,de,B,y,l,p,A,r,n,w,R,se,g,f,T,h,C]);return u.useMemo((()=>a?{reference:ke,floating:Ie,item:ue}:{}),[a,ke,Ie,ue])},e.useMergeRefs=Te,e.useNextDelayGroup=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,floatingId:o}=e,{enabled:i=!0}=t,l=u.useContext(gt),{currentIdRef:c,delayRef:s,timeoutMs:a,initialDelayRef:f,currentContextRef:d,hasProvider:m,timeoutIdRef:v}=l,[p,g]=u.useState(!1);return ae((()=>{function e(){var e;g(!1),null==(e=d.current)||e.setIsInstantPhase(!1),c.current=null,d.current=null,s.current=f.current}if(i&&c.current&&!n&&c.current===o){if(g(!1),a)return v.current=window.setTimeout(e,a),()=>{clearTimeout(v.current)};e()}}),[i,n,o,c,s,a,f,d,v]),ae((()=>{if(!i)return;if(!n)return;const e=d.current,t=c.current;d.current={onOpenChange:r,setIsInstantPhase:g},c.current=o,s.current={open:0,close:ft(f.current,"close")},null!==t&&t!==o?(st(v),g(!0),null==e||e.setIsInstantPhase(!0),null==e||e.onOpenChange(!1)):(g(!1),null==e||e.setIsInstantPhase(!1))}),[i,n,o,r,c,s,a,f,d,v]),ae((()=>()=>{d.current=null}),[d]),u.useMemo((()=>({hasProvider:m,delayRef:s,isInstantPhase:p})),[m,s,p])},e.useRole=function(e,t){var n,r;void 0===t&&(t={});const{open:o,elements:i,floatingId:l}=e,{enabled:c=!0,role:s="dialog"}=t,a=tt(),f=(null==(n=i.domReference)?void 0:n.id)||a,d=u.useMemo((()=>{var e;return(null==(e=re(i.floating))?void 0:e.id)||l}),[i.floating,l]),m=null!=(r=rn.get(s))?r:s,v=null!=it(),p=u.useMemo((()=>"tooltip"===m||"label"===s?{["aria-"+("label"===s?"labelledby":"describedby")]:o?d:void 0}:{"aria-expanded":o?"true":"false","aria-haspopup":"alertdialog"===m?"dialog":m,"aria-controls":o?d:void 0,..."listbox"===m&&{role:"combobox"},..."menu"===m&&{id:f},..."menu"===m&&v&&{role:"menuitem"},..."select"===s&&{"aria-autocomplete":"none"},..."combobox"===s&&{"aria-autocomplete":"list"}}),[m,d,v,o,f,s]),g=u.useMemo((()=>{const e={id:d,...m&&{role:m}};return"tooltip"===m||"label"===s?e:{...e,..."menu"===m&&{"aria-labelledby":f}}}),[m,d,f,s]),h=u.useCallback((e=>{let{active:t,selected:n}=e;const r={role:"option",...t&&{id:d+"-fui-option"}};switch(s){case"select":return{...r,"aria-selected":t&&n};case"combobox":return{...r,"aria-selected":n}}return{}}),[d,s]);return u.useMemo((()=>c?{reference:p,floating:g,item:h}:{}),[c,p,g,h])},e.useTransitionStatus=ln,e.useTransitionStyles=function(e,t){void 0===t&&(t={});const{initial:n={opacity:0},open:r,close:o,common:i,duration:l=250}=t,c=e.placement,s=c.split("-")[0],a=u.useMemo((()=>({side:s,placement:c})),[s,c]),f="number"==typeof l,d=(f?l:l.open)||0,m=(f?l:l.close)||0,[v,p]=u.useState((()=>({...un(i,a),...un(n,a)}))),{isMounted:g,status:h}=ln(e,{duration:l}),y=fe(n),b=fe(r),w=fe(o),E=fe(i);return ae((()=>{const e=un(y.current,a),t=un(w.current,a),n=un(E.current,a),r=un(b.current,a)||Object.keys(e).reduce(((e,t)=>(e[t]="",e)),{});if("initial"===h&&p((t=>({transitionProperty:t.transitionProperty,...n,...e}))),"open"===h&&p({transitionProperty:Object.keys(r).map(on).join(","),transitionDuration:d+"ms",...n,...r}),"close"===h){const r=t||e;p({transitionProperty:Object.keys(r).map(on).join(","),transitionDuration:m+"ms",...n,...r})}}),[m,w,y,b,E,d,h,a]),{isMounted:g,styles:v}},e.useTypeahead=function(e,t){var n;const{open:r,dataRef:o}=e,{listRef:i,activeIndex:l,onMatch:c,onTypingChange:s,enabled:a=!0,findMatch:f=null,resetMs:d=750,ignoreKeys:m=[],selectedIndex:v=null}=t,p=u.useRef(-1),g=u.useRef(""),h=u.useRef(null!=(n=null!=v?v:l)?n:-1),y=u.useRef(null),b=me(c),w=me(s),E=fe(f),R=fe(m);ae((()=>{r&&(st(p),y.current=null,g.current="")}),[r]),ae((()=>{var e;r&&""===g.current&&(h.current=null!=(e=null!=v?v:l)?e:-1)}),[r,v,l]);const x=me((e=>{e?o.current.typing||(o.current.typing=e,w(e)):o.current.typing&&(o.current.typing=e,w(e))})),I=me((e=>{function t(e,t,n){const r=E.current?E.current(t,n):t.find((e=>0===(null==e?void 0:e.toLocaleLowerCase().indexOf(n.toLocaleLowerCase()))));return r?e.indexOf(r):-1}const n=i.current;if(g.current.length>0&&" "!==g.current[0]&&(-1===t(n,n,g.current)?x(!1):" "===e.key&&ie(e)),null==n||R.current.includes(e.key)||1!==e.key.length||e.ctrlKey||e.metaKey||e.altKey)return;r&&" "!==e.key&&(ie(e),x(!0));n.every((e=>{var t,n;return!e||(null==(t=e[0])?void 0:t.toLocaleLowerCase())!==(null==(n=e[1])?void 0:n.toLocaleLowerCase())}))&&g.current===e.key&&(g.current="",h.current=y.current),g.current+=e.key,st(p),p.current=window.setTimeout((()=>{g.current="",h.current=y.current,x(!1)}),d);const o=h.current,u=t(n,[...n.slice((o||0)+1),...n.slice(0,(o||0)+1)],g.current);-1!==u?(b(u),y.current=u):" "!==e.key&&(g.current="",x(!1))})),k=u.useMemo((()=>({onKeyDown:I})),[I]),C=u.useMemo((()=>({onKeyDown:I,onKeyUp(e){" "===e.key&&x(!1)}})),[I,x]);return u.useMemo((()=>a?{reference:k,floating:C}:{}),[a,k,C])}}));
