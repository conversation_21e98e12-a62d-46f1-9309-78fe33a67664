{"name": "@emoji-mart/react", "version": "1.1.1", "description": "React wrapper for Emoji Mart; the emoji picker for the web.", "license": "MIT", "homepage": "https://missiveapp.com/open/emoji-mart", "repository": {"type": "git", "url": "https://github.com/missive/emoji-mart", "directory": "packages/emoji-mart-react"}, "source": "react.tsx", "types": "dist/index.d.ts", "main": "dist/main.js", "module": "dist/module.js", "scripts": {"build": "parcel build --no-autoinstall", "prepublishOnly": "yarn build"}, "peerDependencies": {"emoji-mart": "^5.2", "react": "^16.8 || ^17 || ^18"}, "files": ["/dist", "LICENSE"]}