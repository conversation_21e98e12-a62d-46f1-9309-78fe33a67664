{"mappings": ";;;;;;;;;;;;;;;;AACA;;AAGe,kDAAqB,KAAK,EAAE;IACzC,MAAM,GAAG,GAAG,CAAA,GAAA,mBAAM,CAAA,CAAC,IAAI,CAAC;IACxB,MAAM,QAAQ,GAAG,CAAA,GAAA,mBAAM,CAAA,CAAC,IAAI,CAAC;IAE7B,IAAI,QAAQ,CAAC,OAAO,EAClB,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;IAGhC,CAAA,GAAA,sBAAS,CAAA,CAAC,IAAM;QACd,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAA,GAAA,uBAAM,CAAA,CAAC;YAAE,GAAG,KAAK;iBAAE,GAAG;SAAE,CAAC;QAEhD,OAAO,IAAM;YACX,QAAQ,CAAC,OAAO,GAAG,IAAI;SACxB,CAAA;KACF,EAAE,EAAE,CAAC;IAEN,qBAAO,CAAA,GAAA,sCAAK,CAAA,CAAC,aAAa,CAAC,KAAK,EAAE;aAAE,GAAG;KAAE,CAAC,CAAA;CAC3C", "sources": ["packages/emoji-mart-react/react.tsx"], "sourcesContent": ["// @ts-nocheck\nimport React, { useEffect, useRef } from 'react'\nimport { Picker } from 'emoji-mart'\n\nexport default function EmojiPicker(props) {\n  const ref = useRef(null)\n  const instance = useRef(null)\n\n  if (instance.current) {\n    instance.current.update(props)\n  }\n\n  useEffect(() => {\n    instance.current = new Picker({ ...props, ref })\n\n    return () => {\n      instance.current = null\n    }\n  }, [])\n\n  return React.createElement('div', { ref })\n}\n"], "names": [], "version": 3, "file": "main.js.map"}