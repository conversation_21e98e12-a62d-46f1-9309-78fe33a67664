{"mappings": "AAIA,oCAAoC,KAAK,KAAA,OAiBxC", "sources": ["packages/emoji-mart-react/react.tsx"], "sourcesContent": ["// @ts-nocheck\nimport React, { useEffect, useRef } from 'react'\nimport { Picker } from 'emoji-mart'\n\nexport default function EmojiPicker(props) {\n  const ref = useRef(null)\n  const instance = useRef(null)\n\n  if (instance.current) {\n    instance.current.update(props)\n  }\n\n  useEffect(() => {\n    instance.current = new Picker({ ...props, ref })\n\n    return () => {\n      instance.current = null\n    }\n  }, [])\n\n  return React.createElement('div', { ref })\n}\n"], "names": [], "version": 3, "file": "index.d.ts.map"}