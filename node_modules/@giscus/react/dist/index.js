import { jsx as b } from "react/jsx-runtime";
import { useState as h, useEffect as j } from "react";
function y({ id: e, host: i, repo: o, repoId: r, category: n, categoryId: u, mapping: s, term: a, strict: d, reactionsEnabled: f, emitMetadata: m, inputPosition: c, theme: p, lang: l, loading: g }) {
  const [t, x] = h(!1);
  return j(() => {
    t || import("./giscus-Ci9LqPcC.js").then(() => x(!0));
  }, []), t ? b("giscus-widget", { id: e, host: i, repo: o, repoid: r, category: n, categoryid: u, mapping: s, term: a, strict: d, reactionsenabled: f, emitmetadata: m, inputposition: c, theme: p, lang: l, loading: g }) : null;
}
export {
  y as default
};
