{"name": "@giscus/react", "version": "3.1.0", "types": "dist/index.d.ts", "type": "module", "exports": "./dist/index.js", "files": ["dist"], "repository": {"type": "git", "url": "https://github.com/giscus/giscus-component.git", "directory": "react"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint src", "lint:fix": "eslint src --fix", "format": "prettier . --check", "format:fix": "prettier . --write"}, "dependencies": {"giscus": "^1.6.0"}, "peerDependencies": {"react": "^16 || ^17 || ^18 || ^19", "react-dom": "^16 || ^17 || ^18 || ^19"}, "devDependencies": {"@rollup/plugin-typescript": "^12.1.2", "@types/node": "^22.10.2", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "@typescript-eslint/eslint-plugin": "^8.19.0", "@typescript-eslint/parser": "^8.19.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.37.3", "prettier": "^3.4.2", "tslib": "^2.8.1", "typescript": "^5.7.2", "vite": "^6.0.6"}}