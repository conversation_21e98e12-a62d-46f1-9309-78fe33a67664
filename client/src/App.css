/* Main App Styles for SahAI CEP Extension V2 */
/* Using Adobe CEP Theme Variables */

.sahai-app {
  width: 100%;
  height: 100vh;
  background: var(--adobe-bg-primary);
  color: var(--adobe-text-primary);
  font-family: var(--adobe-font-family);
  overflow: hidden;
}

.app-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Main content area */
.main-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0; /* Allow flex shrinking */
}



/* Adobe CEP Theme Variables are now in globals.css */

/* Responsive design for different CEP panel sizes */
@media (max-width: 400px) {
  /* Mobile-specific adjustments handled by individual components */
}

@media (max-height: 500px) {
  /* Height-specific adjustments handled by individual components */
}

/* High DPI support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* High DPI adjustments handled by individual components */
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
}

/* Focus management for keyboard navigation */
.sahai-app:focus-within {
  outline: none;
}

/* Scrollbar styling for CEP */
.main-content::-webkit-scrollbar {
  width: 8px;
}

.main-content::-webkit-scrollbar-track {
  background: var(--adobe-bg-primary);
}

.main-content::-webkit-scrollbar-thumb {
  background: var(--adobe-border);
  border-radius: 4px;
}

.main-content::-webkit-scrollbar-thumb:hover {
  background: var(--adobe-text-secondary);
}
