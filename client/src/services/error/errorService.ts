/**
 * Error Handling Service for SahAI V2
 * Centralized error handling, classification, and recovery strategies
 */

import { ApiError, ApiErrorData } from '../../types/api';

export interface ErrorContext {
  providerId?: string;
  operation?: string;
  timestamp: Date;
  userAgent?: string;
  url?: string;
  metadata?: Record<string, any>;
}

export interface ErrorReport {
  id: string;
  error: ApiError | Error;
  context: ErrorContext;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'network' | 'authentication' | 'rate-limit' | 'api' | 'validation' | 'unknown';
  recoveryStrategy?: string;
  userMessage?: string;
  technicalMessage?: string;
}

export interface ErrorRecoveryStrategy {
  name: string;
  description: string;
  canRecover: (error: Error, context: ErrorContext) => boolean;
  recover: (error: Error, context: ErrorContext) => Promise<any>;
  maxAttempts: number;
}

export class ErrorService {
  private errorHistory: ErrorReport[] = [];
  private recoveryStrategies: ErrorRecoveryStrategy[] = [];
  private listeners = new Set<(report: ErrorReport) => void>();
  private maxHistorySize = 1000;

  constructor() {
    this.initializeRecoveryStrategies();
  }

  /**
   * Handle and classify an error
   */
  handleError(error: Error | ApiError, context: Partial<ErrorContext> = {}): ErrorReport {
    const fullContext: ErrorContext = {
      timestamp: new Date(),
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Unknown',
      url: typeof window !== 'undefined' ? window.location.href : 'Unknown',
      ...context,
    };

    const report: ErrorReport = {
      id: this.generateErrorId(),
      error,
      context: fullContext,
      severity: this.classifySeverity(error),
      category: this.classifyCategory(error),
      recoveryStrategy: this.findRecoveryStrategy(error, fullContext),
      userMessage: this.generateUserMessage(error),
      technicalMessage: this.generateTechnicalMessage(error),
    };

    // Add to history
    this.addToHistory(report);

    // Notify listeners
    this.notifyListeners(report);

    // Log error
    this.logError(report);

    return report;
  }

  /**
   * Attempt to recover from an error
   */
  async attemptRecovery(report: ErrorReport): Promise<any> {
    const strategy = this.recoveryStrategies.find(s => s.name === report.recoveryStrategy);
    
    if (!strategy) {
      throw new Error(`No recovery strategy found: ${report.recoveryStrategy}`);
    }

    if (!strategy.canRecover(report.error, report.context)) {
      throw new Error(`Recovery strategy ${strategy.name} cannot handle this error`);
    }

    console.log(`🔄 Attempting recovery with strategy: ${strategy.name}`);
    
    try {
      const result = await strategy.recover(report.error, report.context);
      console.log(`✅ Recovery successful with strategy: ${strategy.name}`);
      return result;
    } catch (recoveryError) {
      console.error(`❌ Recovery failed with strategy: ${strategy.name}`, recoveryError);
      throw recoveryError;
    }
  }

  /**
   * Get error statistics
   */
  getErrorStats(): {
    totalErrors: number;
    errorsByCategory: Record<string, number>;
    errorsBySeverity: Record<string, number>;
    errorsByProvider: Record<string, number>;
    recentErrors: ErrorReport[];
  } {
    const stats = {
      totalErrors: this.errorHistory.length,
      errorsByCategory: {} as Record<string, number>,
      errorsBySeverity: {} as Record<string, number>,
      errorsByProvider: {} as Record<string, number>,
      recentErrors: this.errorHistory.slice(-10),
    };

    this.errorHistory.forEach(report => {
      // Count by category
      stats.errorsByCategory[report.category] = (stats.errorsByCategory[report.category] || 0) + 1;
      
      // Count by severity
      stats.errorsBySeverity[report.severity] = (stats.errorsBySeverity[report.severity] || 0) + 1;
      
      // Count by provider
      if (report.context.providerId) {
        stats.errorsByProvider[report.context.providerId] = (stats.errorsByProvider[report.context.providerId] || 0) + 1;
      }
    });

    return stats;
  }

  /**
   * Add error listener
   */
  addListener(listener: (report: ErrorReport) => void): void {
    this.listeners.add(listener);
  }

  /**
   * Remove error listener
   */
  removeListener(listener: (report: ErrorReport) => void): void {
    this.listeners.delete(listener);
  }

  /**
   * Clear error history
   */
  clearHistory(): void {
    this.errorHistory = [];
  }

  /**
   * Get error by ID
   */
  getError(id: string): ErrorReport | null {
    return this.errorHistory.find(report => report.id === id) || null;
  }

  /**
   * Private helper methods
   */
  private initializeRecoveryStrategies(): void {
    // Network error recovery
    this.recoveryStrategies.push({
      name: 'network-retry',
      description: 'Retry network requests with exponential backoff',
      canRecover: (error) => {
        const message = error.message.toLowerCase();
        return message.includes('network') || 
               message.includes('fetch') || 
               message.includes('timeout') ||
               message.includes('connection');
      },
      recover: async (error, context) => {
        // This would implement actual retry logic
        await this.sleep(1000);
        throw new Error('Retry not implemented in this example');
      },
      maxAttempts: 3,
    });

    // Authentication error recovery
    this.recoveryStrategies.push({
      name: 'auth-refresh',
      description: 'Refresh authentication credentials',
      canRecover: (error) => {
        return error instanceof ApiError && error.code === 'AUTHENTICATION_FAILED';
      },
      recover: async (error, context) => {
        // This would implement auth refresh logic
        throw new Error('Auth refresh not implemented in this example');
      },
      maxAttempts: 1,
    });

    // Rate limit recovery
    this.recoveryStrategies.push({
      name: 'rate-limit-wait',
      description: 'Wait for rate limit reset',
      canRecover: (error) => {
        return error instanceof ApiError && error.code === 'RATE_LIMITED';
      },
      recover: async (error, context) => {
        // Wait for rate limit reset
        await this.sleep(60000); // Wait 1 minute
        throw new Error('Rate limit wait not implemented in this example');
      },
      maxAttempts: 1,
    });
  }

  private classifySeverity(error: Error | ApiError): 'low' | 'medium' | 'high' | 'critical' {
    if (error instanceof ApiError) {
      switch (error.code) {
        case 'AUTHENTICATION_FAILED':
        case 'ADAPTER_NOT_FOUND':
          return 'critical';
        case 'RATE_LIMITED':
        case 'CIRCUIT_BREAKER_OPEN':
          return 'high';
        case 'TIMEOUT':
        case 'NETWORK_ERROR':
          return 'medium';
        default:
          return 'low';
      }
    }

    const message = error.message.toLowerCase();
    if (message.includes('critical') || message.includes('fatal')) return 'critical';
    if (message.includes('error') || message.includes('failed')) return 'high';
    if (message.includes('warning') || message.includes('timeout')) return 'medium';
    return 'low';
  }

  private classifyCategory(error: Error | ApiError): 'network' | 'authentication' | 'rate-limit' | 'api' | 'validation' | 'unknown' {
    if (error instanceof ApiError) {
      switch (error.code) {
        case 'AUTHENTICATION_FAILED':
          return 'authentication';
        case 'RATE_LIMITED':
          return 'rate-limit';
        case 'NETWORK_ERROR':
        case 'TIMEOUT':
          return 'network';
        case 'API_ERROR':
          return 'api';
        default:
          return 'unknown';
      }
    }

    const message = error.message.toLowerCase();
    if (message.includes('auth') || message.includes('unauthorized')) return 'authentication';
    if (message.includes('rate') || message.includes('limit')) return 'rate-limit';
    if (message.includes('network') || message.includes('timeout')) return 'network';
    if (message.includes('validation') || message.includes('invalid')) return 'validation';
    if (message.includes('api')) return 'api';
    return 'unknown';
  }

  private findRecoveryStrategy(error: Error, context: ErrorContext): string | undefined {
    const strategy = this.recoveryStrategies.find(s => s.canRecover(error, context));
    return strategy?.name;
  }

  private generateUserMessage(error: Error | ApiError): string {
    if (error instanceof ApiError) {
      switch (error.code) {
        case 'AUTHENTICATION_FAILED':
          return 'Please check your API key and try again.';
        case 'RATE_LIMITED':
          return 'Too many requests. Please wait a moment and try again.';
        case 'NETWORK_ERROR':
          return 'Network connection issue. Please check your internet connection.';
        case 'TIMEOUT':
          return 'Request timed out. Please try again.';
        default:
          return 'An error occurred. Please try again.';
      }
    }

    return 'An unexpected error occurred. Please try again.';
  }

  private generateTechnicalMessage(error: Error | ApiError): string {
    if (error instanceof ApiError) {
      return `${error.code}: ${error.message}`;
    }
    return `${error.name}: ${error.message}`;
  }

  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private addToHistory(report: ErrorReport): void {
    this.errorHistory.push(report);
    
    // Trim history if too large
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(-this.maxHistorySize);
    }
  }

  private notifyListeners(report: ErrorReport): void {
    this.listeners.forEach(listener => {
      try {
        listener(report);
      } catch (error) {
        console.error('Error listener failed:', error);
      }
    });
  }

  private logError(report: ErrorReport): void {
    const logLevel = report.severity === 'critical' ? 'error' : 
                    report.severity === 'high' ? 'warn' : 'log';
    
    console[logLevel](`[${report.severity.toUpperCase()}] ${report.category}:`, {
      id: report.id,
      message: report.technicalMessage,
      context: report.context,
      error: report.error,
    });
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Singleton instance
export const errorService = new ErrorService();
