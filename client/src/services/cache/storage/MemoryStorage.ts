/**
 * Memory Storage Adapter for Cache Service
 * In-memory storage with TTL support and LRU eviction
 */

import { CacheStorage, CacheEntry, MemoryStorageConfig } from '../../../types/cache';

export class MemoryStorage implements CacheStorage {
  private store = new Map<string, CacheEntry>();
  private accessOrder = new Map<string, number>(); // For LRU tracking
  private accessCounter = 0;
  private config: MemoryStorageConfig;
  private gcInterval?: NodeJS.Timeout;

  constructor(config: Partial<MemoryStorageConfig> = {}) {
    this.config = {
      maxSize: 1000,
      maxMemory: 50 * 1024 * 1024, // 50MB
      gcInterval: 60000, // 1 minute
      ...config,
    };

    this.startGarbageCollection();
  }

  /**
   * Get cache entry by key
   */
  async get<T>(key: string): Promise<CacheEntry<T> | null> {
    const entry = this.store.get(key) as CacheEntry<T> | undefined;
    
    if (!entry) {
      return null;
    }

    // Check TTL
    const now = Date.now();
    const age = now - entry.createdAt.getTime();
    
    if (entry.ttl !== Infinity && age > entry.ttl) {
      // Expired, remove it
      this.store.delete(key);
      this.accessOrder.delete(key);
      return null;
    }

    // Update access tracking for LRU
    this.accessOrder.set(key, ++this.accessCounter);
    entry.lastAccessed = new Date();
    entry.accessCount++;

    return entry;
  }

  /**
   * Set cache entry
   */
  async set<T>(key: string, value: T, ttl: number): Promise<void> {
    // Check if we need to evict entries first
    await this.enforceMemoryLimits();

    const entry: CacheEntry<T> = {
      key,
      value,
      ttl,
      createdAt: new Date(),
      lastAccessed: new Date(),
      accessCount: 1,
      size: this.calculateSize(value),
    };

    this.store.set(key, entry);
    this.accessOrder.set(key, ++this.accessCounter);
  }

  /**
   * Delete cache entry
   */
  async delete(key: string): Promise<boolean> {
    const deleted = this.store.delete(key);
    this.accessOrder.delete(key);
    return deleted;
  }

  /**
   * Clear all entries
   */
  async clear(): Promise<void> {
    this.store.clear();
    this.accessOrder.clear();
    this.accessCounter = 0;
  }

  /**
   * Get all keys
   */
  async keys(): Promise<string[]> {
    return Array.from(this.store.keys());
  }

  /**
   * Get total number of entries
   */
  async size(): Promise<number> {
    return this.store.size;
  }

  /**
   * Check if key exists
   */
  async has(key: string): Promise<boolean> {
    const entry = await this.get(key);
    return entry !== null;
  }

  /**
   * Get memory usage statistics
   */
  getMemoryUsage(): { totalSize: number; totalMemory: number; entryCount: number } {
    let totalMemory = 0;
    let entryCount = 0;

    for (const entry of this.store.values()) {
      totalMemory += entry.size;
      entryCount++;
    }

    return {
      totalSize: this.store.size,
      totalMemory,
      entryCount,
    };
  }

  /**
   * Get entries sorted by access time (LRU)
   */
  getLRUEntries(): Array<{ key: string; accessOrder: number; lastAccessed: Date }> {
    const entries: Array<{ key: string; accessOrder: number; lastAccessed: Date }> = [];
    
    for (const [key, entry] of this.store.entries()) {
      const accessOrder = this.accessOrder.get(key) || 0;
      entries.push({
        key,
        accessOrder,
        lastAccessed: entry.lastAccessed,
      });
    }

    return entries.sort((a, b) => a.accessOrder - b.accessOrder);
  }

  /**
   * Cleanup expired entries
   */
  async cleanup(): Promise<number> {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, entry] of this.store.entries()) {
      const age = now - entry.createdAt.getTime();
      
      if (entry.ttl !== Infinity && age > entry.ttl) {
        this.store.delete(key);
        this.accessOrder.delete(key);
        cleaned++;
      }
    }

    return cleaned;
  }

  /**
   * Destroy storage and cleanup
   */
  destroy(): void {
    this.stopGarbageCollection();
    this.store.clear();
    this.accessOrder.clear();
  }

  /**
   * Private helper methods
   */
  private calculateSize(value: any): number {
    try {
      // Rough estimation of memory usage
      const jsonString = JSON.stringify(value);
      return jsonString.length * 2; // Assuming UTF-16 encoding
    } catch {
      return 1024; // Default size if serialization fails
    }
  }

  private async enforceMemoryLimits(): Promise<void> {
    const usage = this.getMemoryUsage();
    
    // Check size limit
    if (usage.totalSize >= this.config.maxSize) {
      await this.evictLRUEntries(Math.floor(this.config.maxSize * 0.1)); // Evict 10%
    }

    // Check memory limit
    if (usage.totalMemory >= this.config.maxMemory) {
      await this.evictLRUEntries(Math.floor(usage.entryCount * 0.1)); // Evict 10%
    }
  }

  private async evictLRUEntries(count: number): Promise<void> {
    const lruEntries = this.getLRUEntries();
    const toEvict = lruEntries.slice(0, count);

    for (const { key } of toEvict) {
      this.store.delete(key);
      this.accessOrder.delete(key);
    }
  }

  private startGarbageCollection(): void {
    this.gcInterval = setInterval(async () => {
      try {
        const cleaned = await this.cleanup();
        if (cleaned > 0) {
          console.log(`🗑️ Cache GC: Cleaned ${cleaned} expired entries`);
        }
      } catch (error) {
        console.error('Cache garbage collection error:', error);
      }
    }, this.config.gcInterval);
  }

  private stopGarbageCollection(): void {
    if (this.gcInterval) {
      clearInterval(this.gcInterval);
      this.gcInterval = undefined;
    }
  }
}

// Factory function for easy instantiation
export function createMemoryStorage(config?: Partial<MemoryStorageConfig>): MemoryStorage {
  return new MemoryStorage(config);
}
