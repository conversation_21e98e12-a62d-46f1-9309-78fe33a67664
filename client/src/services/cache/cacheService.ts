/**
 * Intelligent Cache Service for SahAI V2
 * Implements TTL-based caching with LRU eviction and background refresh
 */

import {
  CacheService as ICacheService,
  CacheEntry,
  CacheStorage,
  CacheStrategy,
  CacheStats,
  CacheEvent,
  CacheEventListener,
  CACHE_STRATEGIES,
  CACHE_KEY_PATTERNS,
} from '../../types/cache';

export class CacheService implements ICacheService {
  private storage: CacheStorage;
  private strategies = new Map<string, CacheStrategy>();
  private listeners = new Map<string, Set<CacheEventListener>>();
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    evictions: 0,
    refreshes: 0,
    errors: 0,
    hitRate: 0,
    totalSize: 0,
    totalMemory: 0,
    averageAccessTime: 0,
  };
  private refreshInterval?: NodeJS.Timeout;

  constructor(storage: CacheStorage) {
    this.storage = storage;
    this.initializeStrategies();
  }

  /**
   * Initialize default cache strategies
   */
  private initializeStrategies(): void {
    Object.entries(CACHE_STRATEGIES).forEach(([name, strategy]) => {
      this.strategies.set(name, strategy);
    });
  }

  /**
   * Get value from cache
   */
  async get<T>(key: string): Promise<T | null> {
    const startTime = Date.now();
    
    try {
      const entry = await this.storage.get<T>(key);
      
      if (!entry) {
        this.stats.misses++;
        this.emit('miss', { key, timestamp: new Date() });
        return null;
      }

      // Check TTL
      const now = Date.now();
      const age = now - entry.createdAt.getTime();
      
      if (age > entry.ttl) {
        // Expired, remove and return null
        await this.storage.delete(key);
        this.stats.misses++;
        this.emit('miss', { key, timestamp: new Date() });
        return null;
      }

      // Update access info
      entry.lastAccessed = new Date();
      entry.accessCount++;
      await this.storage.set(key, entry.value, entry.ttl);

      // Check if refresh is needed
      const strategy = this.getStrategy(key);
      const refreshThreshold = entry.ttl * strategy.refreshThreshold;

      if (strategy.backgroundRefresh && age > refreshThreshold) {
        this.scheduleRefresh(key);
      }

      this.stats.hits++;
      this.stats.averageAccessTime = (this.stats.averageAccessTime + (Date.now() - startTime)) / 2;
      this.updateHitRate();

      this.emit('hit', { key, timestamp: new Date() });
      return entry.value;

    } catch (error) {
      this.stats.errors++;
      this.emit('error', {
        key,
        timestamp: new Date(),
        metadata: { error: error instanceof Error ? error.message : 'Unknown error' }
      });
      return null;
    }
  }

  /**
   * Set value in cache
   */
  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    try {
      const strategy = this.getStrategy(key);
      const finalTtl = ttl || strategy.ttl;
      
      // Check memory limits before setting
      await this.enforceMemoryLimits(strategy);
      
      const entry: CacheEntry<T> = {
        key,
        value,
        ttl: finalTtl,
        createdAt: new Date(),
        lastAccessed: new Date(),
        accessCount: 1,
        size: this.calculateSize(value),
      };

      await this.storage.set(key, value, finalTtl);
      
      this.stats.sets++;
      this.stats.totalSize++;
      this.stats.totalMemory += entry.size;
      
      this.emit('set', { key, timestamp: new Date() });

    } catch (error) {
      this.stats.errors++;
      this.emit('error', {
        key,
        timestamp: new Date(),
        metadata: { error: error instanceof Error ? error.message : 'Unknown error' }
      });
      throw error;
    }
  }

  /**
   * Delete value from cache
   */
  async delete(key: string): Promise<boolean> {
    try {
      const deleted = await this.storage.delete(key);
      
      if (deleted) {
        this.stats.deletes++;
        this.stats.totalSize--;
        this.emit('delete', { key, timestamp: new Date() });
      }
      
      return deleted;
    } catch (error) {
      this.stats.errors++;
      return false;
    }
  }

  /**
   * Clear all cache entries
   */
  async clear(): Promise<void> {
    try {
      await this.storage.clear();
      this.resetStats();
    } catch (error) {
      this.stats.errors++;
      throw error;
    }
  }

  /**
   * Invalidate cache entries matching pattern
   */
  async invalidate(pattern: string): Promise<number> {
    try {
      const keys = await this.storage.keys();
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      let invalidated = 0;
      
      for (const key of keys) {
        if (regex.test(key)) {
          await this.delete(key);
          invalidated++;
        }
      }
      
      return invalidated;
    } catch (error) {
      this.stats.errors++;
      return 0;
    }
  }

  /**
   * Refresh cache entry
   */
  async refresh(key: string): Promise<void> {
    try {
      // This would typically involve calling the original data source
      // For now, we just emit the refresh event
      this.stats.refreshes++;
      this.emit('refresh', { key, timestamp: new Date() });
    } catch (error) {
      this.stats.errors++;
      throw error;
    }
  }

  /**
   * Get multiple values
   */
  async mget<T>(keys: string[]): Promise<(T | null)[]> {
    const results: (T | null)[] = [];
    
    for (const key of keys) {
      results.push(await this.get<T>(key));
    }
    
    return results;
  }

  /**
   * Set multiple values
   */
  async mset<T>(entries: Array<{ key: string; value: T; ttl?: number }>): Promise<void> {
    for (const entry of entries) {
      await this.set(entry.key, entry.value, entry.ttl);
    }
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<CacheStats> {
    this.stats.totalSize = (await this.storage.keys()).length;
    return { ...this.stats };
  }

  /**
   * Get strategy for key
   */
  getStrategy(key: string): CacheStrategy {
    // Find matching pattern
    for (const [patternName, pattern] of Object.entries(CACHE_KEY_PATTERNS)) {
      const regex = new RegExp(pattern.pattern.replace(/\{[^}]+\}/g, '[^:]+'));
      if (regex.test(key)) {
        return this.strategies.get(pattern.strategy) || CACHE_STRATEGIES.models;
      }
    }
    
    return CACHE_STRATEGIES.models; // Default strategy
  }

  /**
   * Set custom strategy
   */
  setStrategy(pattern: string, strategy: Partial<CacheStrategy>): void {
    const existing = this.strategies.get(pattern) || CACHE_STRATEGIES.models;
    this.strategies.set(pattern, { ...existing, ...strategy });
  }

  /**
   * Event handling
   */
  on(event: CacheEvent['type'], listener: CacheEventListener): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(listener);
  }

  off(event: CacheEvent['type'], listener: CacheEventListener): void {
    const listeners = this.listeners.get(event);
    if (listeners) {
      listeners.delete(listener);
    }
  }

  /**
   * Start background refresh
   */
  startBackgroundRefresh(): void {
    if (this.refreshInterval) return;
    
    this.refreshInterval = setInterval(async () => {
      await this.performBackgroundRefresh();
    }, 30000); // Every 30 seconds
  }

  /**
   * Stop background refresh
   */
  stopBackgroundRefresh(): void {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = undefined;
    }
  }

  /**
   * Generate cache key from pattern
   */
  generateKey(pattern: string, params: Record<string, string>): string {
    let key = pattern;
    for (const [param, value] of Object.entries(params)) {
      key = key.replace(`{${param}}`, value);
    }
    return key;
  }

  /**
   * Hash API key for cache key
   */
  hashApiKey(apiKey: string): string {
    // Simple hash function for API keys
    let hash = 0;
    for (let i = 0; i < apiKey.length; i++) {
      const char = apiKey.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Private helper methods
   */
  private emit(type: CacheEvent['type'], event: { key: string; timestamp: Date; metadata?: Record<string, any> }): void {
    const listeners = this.listeners.get(type);
    if (listeners) {
      const fullEvent: CacheEvent = { type, ...event };
      listeners.forEach(listener => {
        try {
          listener(fullEvent);
        } catch (error) {
          console.error('Cache event listener error:', error);
        }
      });
    }
  }

  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? this.stats.hits / total : 0;
  }

  private calculateSize(value: any): number {
    // Rough estimation of object size in bytes
    return JSON.stringify(value).length * 2; // Assuming UTF-16
  }

  private async enforceMemoryLimits(strategy: CacheStrategy): Promise<void> {
    const currentSize = await this.storage.size();
    
    if (currentSize >= strategy.maxSize) {
      await this.evictEntries(strategy, Math.floor(strategy.maxSize * 0.1)); // Evict 10%
    }
  }

  private async evictEntries(strategy: CacheStrategy, count: number): Promise<void> {
    const keys = await this.storage.keys();
    
    // Simple LRU eviction - in a real implementation, you'd track access times
    const keysToEvict = keys.slice(0, count);
    
    for (const key of keysToEvict) {
      await this.storage.delete(key);
      this.stats.evictions++;
      this.emit('evict', { key, timestamp: new Date() });
    }
  }

  private scheduleRefresh(key: string): void {
    // In a real implementation, this would add to a refresh queue
    setTimeout(() => {
      this.refresh(key).catch(console.error);
    }, 1000);
  }

  private async performBackgroundRefresh(): Promise<void> {
    // Implementation would check for entries needing refresh
    // and update them from their original sources
  }

  private resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      evictions: 0,
      refreshes: 0,
      errors: 0,
      hitRate: 0,
      totalSize: 0,
      totalMemory: 0,
      averageAccessTime: 0,
    };
  }
}
