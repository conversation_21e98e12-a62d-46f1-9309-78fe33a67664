/**
 * Performance Monitor Service for SahAI CEP Extension
 * Provides performance monitoring and optimization for CEP environment
 */

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  category: 'render' | 'network' | 'memory' | 'user';
}

interface PerformanceReport {
  metrics: PerformanceMetric[];
  summary: {
    averageRenderTime: number;
    memoryUsage: number;
    networkLatency: number;
    userInteractions: number;
  };
  recommendations: string[];
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private observers: PerformanceObserver[] = [];
  private isEnabled: boolean = true;
  private maxMetrics: number = 1000; // Limit stored metrics for memory efficiency

  constructor() {
    this.initializeObservers();
  }

  /**
   * Initialize performance observers for CEP environment
   */
  private initializeObservers(): void {
    try {
      // Only initialize if PerformanceObserver is available
      if (typeof PerformanceObserver !== 'undefined') {
        // Observe paint timing
        const paintObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordMetric({
              name: entry.name,
              value: entry.startTime,
              timestamp: Date.now(),
              category: 'render',
            });
          }
        });
        
        try {
          paintObserver.observe({ entryTypes: ['paint'] });
          this.observers.push(paintObserver);
        } catch (error) {
          console.warn('Paint observer not supported:', error);
        }

        // Observe navigation timing
        const navigationObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordMetric({
              name: 'navigation',
              value: entry.duration,
              timestamp: Date.now(),
              category: 'network',
            });
          }
        });
        
        try {
          navigationObserver.observe({ entryTypes: ['navigation'] });
          this.observers.push(navigationObserver);
        } catch (error) {
          console.warn('Navigation observer not supported:', error);
        }
      }
    } catch (error) {
      console.warn('Performance observers not available in CEP environment:', error);
    }
  }

  /**
   * Record a custom performance metric
   */
  recordMetric(metric: PerformanceMetric): void {
    if (!this.isEnabled) return;

    this.metrics.push(metric);

    // Limit stored metrics to prevent memory issues
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }
  }

  /**
   * Measure execution time of a function
   */
  measureFunction<T>(name: string, fn: () => T): T {
    const startTime = performance.now();
    const result = fn();
    const endTime = performance.now();

    this.recordMetric({
      name,
      value: endTime - startTime,
      timestamp: Date.now(),
      category: 'render',
    });

    return result;
  }

  /**
   * Measure execution time of an async function
   */
  async measureAsyncFunction<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const startTime = performance.now();
    const result = await fn();
    const endTime = performance.now();

    this.recordMetric({
      name,
      value: endTime - startTime,
      timestamp: Date.now(),
      category: 'network',
    });

    return result;
  }

  /**
   * Record user interaction timing
   */
  recordUserInteraction(action: string, duration: number): void {
    this.recordMetric({
      name: `user_${action}`,
      value: duration,
      timestamp: Date.now(),
      category: 'user',
    });
  }

  /**
   * Get memory usage information
   */
  getMemoryUsage(): number {
    try {
      // Try to get memory info if available
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        return memory.usedJSHeapSize / 1024 / 1024; // Convert to MB
      }
    } catch (error) {
      console.warn('Memory API not available:', error);
    }
    return 0;
  }

  /**
   * Generate performance report
   */
  generateReport(): PerformanceReport {
    const renderMetrics = this.metrics.filter(m => m.category === 'render');
    const networkMetrics = this.metrics.filter(m => m.category === 'network');
    const userMetrics = this.metrics.filter(m => m.category === 'user');

    const averageRenderTime = renderMetrics.length > 0
      ? renderMetrics.reduce((sum, m) => sum + m.value, 0) / renderMetrics.length
      : 0;

    const networkLatency = networkMetrics.length > 0
      ? networkMetrics.reduce((sum, m) => sum + m.value, 0) / networkMetrics.length
      : 0;

    const memoryUsage = this.getMemoryUsage();

    const recommendations: string[] = [];

    // Generate recommendations based on metrics
    if (averageRenderTime > 100) {
      recommendations.push('Consider optimizing render performance - average render time is high');
    }

    if (networkLatency > 1000) {
      recommendations.push('Network requests are slow - consider caching or optimization');
    }

    if (memoryUsage > 50) {
      recommendations.push('Memory usage is high - consider cleanup or optimization');
    }

    if (this.metrics.length > this.maxMetrics * 0.8) {
      recommendations.push('Performance metrics storage is near capacity');
    }

    return {
      metrics: [...this.metrics],
      summary: {
        averageRenderTime,
        memoryUsage,
        networkLatency,
        userInteractions: userMetrics.length,
      },
      recommendations,
    };
  }

  /**
   * Clear all stored metrics
   */
  clearMetrics(): void {
    this.metrics = [];
  }

  /**
   * Enable or disable performance monitoring
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * Cleanup observers and resources
   */
  dispose(): void {
    this.observers.forEach(observer => {
      try {
        observer.disconnect();
      } catch (error) {
        console.warn('Error disconnecting performance observer:', error);
      }
    });
    this.observers = [];
    this.clearMetrics();
  }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();

// Hook for React components
export function usePerformanceMonitor() {
  return {
    recordMetric: (metric: PerformanceMetric) => performanceMonitor.recordMetric(metric),
    measureFunction: <T>(name: string, fn: () => T) => performanceMonitor.measureFunction(name, fn),
    measureAsyncFunction: <T>(name: string, fn: () => Promise<T>) => performanceMonitor.measureAsyncFunction(name, fn),
    recordUserInteraction: (action: string, duration: number) => performanceMonitor.recordUserInteraction(action, duration),
    generateReport: () => performanceMonitor.generateReport(),
    clearMetrics: () => performanceMonitor.clearMetrics(),
  };
}

export default performanceMonitor;
