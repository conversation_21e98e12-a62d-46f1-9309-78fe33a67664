/**
 * Ollama Provider Adapter for SahAI V2
 * Implements Ollama local API integration
 */

import { 
  ProviderAdapter, 
  AuthConfig, 
  RawModelInfo, 
  ModelInfo, 
  HealthStatus,
  OllamaConfig 
} from '../../../types/api';

export class OllamaAdapter implements ProviderAdapter {
  readonly providerId = 'ollama';
  readonly name = 'Ollama';
  readonly baseUrl: string;
  readonly authType = 'custom' as const;

  constructor(baseUrl: string = 'http://localhost:11434') {
    this.baseUrl = baseUrl;
  }

  /**
   * Authenticate with Ollama (no auth required for local)
   */
  async authenticate(config: AuthConfig): Promise<boolean> {
    try {
      // Ollama doesn't require authentication, just check if it's running
      const response = await fetch(`${this.baseUrl}/api/version`, {
        method: 'GET',
      });

      return response.ok;
    } catch (error) {
      console.error('Ollama connection failed:', error);
      return false;
    }
  }

  /**
   * Discover available models from Ollama API
   */
  async discoverModels(config: AuthConfig): Promise<RawModelInfo[]> {
    const response = await fetch(`${this.baseUrl}/api/tags`, {
      method: 'GET',
    });

    if (!response.ok) {
      throw new Error(`Ollama API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    // Transform Ollama format to our standard format
    return (data.models || []).map((model: any) => ({
      id: model.name,
      name: model.name,
      object: 'model',
      created: new Date(model.modified_at).getTime() / 1000,
      owned_by: 'ollama',
      size: model.size,
      digest: model.digest,
      details: model.details,
    }));
  }

  /**
   * Check Ollama API health
   */
  async checkHealth(config?: AuthConfig): Promise<HealthStatus> {
    const startTime = Date.now();
    
    try {
      const response = await fetch(`${this.baseUrl}/api/version`, {
        method: 'GET',
      });

      const latency = Date.now() - startTime;
      
      if (response.ok) {
        const version = await response.json();
        return {
          status: 'healthy',
          latency,
          lastCheck: new Date(),
          errorRate: 0,
          uptime: 1,
          details: {
            endpoint: '/api/version',
            responseTime: latency,
          },
        };
      } else {
        return {
          status: 'degraded',
          latency,
          lastCheck: new Date(),
          errorRate: 0.5,
          uptime: 0.5,
          details: {
            endpoint: '/api/version',
            responseTime: latency,
            errorMessage: `HTTP ${response.status}: ${response.statusText}`,
          },
        };
      }
    } catch (error) {
      const latency = Date.now() - startTime;
      
      return {
        status: 'down',
        latency,
        lastCheck: new Date(),
        errorRate: 1,
        uptime: 0,
        details: {
          endpoint: '/api/version',
          responseTime: latency,
          errorMessage: error instanceof Error ? error.message : 'Ollama not running',
        },
      };
    }
  }

  /**
   * Normalize Ollama model data to standard format
   */
  normalizeModel(raw: RawModelInfo): ModelInfo {
    const modelName = raw.id || raw.name || 'Unknown';
    
    // Extract model family and size from name
    const getModelInfo = (name: string) => {
      const lowerName = name.toLowerCase();
      
      // Common Ollama model patterns
      if (lowerName.includes('llama')) {
        if (lowerName.includes('3.1')) return { family: 'llama-3.1', size: this.extractSize(name) };
        if (lowerName.includes('3')) return { family: 'llama-3', size: this.extractSize(name) };
        if (lowerName.includes('2')) return { family: 'llama-2', size: this.extractSize(name) };
        return { family: 'llama', size: this.extractSize(name) };
      }
      
      if (lowerName.includes('mistral')) return { family: 'mistral', size: this.extractSize(name) };
      if (lowerName.includes('mixtral')) return { family: 'mixtral', size: this.extractSize(name) };
      if (lowerName.includes('codellama')) return { family: 'codellama', size: this.extractSize(name) };
      if (lowerName.includes('vicuna')) return { family: 'vicuna', size: this.extractSize(name) };
      if (lowerName.includes('orca')) return { family: 'orca', size: this.extractSize(name) };
      if (lowerName.includes('phi')) return { family: 'phi', size: this.extractSize(name) };
      if (lowerName.includes('gemma')) return { family: 'gemma', size: this.extractSize(name) };
      
      return { family: 'unknown', size: this.extractSize(name) };
    };

    const { family, size } = getModelInfo(modelName);

    // Model capabilities based on family
    const getCapabilities = (family: string): string[] => {
      const capabilities: string[] = ['text', 'conversation'];
      
      switch (family) {
        case 'llama-3.1':
        case 'llama-3':
          capabilities.push('reasoning', 'code', 'analysis', 'multilingual');
          break;
        case 'codellama':
          capabilities.push('code', 'programming', 'analysis');
          break;
        case 'mistral':
        case 'mixtral':
          capabilities.push('reasoning', 'multilingual', 'code');
          break;
        case 'phi':
          capabilities.push('reasoning', 'code', 'compact');
          break;
        case 'gemma':
          capabilities.push('reasoning', 'safety', 'instruction-following');
          break;
        default:
          capabilities.push('general');
      }
      
      return capabilities;
    };

    // Context length estimation
    const getContextLength = (family: string, size: string): number => {
      if (family.includes('llama-3.1')) return 131072; // 128K
      if (family.includes('llama-3')) return 8192;
      if (family.includes('mixtral')) return 32768;
      if (family.includes('mistral')) return 8192;
      if (family.includes('codellama')) return 16384;
      if (family.includes('gemma')) return 8192;
      return 4096; // Default
    };

    return {
      id: modelName,
      name: this.formatModelName(modelName),
      description: this.getModelDescription(family, size),
      contextLength: getContextLength(family, size),
      inputCost: 0, // Local models are free
      outputCost: 0,
      capabilities: getCapabilities(family),
      provider: this.providerId,
      isAvailable: true,
      lastUpdated: new Date(),
      metadata: {
        family,
        size,
        digest: raw.digest,
        localSize: raw.size,
        details: raw.details,
        original: raw,
      },
    };
  }

  /**
   * Validate Ollama connection (no API key needed)
   */
  async validateApiKey(apiKey: string): Promise<boolean> {
    // Ollama doesn't use API keys, just check if it's running
    return await this.authenticate({ type: 'custom' });
  }

  /**
   * Private helper methods
   */
  private extractSize(modelName: string): string {
    const sizeMatch = modelName.match(/(\d+(?:\.\d+)?)[bB]/);
    return sizeMatch ? `${sizeMatch[1]}B` : 'Unknown';
  }

  private formatModelName(modelName: string): string {
    // Clean up model name for display
    return modelName
      .replace(/[_-]/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase())
      .replace(/\s+/g, ' ')
      .trim();
  }

  private getModelDescription(family: string, size: string): string {
    const descriptions: Record<string, string> = {
      'llama-3.1': `Latest Llama 3.1 model (${size}) with enhanced capabilities`,
      'llama-3': `Llama 3 model (${size}) for general-purpose tasks`,
      'llama-2': `Llama 2 model (${size}) for conversation and reasoning`,
      'codellama': `Code Llama model (${size}) specialized for programming`,
      'mistral': `Mistral model (${size}) for efficient reasoning`,
      'mixtral': `Mixtral model (${size}) with mixture of experts architecture`,
      'phi': `Microsoft Phi model (${size}) optimized for efficiency`,
      'gemma': `Google Gemma model (${size}) with safety focus`,
      'vicuna': `Vicuna model (${size}) fine-tuned for conversation`,
      'orca': `Orca model (${size}) for reasoning and analysis`,
    };

    return descriptions[family] || `Local ${family} model (${size})`;
  }

  /**
   * Get available models with additional details
   */
  async getModelDetails(modelId: string, config: AuthConfig): Promise<RawModelInfo> {
    const response = await fetch(`${this.baseUrl}/api/show`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ name: modelId }),
    });

    if (!response.ok) {
      throw new Error(`Failed to get model details: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return {
      id: data.name || modelId,
      name: data.name || modelId,
      object: 'model',
      created: new Date(data.modified_at).getTime() / 1000,
      owned_by: 'ollama',
      size: data.size,
      digest: data.digest,
      details: data.details,
      modelfile: data.modelfile,
      parameters: data.parameters,
      template: data.template,
    };
  }

  /**
   * Pull a model from Ollama registry
   */
  async pullModel(modelName: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/api/pull`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ name: modelName }),
    });

    if (!response.ok) {
      throw new Error(`Failed to pull model: ${response.status} ${response.statusText}`);
    }

    // This would typically handle streaming response for progress
    // For now, we just wait for completion
    await response.json();
  }
}
