/**
 * OpenAI Provider Adapter for SahAI V2
 * Implements real OpenAI API integration
 */

import { 
  Provider<PERSON>dapter, 
  AuthConfig, 
  RawModelInfo, 
  ModelInfo, 
  HealthStatus,
  OpenAIConfig 
} from '../../../types/api';

export class OpenAIAdapter implements ProviderAdapter {
  readonly providerId = 'openai';
  readonly name = 'OpenAI';
  readonly baseUrl = 'https://api.openai.com/v1';
  readonly authType = 'bearer' as const;

  /**
   * Authenticate with OpenAI API
   */
  async authenticate(config: AuthConfig): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/models`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${config.token}`,
          'Content-Type': 'application/json',
        },
      });

      return response.ok;
    } catch (error) {
      console.error('OpenAI authentication failed:', error);
      return false;
    }
  }

  /**
   * Discover available models from OpenAI API
   */
  async discoverModels(config: AuthConfig): Promise<RawModelInfo[]> {
    const response = await fetch(`${this.baseUrl}/models`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${config.token}`,
        'Content-Type': 'application/json',
        ...(config as OpenAIConfig).organization && {
          'OpenAI-Organization': (config as OpenAIConfig).organization!
        },
        ...(config as OpenAIConfig).project && {
          'OpenAI-Project': (config as OpenAIConfig).project!
        },
      },
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.data || [];
  }

  /**
   * Check OpenAI API health
   */
  async checkHealth(config?: AuthConfig): Promise<HealthStatus> {
    const startTime = Date.now();
    
    try {
      const response = await fetch(`${this.baseUrl}/models`, {
        method: 'GET',
        headers: {
          ...(config?.token && { 'Authorization': `Bearer ${config.token}` }),
          'Content-Type': 'application/json',
        },
      });

      const latency = Date.now() - startTime;
      
      if (response.ok) {
        return {
          status: 'healthy',
          latency,
          lastCheck: new Date(),
          errorRate: 0,
          uptime: 1,
          details: {
            endpoint: '/v1/models',
            responseTime: latency,
          },
        };
      } else {
        return {
          status: 'degraded',
          latency,
          lastCheck: new Date(),
          errorRate: 0.5,
          uptime: 0.5,
          details: {
            endpoint: '/v1/models',
            responseTime: latency,
            errorMessage: `HTTP ${response.status}: ${response.statusText}`,
          },
        };
      }
    } catch (error) {
      const latency = Date.now() - startTime;
      
      return {
        status: 'down',
        latency,
        lastCheck: new Date(),
        errorRate: 1,
        uptime: 0,
        details: {
          endpoint: '/v1/models',
          responseTime: latency,
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Normalize OpenAI model data to standard format
   */
  normalizeModel(raw: RawModelInfo): ModelInfo {
    // OpenAI model capabilities mapping
    const getCapabilities = (modelId: string): string[] => {
      const capabilities: string[] = ['text'];
      
      if (modelId.includes('gpt-4')) {
        capabilities.push('reasoning', 'code', 'analysis');
        if (modelId.includes('vision') || modelId.includes('turbo')) {
          capabilities.push('vision');
        }
      } else if (modelId.includes('gpt-3.5')) {
        capabilities.push('conversation', 'code');
      } else if (modelId.includes('dall-e')) {
        capabilities.push('image-generation');
      } else if (modelId.includes('whisper')) {
        capabilities.push('speech-to-text');
      } else if (modelId.includes('tts')) {
        capabilities.push('text-to-speech');
      }
      
      return capabilities;
    };

    // Context length mapping
    const getContextLength = (modelId: string): number => {
      if (modelId.includes('gpt-4-turbo') || modelId.includes('gpt-4-1106')) return 128000;
      if (modelId.includes('gpt-4-32k')) return 32768;
      if (modelId.includes('gpt-4')) return 8192;
      if (modelId.includes('gpt-3.5-turbo-16k')) return 16384;
      if (modelId.includes('gpt-3.5-turbo')) return 4096;
      return 4096; // Default
    };

    // Pricing information (approximate, in USD per 1K tokens)
    const getPricing = (modelId: string): { input: number; output: number } => {
      if (modelId.includes('gpt-4-turbo')) return { input: 0.01, output: 0.03 };
      if (modelId.includes('gpt-4-32k')) return { input: 0.06, output: 0.12 };
      if (modelId.includes('gpt-4')) return { input: 0.03, output: 0.06 };
      if (modelId.includes('gpt-3.5-turbo')) return { input: 0.001, output: 0.002 };
      return { input: 0.001, output: 0.002 }; // Default
    };

    const pricing = getPricing(raw.id);
    
    return {
      id: raw.id,
      name: this.formatModelName(raw.id),
      description: this.getModelDescription(raw.id),
      contextLength: getContextLength(raw.id),
      inputCost: pricing.input,
      outputCost: pricing.output,
      capabilities: getCapabilities(raw.id),
      provider: this.providerId,
      isAvailable: true,
      lastUpdated: new Date(),
      metadata: {
        owned_by: raw.owned_by,
        created: raw.created,
        object: raw.object,
        original: raw,
      },
    };
  }

  /**
   * Validate OpenAI API key format
   */
  async validateApiKey(apiKey: string): Promise<boolean> {
    // OpenAI API keys start with 'sk-' and are 51 characters long
    if (!apiKey.startsWith('sk-') || apiKey.length !== 51) {
      return false;
    }

    // Test with a simple API call
    try {
      return await this.authenticate({ type: 'bearer', token: apiKey });
    } catch {
      return false;
    }
  }

  /**
   * Format model name for display
   */
  private formatModelName(modelId: string): string {
    const nameMap: Record<string, string> = {
      'gpt-4': 'GPT-4',
      'gpt-4-turbo': 'GPT-4 Turbo',
      'gpt-4-turbo-preview': 'GPT-4 Turbo Preview',
      'gpt-4-1106-preview': 'GPT-4 Turbo (1106)',
      'gpt-4-0125-preview': 'GPT-4 Turbo (0125)',
      'gpt-4-vision-preview': 'GPT-4 Vision',
      'gpt-4-32k': 'GPT-4 32K',
      'gpt-3.5-turbo': 'GPT-3.5 Turbo',
      'gpt-3.5-turbo-16k': 'GPT-3.5 Turbo 16K',
      'gpt-3.5-turbo-1106': 'GPT-3.5 Turbo (1106)',
      'dall-e-3': 'DALL-E 3',
      'dall-e-2': 'DALL-E 2',
      'whisper-1': 'Whisper',
      'tts-1': 'Text-to-Speech',
      'tts-1-hd': 'Text-to-Speech HD',
    };

    return nameMap[modelId] || modelId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Get model description
   */
  private getModelDescription(modelId: string): string {
    const descriptions: Record<string, string> = {
      'gpt-4': 'Most capable GPT-4 model, great for complex tasks',
      'gpt-4-turbo': 'Faster and more efficient GPT-4 with 128K context',
      'gpt-4-turbo-preview': 'Preview of the latest GPT-4 Turbo model',
      'gpt-4-vision-preview': 'GPT-4 with vision capabilities for image analysis',
      'gpt-4-32k': 'GPT-4 with extended 32K context window',
      'gpt-3.5-turbo': 'Fast, efficient model for most conversational tasks',
      'gpt-3.5-turbo-16k': 'GPT-3.5 with extended 16K context window',
      'dall-e-3': 'Advanced image generation model',
      'dall-e-2': 'Image generation model',
      'whisper-1': 'Speech recognition and transcription',
      'tts-1': 'Convert text to natural-sounding speech',
      'tts-1-hd': 'High-definition text-to-speech conversion',
    };

    return descriptions[modelId] || 'OpenAI language model';
  }
}
