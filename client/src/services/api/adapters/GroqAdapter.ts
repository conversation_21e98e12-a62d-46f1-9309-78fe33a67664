/**
 * Groq Provider Adapter for SahAI V2
 * Implements Groq API integration (OpenAI-compatible)
 */

import { 
  ProviderAdapter, 
  AuthConfig, 
  RawModelInfo, 
  ModelInfo, 
  HealthStatus,
  GroqConfig 
} from '../../../types/api';

export class GroqAdapter implements ProviderAdapter {
  readonly providerId = 'groq';
  readonly name = 'Groq';
  readonly baseUrl = 'https://api.groq.com/openai/v1';
  readonly authType = 'bearer' as const;

  /**
   * Authenticate with Groq API
   */
  async authenticate(config: AuthConfig): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/models`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${config.token}`,
          'Content-Type': 'application/json',
        },
      });

      return response.ok;
    } catch (error) {
      console.error('Groq authentication failed:', error);
      return false;
    }
  }

  /**
   * Discover available models from Groq API
   */
  async discoverModels(config: AuthConfig): Promise<RawModelInfo[]> {
    const response = await fetch(`${this.baseUrl}/models`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${config.token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Groq API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.data || [];
  }

  /**
   * Check Groq API health
   */
  async checkHealth(config?: AuthConfig): Promise<HealthStatus> {
    const startTime = Date.now();
    
    try {
      const response = await fetch(`${this.baseUrl}/models`, {
        method: 'GET',
        headers: {
          ...(config?.token && { 'Authorization': `Bearer ${config.token}` }),
          'Content-Type': 'application/json',
        },
      });

      const latency = Date.now() - startTime;
      
      if (response.ok) {
        return {
          status: 'healthy',
          latency,
          lastCheck: new Date(),
          errorRate: 0,
          uptime: 1,
          details: {
            endpoint: '/openai/v1/models',
            responseTime: latency,
          },
        };
      } else {
        return {
          status: 'degraded',
          latency,
          lastCheck: new Date(),
          errorRate: 0.5,
          uptime: 0.5,
          details: {
            endpoint: '/openai/v1/models',
            responseTime: latency,
            errorMessage: `HTTP ${response.status}: ${response.statusText}`,
          },
        };
      }
    } catch (error) {
      const latency = Date.now() - startTime;
      
      return {
        status: 'down',
        latency,
        lastCheck: new Date(),
        errorRate: 1,
        uptime: 0,
        details: {
          endpoint: '/openai/v1/models',
          responseTime: latency,
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Normalize Groq model data to standard format
   */
  normalizeModel(raw: RawModelInfo): ModelInfo {
    // Groq model capabilities
    const getCapabilities = (modelId: string): string[] => {
      const capabilities: string[] = ['text', 'conversation'];
      
      if (modelId.includes('llama')) {
        capabilities.push('reasoning', 'code', 'analysis');
      } else if (modelId.includes('mixtral')) {
        capabilities.push('reasoning', 'multilingual', 'code');
      } else if (modelId.includes('gemma')) {
        capabilities.push('reasoning', 'safety');
      }
      
      return capabilities;
    };

    // Context length mapping for Groq models
    const getContextLength = (modelId: string): number => {
      if (modelId.includes('32k')) return 32768;
      if (modelId.includes('8k')) return 8192;
      if (modelId.includes('llama-3.1')) return 131072; // 128K
      if (modelId.includes('llama-3')) return 8192;
      if (modelId.includes('mixtral')) return 32768;
      if (modelId.includes('gemma')) return 8192;
      return 8192; // Default
    };

    // Groq pricing (very competitive, approximate USD per 1K tokens)
    const getPricing = (modelId: string): { input: number; output: number } => {
      if (modelId.includes('llama-3.1-70b')) return { input: 0.00059, output: 0.00079 };
      if (modelId.includes('llama-3.1-8b')) return { input: 0.00005, output: 0.00008 };
      if (modelId.includes('llama-3-70b')) return { input: 0.00059, output: 0.00079 };
      if (modelId.includes('llama-3-8b')) return { input: 0.00005, output: 0.00008 };
      if (modelId.includes('mixtral-8x7b')) return { input: 0.00024, output: 0.00024 };
      if (modelId.includes('gemma-7b')) return { input: 0.00007, output: 0.00007 };
      return { input: 0.0001, output: 0.0001 }; // Default very low pricing
    };

    const pricing = getPricing(raw.id);
    
    return {
      id: raw.id,
      name: this.formatModelName(raw.id),
      description: this.getModelDescription(raw.id),
      contextLength: getContextLength(raw.id),
      inputCost: pricing.input,
      outputCost: pricing.output,
      capabilities: getCapabilities(raw.id),
      provider: this.providerId,
      isAvailable: true,
      lastUpdated: new Date(),
      metadata: {
        owned_by: raw.owned_by,
        created: raw.created,
        object: raw.object,
        original: raw,
      },
    };
  }

  /**
   * Validate Groq API key format
   */
  async validateApiKey(apiKey: string): Promise<boolean> {
    // Groq API keys start with 'gsk_' and are longer
    if (!apiKey.startsWith('gsk_') || apiKey.length < 40) {
      return false;
    }

    // Test with actual API call
    try {
      return await this.authenticate({ type: 'bearer', token: apiKey });
    } catch {
      return false;
    }
  }

  /**
   * Format model name for display
   */
  private formatModelName(modelId: string): string {
    const nameMap: Record<string, string> = {
      'llama-3.1-405b-reasoning': 'Llama 3.1 405B Reasoning',
      'llama-3.1-70b-versatile': 'Llama 3.1 70B Versatile',
      'llama-3.1-8b-instant': 'Llama 3.1 8B Instant',
      'llama3-groq-70b-8192-tool-use-preview': 'Llama 3 Groq 70B Tool Use',
      'llama3-groq-8b-8192-tool-use-preview': 'Llama 3 Groq 8B Tool Use',
      'llama3-70b-8192': 'Llama 3 70B',
      'llama3-8b-8192': 'Llama 3 8B',
      'mixtral-8x7b-32768': 'Mixtral 8x7B',
      'gemma-7b-it': 'Gemma 7B IT',
      'gemma2-9b-it': 'Gemma 2 9B IT',
    };

    return nameMap[modelId] || modelId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Get model description
   */
  private getModelDescription(modelId: string): string {
    const descriptions: Record<string, string> = {
      'llama-3.1-405b-reasoning': 'Most capable Llama model for complex reasoning tasks',
      'llama-3.1-70b-versatile': 'Versatile model balancing capability and speed',
      'llama-3.1-8b-instant': 'Fastest Llama model for quick responses',
      'llama3-groq-70b-8192-tool-use-preview': 'Llama 3 optimized for tool use and function calling',
      'llama3-groq-8b-8192-tool-use-preview': 'Fast Llama 3 model with tool use capabilities',
      'llama3-70b-8192': 'Large Llama 3 model for complex tasks',
      'llama3-8b-8192': 'Efficient Llama 3 model for general use',
      'mixtral-8x7b-32768': 'Mixture of experts model with large context',
      'gemma-7b-it': 'Google Gemma model fine-tuned for instruction following',
      'gemma2-9b-it': 'Latest Gemma model with improved capabilities',
    };

    return descriptions[modelId] || 'High-performance language model optimized for speed';
  }

  /**
   * Get model details for a specific model
   */
  async getModelDetails(modelId: string, config: AuthConfig): Promise<RawModelInfo> {
    const response = await fetch(`${this.baseUrl}/models/${modelId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${config.token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to get model details: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  }
}
