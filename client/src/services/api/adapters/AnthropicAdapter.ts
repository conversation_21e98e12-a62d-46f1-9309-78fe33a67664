/**
 * Anthropic Provider Adapter for SahAI V2
 * Implements Anthropic Claude API integration
 */

import { 
  Provider<PERSON>dapter, 
  AuthConfig, 
  RawModelInfo, 
  ModelInfo, 
  HealthStatus,
  AnthropicConfig 
} from '../../../types/api';

export class AnthropicAdapter implements ProviderAdapter {
  readonly providerId = 'anthropic';
  readonly name = 'Anthropic';
  readonly baseUrl = 'https://api.anthropic.com';
  readonly authType = 'api-key' as const;

  // Anthropic doesn't have a public models endpoint, so we maintain a list
  private readonly availableModels: RawModelInfo[] = [
    {
      id: 'claude-3-5-sonnet-20241022',
      name: 'Claude 3.5 Sonnet',
      object: 'model',
      created: **********,
      owned_by: 'anthropic',
    },
    {
      id: 'claude-3-5-haiku-20241022',
      name: '<PERSON> 3.5 <PERSON>ku',
      object: 'model',
      created: **********,
      owned_by: 'anthropic',
    },
    {
      id: 'claude-3-opus-20240229',
      name: '<PERSON> 3 Opus',
      object: 'model',
      created: **********,
      owned_by: 'anthropic',
    },
    {
      id: 'claude-3-sonnet-20240229',
      name: 'Claude 3 Sonnet',
      object: 'model',
      created: **********,
      owned_by: 'anthropic',
    },
    {
      id: 'claude-3-haiku-20240307',
      name: 'Claude 3 Haiku',
      object: 'model',
      created: 1709856000,
      owned_by: 'anthropic',
    },
  ];

  /**
   * Authenticate with Anthropic API
   */
  async authenticate(config: AuthConfig): Promise<boolean> {
    try {
      // Test authentication with a simple message request
      const response = await fetch(`${this.baseUrl}/v1/messages`, {
        method: 'POST',
        headers: {
          'x-api-key': config.apiKey!,
          'Content-Type': 'application/json',
          'anthropic-version': (config as AnthropicConfig).version || '2023-06-01',
        },
        body: JSON.stringify({
          model: 'claude-3-haiku-20240307',
          max_tokens: 1,
          messages: [{ role: 'user', content: 'Hi' }],
        }),
      });

      // Even if the request fails due to content, a 400 with proper error indicates valid auth
      if (response.status === 400) {
        const errorData = await response.json();
        return errorData.type !== 'authentication_error';
      }

      return response.ok;
    } catch (error) {
      console.error('Anthropic authentication failed:', error);
      return false;
    }
  }

  /**
   * Discover available models (static list for Anthropic)
   */
  async discoverModels(config: AuthConfig): Promise<RawModelInfo[]> {
    // Validate authentication first
    const isAuthenticated = await this.authenticate(config);
    if (!isAuthenticated) {
      throw new Error('Authentication failed for Anthropic API');
    }

    // Return our curated list of available models
    return [...this.availableModels];
  }

  /**
   * Check Anthropic API health
   */
  async checkHealth(config?: AuthConfig): Promise<HealthStatus> {
    const startTime = Date.now();
    
    try {
      // Use a lightweight request to check API health
      const response = await fetch(`${this.baseUrl}/v1/messages`, {
        method: 'POST',
        headers: {
          ...(config?.apiKey && { 'x-api-key': config.apiKey }),
          'Content-Type': 'application/json',
          'anthropic-version': '2023-06-01',
        },
        body: JSON.stringify({
          model: 'claude-3-haiku-20240307',
          max_tokens: 1,
          messages: [{ role: 'user', content: 'ping' }],
        }),
      });

      const latency = Date.now() - startTime;
      
      // For health check, we consider 400 (bad request) as healthy if it's not auth error
      if (response.ok || response.status === 400) {
        let status: 'healthy' | 'degraded' = 'healthy';
        
        if (response.status === 400) {
          const errorData = await response.json();
          if (errorData.type === 'authentication_error') {
            status = 'degraded';
          }
        }
        
        return {
          status,
          latency,
          lastCheck: new Date(),
          errorRate: status === 'healthy' ? 0 : 0.1,
          uptime: status === 'healthy' ? 1 : 0.9,
          details: {
            endpoint: '/v1/messages',
            responseTime: latency,
          },
        };
      } else {
        return {
          status: 'degraded',
          latency,
          lastCheck: new Date(),
          errorRate: 0.5,
          uptime: 0.5,
          details: {
            endpoint: '/v1/messages',
            responseTime: latency,
            errorMessage: `HTTP ${response.status}: ${response.statusText}`,
          },
        };
      }
    } catch (error) {
      const latency = Date.now() - startTime;
      
      return {
        status: 'down',
        latency,
        lastCheck: new Date(),
        errorRate: 1,
        uptime: 0,
        details: {
          endpoint: '/v1/messages',
          responseTime: latency,
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Normalize Anthropic model data to standard format
   */
  normalizeModel(raw: RawModelInfo): ModelInfo {
    // Anthropic model capabilities
    const getCapabilities = (modelId: string): string[] => {
      const capabilities: string[] = ['text', 'reasoning', 'analysis', 'code'];
      
      if (modelId.includes('claude-3')) {
        capabilities.push('vision', 'documents');
      }
      
      if (modelId.includes('opus')) {
        capabilities.push('creative-writing', 'complex-reasoning');
      }
      
      return capabilities;
    };

    // Context length mapping
    const getContextLength = (modelId: string): number => {
      if (modelId.includes('claude-3')) return 200000; // 200K tokens
      return 100000; // Default for older models
    };

    // Pricing information (approximate, in USD per 1K tokens)
    const getPricing = (modelId: string): { input: number; output: number } => {
      if (modelId.includes('opus')) return { input: 0.015, output: 0.075 };
      if (modelId.includes('sonnet')) return { input: 0.003, output: 0.015 };
      if (modelId.includes('haiku')) return { input: 0.00025, output: 0.00125 };
      return { input: 0.003, output: 0.015 }; // Default to Sonnet pricing
    };

    const pricing = getPricing(raw.id);
    
    return {
      id: raw.id,
      name: this.formatModelName(raw.id),
      description: this.getModelDescription(raw.id),
      contextLength: getContextLength(raw.id),
      inputCost: pricing.input,
      outputCost: pricing.output,
      capabilities: getCapabilities(raw.id),
      provider: this.providerId,
      isAvailable: true,
      lastUpdated: new Date(),
      metadata: {
        owned_by: raw.owned_by,
        created: raw.created,
        object: raw.object,
        original: raw,
      },
    };
  }

  /**
   * Validate Anthropic API key format
   */
  async validateApiKey(apiKey: string): Promise<boolean> {
    // Anthropic API keys start with 'sk-ant-' and are longer
    if (!apiKey.startsWith('sk-ant-') || apiKey.length < 40) {
      return false;
    }

    // Test with actual API call
    try {
      return await this.authenticate({ type: 'api-key', apiKey });
    } catch {
      return false;
    }
  }

  /**
   * Format model name for display
   */
  private formatModelName(modelId: string): string {
    const nameMap: Record<string, string> = {
      'claude-3-5-sonnet-20241022': 'Claude 3.5 Sonnet',
      'claude-3-5-haiku-20241022': 'Claude 3.5 Haiku',
      'claude-3-opus-20240229': 'Claude 3 Opus',
      'claude-3-sonnet-20240229': 'Claude 3 Sonnet',
      'claude-3-haiku-20240307': 'Claude 3 Haiku',
    };

    return nameMap[modelId] || modelId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Get model description
   */
  private getModelDescription(modelId: string): string {
    const descriptions: Record<string, string> = {
      'claude-3-5-sonnet-20241022': 'Most intelligent model with enhanced capabilities',
      'claude-3-5-haiku-20241022': 'Fastest model with improved performance',
      'claude-3-opus-20240229': 'Most powerful model for complex tasks',
      'claude-3-sonnet-20240229': 'Balanced model for most use cases',
      'claude-3-haiku-20240307': 'Fastest model for simple tasks',
    };

    return descriptions[modelId] || 'Anthropic Claude language model';
  }

  /**
   * Get model details for a specific model
   */
  async getModelDetails(modelId: string, config: AuthConfig): Promise<RawModelInfo> {
    const model = this.availableModels.find(m => m.id === modelId);
    if (!model) {
      throw new Error(`Model ${modelId} not found`);
    }
    return { ...model };
  }
}
