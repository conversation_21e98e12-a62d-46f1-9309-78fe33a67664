/**
 * Central API Service for SahAI V2 Provider System
 * Handles all external API communications with providers
 */

import {
  ApiResponse,
  ApiError,
  ApiErrorData,
  RequestConfig,
  AuthConfig,
  ModelInfo,
  HealthStatus,
  ProviderAdapter,
  RateLimitState,
  CircuitBreakerState,
  ApiServiceConfig,
  ProviderDiscoveryResult
} from '../../types/api';

export class ApiService {
  private adapters = new Map<string, ProviderAdapter>();
  private rateLimits = new Map<string, RateLimitState>();
  private circuitBreakers = new Map<string, CircuitBreakerState>();
  private config: ApiServiceConfig;

  constructor(config: Partial<ApiServiceConfig> = {}) {
    this.config = {
      timeout: 30000,
      retries: 3,
      rateLimitEnabled: true,
      circuitBreakerEnabled: true,
      cacheEnabled: true,
      healthCheckInterval: 30000,
      ...config,
    };
  }

  /**
   * Register a provider adapter
   */
  registerAdapter(adapter: ProviderAdapter): void {
    this.adapters.set(adapter.providerId, adapter);
    
    // Initialize circuit breaker state
    this.circuitBreakers.set(adapter.providerId, {
      state: 'closed',
      failureCount: 0,
      successCount: 0,
    });

    console.log(`🔌 Registered adapter for provider: ${adapter.providerId}`);
  }

  /**
   * Get registered adapter for provider
   */
  getAdapter(providerId: string): ProviderAdapter | null {
    return this.adapters.get(providerId) || null;
  }

  /**
   * Discover models for a provider
   */
  async discoverModels(providerId: string, authConfig: AuthConfig): Promise<ModelInfo[]> {
    const adapter = this.getAdapter(providerId);
    if (!adapter) {
      throw new ApiError({
        code: 'ADAPTER_NOT_FOUND',
        message: `No adapter found for provider: ${providerId}`,
        retryable: false,
        timestamp: new Date(),
      });
    }

    // Check circuit breaker
    if (this.config.circuitBreakerEnabled && !this.isCircuitClosed(providerId)) {
      throw new ApiError({
        code: 'CIRCUIT_BREAKER_OPEN',
        message: `Circuit breaker is open for provider: ${providerId}`,
        retryable: true,
        timestamp: new Date(),
      });
    }

    // Check rate limits
    if (this.config.rateLimitEnabled && this.isRateLimited(providerId)) {
      throw new ApiError({
        code: 'RATE_LIMITED',
        message: `Rate limit exceeded for provider: ${providerId}`,
        retryable: true,
        timestamp: new Date(),
      });
    }

    try {
      console.log(`🔍 Discovering models for provider: ${providerId}`);
      
      // Authenticate first
      const isAuthenticated = await adapter.authenticate(authConfig);
      if (!isAuthenticated) {
        throw new ApiError({
          code: 'AUTHENTICATION_FAILED',
          message: `Authentication failed for provider: ${providerId}`,
          retryable: false,
          timestamp: new Date(),
        });
      }

      // Discover models
      const rawModels = await adapter.discoverModels(authConfig);
      const models = rawModels.map(raw => adapter.normalizeModel(raw));

      // Record success
      this.recordSuccess(providerId);
      this.updateRateLimit(providerId);

      console.log(`✅ Discovered ${models.length} models for provider: ${providerId}`);
      return models;

    } catch (error) {
      this.recordFailure(providerId, error);
      throw this.normalizeError(error, providerId);
    }
  }

  /**
   * Check provider health
   */
  async checkProviderHealth(providerId: string, authConfig?: AuthConfig): Promise<HealthStatus> {
    const adapter = this.getAdapter(providerId);
    if (!adapter) {
      return {
        status: 'down',
        latency: 0,
        lastCheck: new Date(),
        errorRate: 1,
        uptime: 0,
        details: {
          errorMessage: `No adapter found for provider: ${providerId}`,
        },
      };
    }

    const startTime = Date.now();
    
    try {
      const health = await adapter.checkHealth(authConfig);
      const latency = Date.now() - startTime;
      
      return {
        ...health,
        latency,
        lastCheck: new Date(),
      };

    } catch (error) {
      const latency = Date.now() - startTime;
      
      return {
        status: 'down',
        latency,
        lastCheck: new Date(),
        errorRate: 1,
        uptime: 0,
        details: {
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Make a generic HTTP request
   */
  async makeRequest<T>(config: RequestConfig): Promise<ApiResponse<T>> {
    const { timeout = this.config.timeout, retries = this.config.retries } = config;
    
    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const response = await this.executeRequest<T>(config, timeout);
        return response;
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < retries && this.isRetryableError(error)) {
          const delay = this.calculateBackoffDelay(attempt);
          console.log(`🔄 Retrying request (attempt ${attempt + 1}/${retries}) after ${delay}ms`);
          await this.sleep(delay);
        } else {
          break;
        }
      }
    }

    throw this.normalizeError(lastError, config.providerId);
  }

  /**
   * Execute HTTP request with timeout
   */
  private async executeRequest<T>(config: RequestConfig, timeout: number): Promise<ApiResponse<T>> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(config.url, {
        method: config.method,
        headers: {
          'Content-Type': 'application/json',
          ...config.headers,
        },
        body: config.body ? JSON.stringify(config.body) : undefined,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      const data = await response.json();

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return {
        data,
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
      };

    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  /**
   * Circuit breaker management
   */
  private isCircuitClosed(providerId: string): boolean {
    const breaker = this.circuitBreakers.get(providerId);
    if (!breaker) return true;

    const now = Date.now();
    
    switch (breaker.state) {
      case 'closed':
        return true;
      case 'open':
        if (breaker.nextAttemptTime && now >= breaker.nextAttemptTime.getTime()) {
          breaker.state = 'half-open';
          return true;
        }
        return false;
      case 'half-open':
        return true;
      default:
        return true;
    }
  }

  private recordSuccess(providerId: string): void {
    const breaker = this.circuitBreakers.get(providerId);
    if (!breaker) return;

    breaker.successCount++;
    
    if (breaker.state === 'half-open' && breaker.successCount >= 3) {
      breaker.state = 'closed';
      breaker.failureCount = 0;
      breaker.successCount = 0;
    }
  }

  private recordFailure(providerId: string, error: any): void {
    const breaker = this.circuitBreakers.get(providerId);
    if (!breaker) return;

    breaker.failureCount++;
    breaker.lastFailureTime = new Date();
    
    if (breaker.failureCount >= 5) {
      breaker.state = 'open';
      breaker.nextAttemptTime = new Date(Date.now() + 60000); // 1 minute
    }
  }

  /**
   * Rate limiting
   */
  private isRateLimited(providerId: string): boolean {
    const rateLimit = this.rateLimits.get(providerId);
    if (!rateLimit) return false;

    const now = Date.now();
    if (now >= rateLimit.resetTime.getTime()) {
      rateLimit.requests = 0;
      rateLimit.resetTime = new Date(now + 60000); // Reset every minute
      rateLimit.isLimited = false;
    }

    return rateLimit.isLimited;
  }

  private updateRateLimit(providerId: string): void {
    let rateLimit = this.rateLimits.get(providerId);
    
    if (!rateLimit) {
      rateLimit = {
        requests: 0,
        tokens: 0,
        resetTime: new Date(Date.now() + 60000),
        isLimited: false,
      };
      this.rateLimits.set(providerId, rateLimit);
    }

    rateLimit.requests++;
    
    // Simple rate limiting: 60 requests per minute
    if (rateLimit.requests >= 60) {
      rateLimit.isLimited = true;
    }
  }

  /**
   * Error handling utilities
   */
  private normalizeError(error: any, providerId: string): ApiError {
    if (error instanceof ApiError) {
      return error;
    }

    const message = error?.message || 'Unknown error occurred';
    const isNetworkError = message.includes('fetch') || message.includes('network');
    const isTimeoutError = message.includes('timeout') || message.includes('aborted');

    return new ApiError({
      code: isTimeoutError ? 'TIMEOUT' : isNetworkError ? 'NETWORK_ERROR' : 'API_ERROR',
      message: `Provider ${providerId}: ${message}`,
      retryable: isNetworkError || isTimeoutError,
      timestamp: new Date(),
      details: error,
    });
  }

  private isRetryableError(error: any): boolean {
    if (error instanceof ApiError) {
      return error.retryable;
    }
    
    const message = error?.message || '';
    return message.includes('timeout') || 
           message.includes('network') || 
           message.includes('fetch');
  }

  private calculateBackoffDelay(attempt: number): number {
    return Math.min(1000 * Math.pow(2, attempt), 10000); // Max 10 seconds
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get service statistics
   */
  getStats() {
    return {
      adapters: Array.from(this.adapters.keys()),
      circuitBreakers: Object.fromEntries(this.circuitBreakers),
      rateLimits: Object.fromEntries(this.rateLimits),
      config: this.config,
    };
  }
}

// Singleton instance
export const apiService = new ApiService();
