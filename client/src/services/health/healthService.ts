/**
 * Health Monitoring Service for SahAI V2
 * Monitors provider health and provides real-time status updates
 */

import { HealthStatus, AuthConfig } from '../../types/api';
import { apiService } from '../api/apiService';

export interface HealthMonitorConfig {
  checkInterval: number; // milliseconds
  enableBackgroundChecks: boolean;
  maxConsecutiveFailures: number;
  alertThreshold: number; // error rate threshold for alerts
}

export interface ProviderHealthInfo {
  providerId: string;
  status: HealthStatus;
  consecutiveFailures: number;
  lastSuccessTime?: Date;
  isMonitoring: boolean;
}

export class HealthService {
  private config: HealthMonitorConfig;
  private providerHealth = new Map<string, ProviderHealthInfo>();
  private monitoringInterval?: NodeJS.Timeout;
  private listeners = new Set<(providerId: string, health: HealthStatus) => void>();

  constructor(config: Partial<HealthMonitorConfig> = {}) {
    this.config = {
      checkInterval: 30000, // 30 seconds
      enableBackgroundChecks: true,
      maxConsecutiveFailures: 5,
      alertThreshold: 0.5, // 50% error rate
      ...config,
    };
  }

  /**
   * Start monitoring a provider
   */
  startMonitoring(providerId: string, authConfig?: AuthConfig): void {
    console.log(`🔍 Starting health monitoring for provider: ${providerId}`);
    
    if (!this.providerHealth.has(providerId)) {
      this.providerHealth.set(providerId, {
        providerId,
        status: {
          status: 'healthy',
          latency: 0,
          lastCheck: new Date(),
          errorRate: 0,
          uptime: 1,
        },
        consecutiveFailures: 0,
        isMonitoring: true,
      });
    } else {
      const info = this.providerHealth.get(providerId)!;
      info.isMonitoring = true;
    }

    // Start background monitoring if not already running
    if (this.config.enableBackgroundChecks && !this.monitoringInterval) {
      this.startBackgroundMonitoring();
    }

    // Perform initial health check
    this.checkProviderHealth(providerId, authConfig);
  }

  /**
   * Stop monitoring a provider
   */
  stopMonitoring(providerId: string): void {
    console.log(`⏹️ Stopping health monitoring for provider: ${providerId}`);
    
    const info = this.providerHealth.get(providerId);
    if (info) {
      info.isMonitoring = false;
    }

    // Stop background monitoring if no providers are being monitored
    const hasActiveMonitoring = Array.from(this.providerHealth.values())
      .some(info => info.isMonitoring);
    
    if (!hasActiveMonitoring && this.monitoringInterval) {
      this.stopBackgroundMonitoring();
    }
  }

  /**
   * Check health for a specific provider
   */
  async checkProviderHealth(providerId: string, authConfig?: AuthConfig): Promise<HealthStatus> {
    try {
      const health = await apiService.checkProviderHealth(providerId, authConfig);
      this.updateProviderHealth(providerId, health, true);
      return health;
    } catch (error) {
      const errorHealth: HealthStatus = {
        status: 'down',
        latency: 0,
        lastCheck: new Date(),
        errorRate: 1,
        uptime: 0,
        details: {
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
        },
      };
      
      this.updateProviderHealth(providerId, errorHealth, false);
      return errorHealth;
    }
  }

  /**
   * Get current health status for a provider
   */
  getProviderHealth(providerId: string): ProviderHealthInfo | null {
    return this.providerHealth.get(providerId) || null;
  }

  /**
   * Get health status for all monitored providers
   */
  getAllProviderHealth(): ProviderHealthInfo[] {
    return Array.from(this.providerHealth.values());
  }

  /**
   * Get overall system health summary
   */
  getSystemHealthSummary(): {
    totalProviders: number;
    healthyProviders: number;
    degradedProviders: number;
    downProviders: number;
    overallStatus: 'healthy' | 'degraded' | 'down';
  } {
    const providers = this.getAllProviderHealth();
    const total = providers.length;
    
    const healthy = providers.filter(p => p.status.status === 'healthy').length;
    const degraded = providers.filter(p => p.status.status === 'degraded').length;
    const down = providers.filter(p => p.status.status === 'down').length;

    let overallStatus: 'healthy' | 'degraded' | 'down' = 'healthy';
    
    if (down > 0) {
      overallStatus = total === down ? 'down' : 'degraded';
    } else if (degraded > 0) {
      overallStatus = 'degraded';
    }

    return {
      totalProviders: total,
      healthyProviders: healthy,
      degradedProviders: degraded,
      downProviders: down,
      overallStatus,
    };
  }

  /**
   * Add health status change listener
   */
  addListener(listener: (providerId: string, health: HealthStatus) => void): void {
    this.listeners.add(listener);
  }

  /**
   * Remove health status change listener
   */
  removeListener(listener: (providerId: string, health: HealthStatus) => void): void {
    this.listeners.delete(listener);
  }

  /**
   * Get health monitoring statistics
   */
  getMonitoringStats(): {
    totalChecks: number;
    successfulChecks: number;
    failedChecks: number;
    averageLatency: number;
    uptime: number;
  } {
    const providers = this.getAllProviderHealth();
    let totalChecks = 0;
    let successfulChecks = 0;
    let failedChecks = 0;
    let totalLatency = 0;
    let totalUptime = 0;

    providers.forEach(provider => {
      // These would be tracked over time in a real implementation
      totalChecks += 1; // Simplified
      if (provider.status.status === 'healthy') {
        successfulChecks += 1;
      } else {
        failedChecks += 1;
      }
      totalLatency += provider.status.latency;
      totalUptime += provider.status.uptime;
    });

    return {
      totalChecks,
      successfulChecks,
      failedChecks,
      averageLatency: providers.length > 0 ? totalLatency / providers.length : 0,
      uptime: providers.length > 0 ? totalUptime / providers.length : 0,
    };
  }

  /**
   * Cleanup and stop all monitoring
   */
  destroy(): void {
    this.stopBackgroundMonitoring();
    this.providerHealth.clear();
    this.listeners.clear();
  }

  /**
   * Private helper methods
   */
  private updateProviderHealth(providerId: string, health: HealthStatus, success: boolean): void {
    let info = this.providerHealth.get(providerId);
    
    if (!info) {
      info = {
        providerId,
        status: health,
        consecutiveFailures: 0,
        isMonitoring: false,
      };
      this.providerHealth.set(providerId, info);
    }

    info.status = health;
    
    if (success) {
      info.consecutiveFailures = 0;
      info.lastSuccessTime = new Date();
    } else {
      info.consecutiveFailures++;
    }

    // Notify listeners
    this.listeners.forEach(listener => {
      try {
        listener(providerId, health);
      } catch (error) {
        console.error('Health listener error:', error);
      }
    });

    // Check for alerts
    this.checkForAlerts(info);
  }

  private checkForAlerts(info: ProviderHealthInfo): void {
    const { status, consecutiveFailures } = info;
    
    // Alert on high error rate
    if (status.errorRate >= this.config.alertThreshold) {
      console.warn(`⚠️ High error rate for provider ${info.providerId}: ${(status.errorRate * 100).toFixed(1)}%`);
    }

    // Alert on consecutive failures
    if (consecutiveFailures >= this.config.maxConsecutiveFailures) {
      console.error(`🚨 Provider ${info.providerId} has ${consecutiveFailures} consecutive failures`);
    }

    // Alert on high latency
    if (status.latency > 10000) { // 10 seconds
      console.warn(`⚠️ High latency for provider ${info.providerId}: ${status.latency}ms`);
    }
  }

  private startBackgroundMonitoring(): void {
    if (this.monitoringInterval) return;

    console.log('🔄 Starting background health monitoring');
    
    this.monitoringInterval = setInterval(async () => {
      const activeProviders = Array.from(this.providerHealth.values())
        .filter(info => info.isMonitoring);

      for (const info of activeProviders) {
        try {
          await this.checkProviderHealth(info.providerId);
        } catch (error) {
          console.error(`Health check failed for ${info.providerId}:`, error);
        }
      }
    }, this.config.checkInterval);
  }

  private stopBackgroundMonitoring(): void {
    if (this.monitoringInterval) {
      console.log('⏹️ Stopping background health monitoring');
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }
  }
}

// Singleton instance
export const healthService = new HealthService();
