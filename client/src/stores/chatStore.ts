import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { generateId } from '../utils/id';

// Enhanced types for rich chat functionality
export interface CodeBlock {
  language: string;
  code: string;
  filename?: string;
  collapsed?: boolean;
}

export interface MessageContent {
  type: 'text' | 'code' | 'markdown' | 'tool_use' | 'tool_result' | 'image' | 'file';
  text?: string;
  code?: CodeBlock;
  markdown?: string;
  toolName?: string;
  toolResult?: any;
  imageUrl?: string;
  fileName?: string;
  fileContent?: string;
}

export interface ChatMessage {
  id: string;
  content: string | MessageContent[];
  role: 'user' | 'assistant' | 'system';
  timestamp: number;
  isEditing?: boolean;
  metadata?: {
    provider?: string;
    model?: string;
    tokens?: number;
    cost?: number;
    tokensIn?: number;
    tokensOut?: number;
  };
}

export interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: number;
  updatedAt: number;
  metadata?: {
    provider?: string;
    model?: string;
    totalTokens?: number;
    totalCost?: number;
  };
}

interface ChatState {
  // Session management
  sessions: ChatSession[];
  currentSession: ChatSession | null;
  
  // UI state
  isLoading: boolean;
  error: string | null;
  
  // Actions
  createNewSession: () => void;
  loadSession: (sessionId: string) => void;
  deleteSession: (sessionId: string) => void;
  updateSessionTitle: (sessionId: string, title: string) => void;
  
  // Message management
  addMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => void;
  updateMessage: (messageId: string, updates: Partial<ChatMessage>) => void;
  deleteMessage: (messageId: string) => void;
  clearCurrentSession: () => void;
  
  // Utility actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  exportSession: (sessionId: string) => string;
  importSession: (sessionData: string) => void;
}

// CEP-compatible storage implementation
const cepStorage = {
  getItem: async (key: string): Promise<string | null> => {
    try {
      if (typeof window !== 'undefined' && (window as any).CSInterface) {
        const csInterface = new (window as any).CSInterface();
        return new Promise((resolve) => {
          csInterface.KVStorage.getItem(key, (data: string) => {
            resolve(data || null);
          });
        });
      } else {
        // Fallback to localStorage for development
        return localStorage.getItem(key);
      }
    } catch (error) {
      console.error('Error getting item from CEP storage:', error);
      return null;
    }
  },
  
  setItem: (key: string, value: string): Promise<void> => {
    return new Promise((resolve) => {
      try {
        if (typeof window !== 'undefined' && (window as any).CSInterface) {
          const csInterface = new (window as any).CSInterface();
          csInterface.KVStorage.setItem(key, value, () => {
            resolve();
          });
        } else {
          // Fallback to localStorage for development
          localStorage.setItem(key, value);
          resolve();
        }
      } catch (error) {
        console.error('Error setting item in CEP storage:', error);
        resolve();
      }
    });
  },
  
  removeItem: (key: string): Promise<void> => {
    return new Promise((resolve) => {
      try {
        if (typeof window !== 'undefined' && (window as any).CSInterface) {
          const csInterface = new (window as any).CSInterface();
          csInterface.KVStorage.removeItem(key, () => {
            resolve();
          });
        } else {
          // Fallback to localStorage for development
          localStorage.removeItem(key);
          resolve();
        }
      } catch (error) {
        console.error('Error removing item from CEP storage:', error);
        resolve();
      }
    });
  }
};

export const useChatStore = create<ChatState>()(
  persist(
    (set, get) => ({
      // Initial state
      sessions: [],
      currentSession: null,
      isLoading: false,
      error: null,

      // Session management
      createNewSession: () => {
        const newSession: ChatSession = {
          id: generateId(),
          title: 'New Chat',
          messages: [],
          createdAt: Date.now(),
          updatedAt: Date.now(),
        };

        set((state) => ({
          sessions: [newSession, ...state.sessions],
          currentSession: newSession,
          error: null,
        }));
      },

      loadSession: (sessionId: string) => {
        const { sessions } = get();
        const session = sessions.find(s => s.id === sessionId);
        
        if (session) {
          set({ currentSession: session, error: null });
        } else {
          set({ error: `Session ${sessionId} not found` });
        }
      },

      deleteSession: (sessionId: string) => {
        set((state) => {
          const updatedSessions = state.sessions.filter(s => s.id !== sessionId);
          const newCurrentSession = state.currentSession?.id === sessionId 
            ? (updatedSessions.length > 0 ? updatedSessions[0] : null)
            : state.currentSession;

          return {
            sessions: updatedSessions,
            currentSession: newCurrentSession,
            error: null,
          };
        });
      },

      updateSessionTitle: (sessionId: string, title: string) => {
        set((state) => ({
          sessions: state.sessions.map(session =>
            session.id === sessionId
              ? { ...session, title, updatedAt: Date.now() }
              : session
          ),
          currentSession: state.currentSession?.id === sessionId
            ? { ...state.currentSession, title, updatedAt: Date.now() }
            : state.currentSession,
        }));
      },

      // Message management
      addMessage: (messageData) => {
        const message: ChatMessage = {
          ...messageData,
          id: generateId(),
          timestamp: Date.now(),
        };

        set((state) => {
          if (!state.currentSession) {
            // Create a new session if none exists
            const newSession: ChatSession = {
              id: generateId(),
              title: messageData.content.slice(0, 50) + (messageData.content.length > 50 ? '...' : ''),
              messages: [message],
              createdAt: Date.now(),
              updatedAt: Date.now(),
            };

            return {
              sessions: [newSession, ...state.sessions],
              currentSession: newSession,
              error: null,
            };
          }

          // Add message to current session
          const updatedSession = {
            ...state.currentSession,
            messages: [...state.currentSession.messages, message],
            updatedAt: Date.now(),
          };

          // Update title if this is the first user message
          if (state.currentSession.messages.length === 0 && messageData.role === 'user') {
            updatedSession.title = messageData.content.slice(0, 50) + (messageData.content.length > 50 ? '...' : '');
          }

          return {
            sessions: state.sessions.map(session =>
              session.id === state.currentSession!.id ? updatedSession : session
            ),
            currentSession: updatedSession,
            error: null,
          };
        });
      },

      updateMessage: (messageId: string, updates: Partial<ChatMessage>) => {
        set((state) => {
          if (!state.currentSession) return state;

          const updatedSession = {
            ...state.currentSession,
            messages: state.currentSession.messages.map(msg =>
              msg.id === messageId ? { ...msg, ...updates } : msg
            ),
            updatedAt: Date.now(),
          };

          return {
            sessions: state.sessions.map(session =>
              session.id === state.currentSession!.id ? updatedSession : session
            ),
            currentSession: updatedSession,
          };
        });
      },

      deleteMessage: (messageId: string) => {
        set((state) => {
          if (!state.currentSession) return state;

          const updatedSession = {
            ...state.currentSession,
            messages: state.currentSession.messages.filter(msg => msg.id !== messageId),
            updatedAt: Date.now(),
          };

          return {
            sessions: state.sessions.map(session =>
              session.id === state.currentSession!.id ? updatedSession : session
            ),
            currentSession: updatedSession,
          };
        });
      },

      clearCurrentSession: () => {
        set((state) => {
          if (!state.currentSession) return state;

          const updatedSession = {
            ...state.currentSession,
            messages: [],
            updatedAt: Date.now(),
          };

          return {
            sessions: state.sessions.map(session =>
              session.id === state.currentSession!.id ? updatedSession : session
            ),
            currentSession: updatedSession,
          };
        });
      },

      // Utility actions
      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      exportSession: (sessionId: string) => {
        const { sessions } = get();
        const session = sessions.find(s => s.id === sessionId);
        return session ? JSON.stringify(session, null, 2) : '';
      },

      importSession: (sessionData: string) => {
        try {
          const session: ChatSession = JSON.parse(sessionData);
          // Generate new ID to avoid conflicts
          session.id = generateId();
          session.updatedAt = Date.now();

          set((state) => ({
            sessions: [session, ...state.sessions],
            currentSession: session,
            error: null,
          }));
        } catch (error) {
          set({ error: 'Failed to import session: Invalid format' });
        }
      },
    }),
    {
      name: 'sahai-chat-store',
      storage: cepStorage as any, // Type assertion to work around Zustand storage interface
      partialize: (state) => ({
        sessions: state.sessions,
        currentSession: state.currentSession,
        isLoading: false, // Reset loading state on restore
        error: null, // Reset error state on restore
      } as Partial<ChatState>),
    }
  )
);
