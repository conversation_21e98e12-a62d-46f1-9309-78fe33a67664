import { create } from 'zustand';

// Modal types for the SahAI extension
export type ModalType =
  | 'settings'
  | 'modelConfiguration'
  | 'apiKey'
  | 'chatHistory'
  | 'providerHealth'
  | 'analytics'
  | 'advancedConfig'
  | 'modelComparison'
  | 'multiModel'
  | 'help'
  | 'about';

export type AccessMethod = 'settings' | 'direct';
export type ModalVariant = 'slide-in';

interface ModalData {
  id?: string;
  name?: string;
  [key: string]: any;
}

interface ModalState {
  // Current modal state
  currentModal: ModalType | null;
  modalData: ModalData | null;
  accessMethod: AccessMethod;
  isOpen: boolean;
  variant: ModalVariant;

  // Modal history for navigation
  modalHistory: ModalType[];

  // Actions
  openModal: (modalType: ModalType, data?: ModalData, method?: AccessMethod, variant?: ModalVariant) => void;
  openSubModal: (modalType: ModalType, data?: ModalData) => void;
  closeModal: () => void;
  goBackToSettings: () => void;

  // Direct modal actions (for convenience)
  showSettings: () => void;
  showModelConfiguration: (variant?: ModalVariant) => void;
  showApiKey: (data?: ModalData) => void;
  showChatHistory: () => void;
  showProviderHealth: () => void;
}

export const useModalStore = create<ModalState>((set, get) => ({
  // Initial state
  currentModal: null,
  modalData: null,
  accessMethod: 'direct',
  isOpen: false,
  variant: 'slide-in', // Default to slide-in for V2 compatibility
  modalHistory: [],

  // Core modal actions
  openModal: (modalType: ModalType, data?: ModalData, method: AccessMethod = 'direct', variant: ModalVariant = 'slide-in') => {
    set({
      currentModal: modalType,
      modalData: data || null,
      accessMethod: method,
      isOpen: true,
      variant: 'slide-in', // Always use slide-in
      modalHistory: method === 'settings' && modalType !== 'settings' ? ['settings'] : [],
    });
  },

  openSubModal: (modalType: ModalType, data?: ModalData) => {
    const { currentModal, modalHistory } = get();
    
    set({
      currentModal: modalType,
      modalData: data || null,
      accessMethod: 'settings',
      isOpen: true,
      modalHistory: currentModal ? [...modalHistory, currentModal] : modalHistory,
    });
  },

  closeModal: () => {
    set({
      currentModal: null,
      modalData: null,
      isOpen: false,
      modalHistory: [],
    });
  },

  goBackToSettings: () => {
    const { modalHistory } = get();
    
    if (modalHistory.length > 0) {
      const previousModal = modalHistory[modalHistory.length - 1];
      const newHistory = modalHistory.slice(0, -1);
      
      set({
        currentModal: previousModal,
        modalData: null,
        modalHistory: newHistory,
      });
    } else {
      set({
        currentModal: 'settings',
        modalData: null,
        accessMethod: 'settings',
        modalHistory: [],
      });
    }
  },

  // Convenience methods
  showSettings: () => {
    get().openModal('settings', undefined, 'settings');
  },

  showModelConfiguration: () => {
    get().openModal('modelConfiguration', undefined, 'direct', 'slide-in');
  },

  showApiKey: (data?: ModalData) => {
    get().openModal('apiKey', data);
  },

  showChatHistory: () => {
    get().openModal('chatHistory');
  },

  showProviderHealth: () => {
    get().openModal('providerHealth');
  },
}));

// Export modal actions for use in components
export const modalActions = {
  showSettings: () => useModalStore.getState().showSettings(),
  showModelConfiguration: () => useModalStore.getState().showModelConfiguration(),
  showApiKey: (data?: ModalData) => useModalStore.getState().showApiKey(data),
  showChatHistory: () => useModalStore.getState().showChatHistory(),
  showProviderHealth: () => useModalStore.getState().showProviderHealth(),
  closeModal: () => useModalStore.getState().closeModal(),
  openModal: (modalType: ModalType, data?: ModalData, method?: AccessMethod) =>
    useModalStore.getState().openModal(modalType, data, method, 'slide-in'),
};
