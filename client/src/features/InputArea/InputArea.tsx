import React, { useState, useRef, useCallback } from 'react';
import { useChatStore } from '../../stores/chatStore';
import {
  AttachIcon,
  SendIcon,
  MicIcon,
  LoadingSpinner
} from '../../components/ui';
import './InputArea.css';

// Context/Reference Icon Component - Simple "@" Symbol
const ContextIcon: React.FC<{ size?: number; className?: string }> = ({
  size = 16,
  className = ''
}) => (
  <div
    className={`context-icon-text ${className}`}
    style={{
      width: size,
      height: size,
      fontSize: size * 0.8,
      fontWeight: 600,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: 'currentColor'
    }}
  >
    @
  </div>
);

interface InputAreaProps {
  onAttachFile?: () => void;
  onVoiceInput?: () => void;
  onContextReference?: () => void;
}

const InputArea: React.FC<InputAreaProps> = React.memo(({
  onAttachFile,
  onVoiceInput,
  onContextReference
}) => {
  const [input, setInput] = useState('');
  const [isComposing, setIsComposing] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const { addMessage, isLoading, setLoading, currentSession, createNewSession } = useChatStore();

  // Constants
  const maxCharacters = 4000;
  const characterCount = input.length;
  const isInputEmpty = !input.trim();

  // Auto-resize textarea
  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`;
    }
  }, []);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);
    adjustTextareaHeight();
  }, [adjustTextareaHeight]);

  const handleSubmit = useCallback(async () => {
    if (!input.trim() || isLoading) return;

    const message = input.trim();
    setInput('');

    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
    }

    try {
      setLoading(true);

      // Create a new session if none exists
      if (!currentSession) {
        createNewSession();
      }

      addMessage({
        content: message,
        role: 'user'
      });

      // TODO: Implement actual message sending to AI
      // For now, just add a mock response
      setTimeout(() => {
        addMessage({
          content: `Echo: ${message}`,
          role: 'assistant'
        });
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Failed to send message:', error);
      setInput(message); // Restore input on error
      setLoading(false);
    }
  }, [input, isLoading, addMessage, setLoading, currentSession, createNewSession]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
      e.preventDefault();
      handleSubmit();
    }
  }, [handleSubmit, isComposing]);

  const handleCompositionStart = useCallback(() => {
    setIsComposing(true);
  }, []);

  const handleCompositionEnd = useCallback(() => {
    setIsComposing(false);
  }, []);

  const handleAttachFile = useCallback(() => {
    onAttachFile?.();
    // TODO: Implement file attachment
    console.log('Attach file clicked');
  }, [onAttachFile]);

  const handleVoiceInput = useCallback(() => {
    onVoiceInput?.();
    // TODO: Implement voice input
    console.log('Voice input clicked');
  }, [onVoiceInput]);

  const handleContextReference = useCallback(() => {
    onContextReference?.();
    // TODO: Implement context reference
    console.log('Context reference clicked');
  }, [onContextReference]);

  return (
    <div className="input-area">
      <div className="input-container">
        <div className="input-wrapper">
          {/* Text Input Area */}
          <div className="textarea-container">
            <textarea
              ref={textareaRef}
              value={input}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              onCompositionStart={handleCompositionStart}
              onCompositionEnd={handleCompositionEnd}
              placeholder="Type a message..."
              className="message-input"
              disabled={isLoading}
              rows={1}
              maxLength={maxCharacters}
            />
          </div>

          {/* Bottom Bar with Icons */}
          <div className="input-bottom-bar">
            {/* Left Buttons */}
            <div className="left-buttons">
              {/* Attach File Button */}
              <button
                onClick={handleAttachFile}
                className="input-icon-btn attach-btn"
                title="Attach file"
                aria-label="Attach file"
                disabled={isLoading}
              >
                <AttachIcon size={16} className="icon" />
              </button>

              {/* Context Reference Button (@) */}
              <button
                onClick={handleContextReference}
                className="input-icon-btn context-btn"
                title="Add context from codebase"
                aria-label="Add context from codebase"
                disabled={isLoading}
              >
                <ContextIcon size={16} className="icon" />
              </button>

              {/* Character Counter - Inside the box */}
              <div className="character-counter">
                <span className={characterCount > maxCharacters * 0.9 ? 'warning' : ''}>
                  {characterCount}
                </span>
                <span className="max-chars">/{maxCharacters}</span>
              </div>
            </div>

            {/* Right Buttons */}
            <div className="right-buttons">
              {/* Mic Button */}
              <button
                onClick={handleVoiceInput}
                className="input-icon-btn mic-btn"
                title="Start voice input"
                aria-label="Start voice input"
                disabled={isLoading}
              >
                <MicIcon size={16} className="icon" />
              </button>

              {/* Send Button */}
              <button
                onClick={handleSubmit}
                disabled={isInputEmpty || isLoading}
                className="input-icon-btn send-btn"
                title="Send message (Enter)"
                aria-label="Send message"
              >
                {isLoading ? (
                  <LoadingSpinner size={16} className="icon" />
                ) : (
                  <SendIcon size={16} className="icon" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Input Footer with Hint Text */}
        <div className="input-footer">
          <div className="input-hint">
            Enter to send, Shift+Enter for new line • Mic for voice input
          </div>
        </div>
      </div>
    </div>
  );
});

export default InputArea;
