/* FIXED: Converted CEP variables to Adobe variables and resolved conflicts with globals.css */
.input-area {
  flex-shrink: 0;
  background: var(--adobe-bg-secondary);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 5;
}

.input-container {
  padding: 16px;
}

.input-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  background: var(--adobe-bg-tertiary);
  border: 1px solid var(--adobe-border);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.input-wrapper:focus-within {
  border-color: var(--adobe-accent);
  box-shadow: 0 0 0 2px rgba(70, 160, 245, 0.2);
}

/* Textarea Container */
.textarea-container {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* FIXED: Scoped textarea styles to prevent conflicts with globals.css */
.message-input {
  width: 100%;
  background: transparent;
  border: none;
  outline: none;
  color: var(--adobe-text-primary);
  font-size: 16px;
  font-family: var(--adobe-font-family);
  font-weight: 400;
  resize: none;
  min-height: 60px; /* Minimum 3 lines of text */
  max-height: 200px;
  line-height: 1.4;
  padding: 16px 16px 8px 16px; /* More top padding, less bottom */
  letter-spacing: 0.01em;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.message-input::placeholder {
  color: var(--adobe-text-secondary);
  opacity: 0.8;
}

.message-input:focus::placeholder {
  opacity: 0.5;
}

.message-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Bottom Bar with Icons */
.input-bottom-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px 12px 12px;
  min-height: 40px;
  background: transparent;
}

/* Left Button Container */
.left-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Right Button Container */
.right-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Character Counter - Inside the box */
.character-counter {
  font-size: 11px;
  line-height: 1;
  color: var(--adobe-text-secondary);
  pointer-events: none;
  user-select: none;
  margin-left: 8px; /* Position near @ icon */
  flex-shrink: 0;
}

.character-counter .warning {
  color: var(--adobe-warning); /* Adobe orange warning color */
}

.character-counter .max-chars {
  color: var(--adobe-text-muted); /* Adobe muted for the max count */
}

/* FIXED: Scoped button styles to prevent conflicts with globals.css */
.input-icon-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--adobe-text-secondary);
  flex-shrink: 0;
  font-family: var(--adobe-font-family);
  outline: none;
}

.input-icon-btn:hover:not(:disabled) {
  background: var(--adobe-bg-tertiary);
  color: var(--adobe-text-primary);
}

.input-icon-btn:active:not(:disabled) {
  background: var(--adobe-bg-tertiary);
  transform: scale(0.95);
}

.input-icon-btn:focus {
  outline: 2px solid var(--adobe-accent);
  outline-offset: 2px;
}

.input-icon-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

/* Specific button styles using Adobe variables */
.attach-btn:hover:not(:disabled) {
  color: var(--adobe-accent); /* Adobe blue for attach */
}

.context-btn:hover:not(:disabled) {
  color: var(--adobe-success); /* Adobe green for context */
}

.mic-btn:hover:not(:disabled) {
  color: var(--adobe-warning); /* Adobe orange for mic */
}

.send-btn:hover:not(:disabled) {
  color: var(--adobe-accent-hover); /* Adobe blue hover for send */
}

.send-btn:disabled {
  opacity: 0.3;
}

/* Icon styles */
.input-icon-btn .icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* Mic listening animation */
.mic-listening {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 28px;
  height: 28px;
  margin: -14px 0 0 -14px;
  border: 2px solid rgba(240, 98, 146, 0.4);
  border-radius: 50%;
  animation: pulse-ring 1.5s ease-out infinite;
  z-index: 1;
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

/* Input Footer */
.input-footer {
  display: flex;
  justify-content: flex-end; /* Right align the hint */
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
  line-height: 1.4;
}

/* Input Hint - Right aligned outside the box */
.input-hint {
  color: var(--adobe-text-secondary);
  font-size: 12px;
  text-align: right;
  line-height: 1.2;
  padding-right: 12px; /* Align with content */
}

/* Responsive Design */
@media (max-width: 768px) {
  .message-input {
    font-size: 16px; /* Prevent zoom on iOS */
    min-height: 50px; /* Slightly smaller on mobile */
    padding: 12px 16px 6px 16px;
  }
  
  .left-buttons, .right-buttons {
    gap: 2px;
  }
  
  .input-icon-btn {
    width: 28px;
    height: 28px;
  }

  .input-icon-btn .icon {
    width: 14px;
    height: 14px;
  }
  
  .input-bottom-bar {
    padding: 6px 8px 8px 8px;
    min-height: 32px;
  }
  
  .character-counter {
    font-size: 10px;
    margin-left: 6px;
  }
}

@media (max-width: 480px) {
  .input-wrapper {
    border-radius: 8px;
  }

  .message-input {
    min-height: 45px;
    padding: 10px 12px 4px 12px;
  }

  .input-icon-btn {
    width: 26px;
    height: 26px;
  }

  .input-icon-btn .icon {
    width: 12px;
    height: 12px;
  }
  
  .input-bottom-bar {
    padding: 4px 6px 6px 6px;
    min-height: 28px;
  }
  
  .character-counter {
    display: none; /* Hide on very small screens */
  }
}

/* Focus States for Accessibility */
.input-icon-btn:focus,
.input-icon-btn:focus-visible {
  outline: 2px solid rgba(255, 255, 255, 0.3);
  outline-offset: 2px;
  border-radius: 6px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .input-wrapper {
    border-width: 2px;
  }
  
  .input-icon-btn {
    border: 1px solid currentColor;
  }
  
  .character-counter {
    font-weight: 600;
  }
}
