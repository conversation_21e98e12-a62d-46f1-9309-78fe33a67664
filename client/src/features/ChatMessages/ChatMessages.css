.chat-messages {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: var(--adobe-bg-primary);
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-height: 100%;
  padding-bottom: 20px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: var(--adobe-text-secondary);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 500;
  color: var(--adobe-text-primary);
}

.empty-state p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

/* Legacy message styles - kept for compatibility but not used by new renderer */
.message {
  display: flex;
  max-width: 80%;
  margin-bottom: 8px;
}

.user-message {
  align-self: flex-end;
  justify-content: flex-end;
}

.assistant-message {
  align-self: flex-start;
  justify-content: flex-start;
}

.message-content {
  background: var(--adobe-bg-secondary);
  border-radius: 12px;
  padding: 12px 16px;
  position: relative;
  border: 1px solid var(--adobe-border);
}

.user-message .message-content {
  background: var(--adobe-accent);
  color: white;
  border-color: var(--adobe-accent);
}

.assistant-message .message-content {
  background: var(--adobe-bg-secondary);
  color: var(--adobe-text-primary);
}

.message-text {
  font-size: 14px;
  line-height: 1.5;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.message-timestamp {
  font-size: 11px;
  opacity: 0.7;
  margin-top: 4px;
  text-align: right;
}

.user-message .message-timestamp {
  color: rgba(255, 255, 255, 0.8);
}

.assistant-message .message-timestamp {
  color: var(--adobe-text-secondary);
}

/* FIXED: Standardized scrollbar styling to match globals.css */
.messages-container::-webkit-scrollbar {
  width: 8px;
}

.messages-container::-webkit-scrollbar-track {
  background: var(--adobe-bg-primary);
}

.messages-container::-webkit-scrollbar-thumb {
  background: var(--adobe-border);
  border-radius: 4px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: var(--adobe-text-secondary);
}

/* Responsive design */
@media (max-width: 768px) {
  .message {
    max-width: 90%;
  }
  
  .messages-container {
    padding: 12px;
    gap: 12px;
  }
  
  .message-content {
    padding: 10px 12px;
  }
  
  .message-text {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .message {
    max-width: 95%;
  }
  
  .messages-container {
    padding: 8px;
    gap: 8px;
  }
  
  .empty-icon {
    font-size: 36px;
  }
  
  .empty-state h3 {
    font-size: 16px;
  }
  
  .empty-state p {
    font-size: 13px;
  }
}
