import React, { useEffect, useRef, useState } from 'react';
import { useChatStore } from '../../stores/chatStore';
import { MessageRenderer } from '../../components/ui/MessageRenderer';
import { createFromMarkdown, messageFactory } from '../../components/ui/MessageRenderer/MessageFactory';
import MessageInteractions from '../../components/ui/MessageInteractions';
import { RichChatMessage, CodeBlockAction } from '../../types/messages';
import { executeCode, formatExecutionResult } from '../../utils/codeExecution';
import './ChatMessages.css';

const ChatMessages: React.FC = () => {
  const {
    currentSession,
    updateMessage,
    deleteMessage,
    addMessage
  } = useChatStore();

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [expandedMessages, setExpandedMessages] = useState<Record<string, boolean>>({});

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [currentSession?.messages]);

  // Convert legacy messages to rich messages
  const convertToRichMessage = (message: any): RichChatMessage => {
    if (typeof message.content === 'string' && message.role === 'assistant') {
      // Try to parse as markdown with code blocks
      return createFromMarkdown(message.content, message.role);
    }

    return {
      id: message.id,
      content: message.content,
      role: message.role,
      timestamp: typeof message.timestamp === 'number'
        ? { created: message.timestamp }
        : message.timestamp,
      metadata: message.metadata,
      state: {
        isEditing: message.isEditing || false,
        isLoading: false,
        hasError: false,
        isExpanded: expandedMessages[message.id] || false
      },
      files: message.files,
      images: message.images
    };
  };

  const handleToggleExpand = (messageId: string) => {
    setExpandedMessages(prev => ({
      ...prev,
      [messageId]: !prev[messageId]
    }));
  };

  const handleEditMessage = (messageId: string, newContent: string) => {
    updateMessage(messageId, { content: newContent });
  };

  const handleDeleteMessage = (messageId: string) => {
    deleteMessage(messageId);
  };

  const handleCopyMessage = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      // Could show a toast notification here
    } catch (error) {
      console.error('Failed to copy message:', error);
    }
  };

  const handleQuoteMessage = (content: string) => {
    // Emit event for input area to handle quoting
    window.dispatchEvent(new CustomEvent('cep:quote-message', {
      detail: { content }
    }));
  };

  const handleCodeAction = async (action: CodeBlockAction) => {
    switch (action.type) {
      case 'run':
        try {
          // Execute the code
          const result = await executeCode({
            language: action.language,
            code: action.code,
            filename: action.filename
          });

          // Create a new message with the execution result
          const resultMessage = messageFactory.createTextMessage(
            formatExecutionResult(result),
            'assistant'
          );

          // Add metadata to indicate this is an execution result
          resultMessage.metadata = {
            ...resultMessage.metadata,
            provider: 'Code Execution',
            model: action.language
          };

          addMessage({
            content: resultMessage.content,
            role: resultMessage.role,
            metadata: resultMessage.metadata
          });
        } catch (error) {
          console.error('Code execution failed:', error);

          // Add error message
          const errorMessage = messageFactory.createTextMessage(
            `❌ Code execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
            'assistant'
          );

          addMessage({
            content: errorMessage.content,
            role: errorMessage.role,
            metadata: {
              provider: 'Code Execution',
              model: action.language
            }
          });
        }
        break;
      case 'save':
        // Handle file save (already handled in CodeBlock component)
        console.log('Save code:', action);
        break;
      case 'copy':
        // Handle copy (already handled in CodeBlock component)
        console.log('Copy code:', action);
        break;
      default:
        break;
    }
  };

  if (!currentSession || currentSession.messages.length === 0) {
    return (
      <div className="chat-messages">
        <div className="empty-state">
          <div className="empty-icon">💬</div>
          <h3>Start a conversation</h3>
          <p>Type a message below to begin chatting with SahAI</p>
        </div>
      </div>
    );
  }

  const richMessages = currentSession.messages.map(convertToRichMessage);

  return (
    <div className="chat-messages">
      <div className="messages-container">
        {richMessages.map((message, index) => (
          <MessageRenderer
            key={message.id}
            message={message}
            isLast={index === richMessages.length - 1}
            isExpanded={expandedMessages[message.id] || false}
            onToggleExpand={handleToggleExpand}
            onEdit={handleEditMessage}
            onDelete={handleDeleteMessage}
            onCopy={handleCopyMessage}
            onQuote={handleQuoteMessage}
            onCodeAction={handleCodeAction}
          />
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Message interactions for text selection */}
      <MessageInteractions
        onQuote={handleQuoteMessage}
        onCopy={handleCopyMessage}
      />
    </div>
  );
};

export default ChatMessages;
