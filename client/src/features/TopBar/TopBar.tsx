import React, { useState, useRef, useEffect, useMemo } from 'react';
import { useChatStore } from '../../stores/chatStore';
import { useSettingsStore } from '../../stores/settingsStore';

import { ProviderStatusIndicator } from '../../components/ProviderSettings/ProviderStatusIndicator';

import { modalActions } from '../../stores/modalStore';
import {
  PlusIcon,
  HistoryIcon,
  MoreVerticalIcon
} from '../../components/ui';
import { usePerformanceMonitor } from '../../services/performanceMonitor';
import '../../components/ui/Icons/Icons.css';
import './TopBar.css';

interface TopBarProps {
  onNewChat?: () => void;
}

const TopBarComponent: React.FC<TopBarProps> = ({
  onNewChat
}) => {
  const { createNewSession, loadSession } = useChatStore();
  const {
    providers,
    currentProvider,
    currentModel,
    setCurrentProvider,
    setCurrentModel
  } = useSettingsStore();

  // Performance monitoring
  const performanceMonitor = usePerformanceMonitor();

  // Model configuration now uses modal store like other modals

  // Initialize default provider if none selected
  useEffect(() => {
    if (!currentProvider && providers.length > 0) {
      console.log('🔄 TopBar: No current provider, setting default to:', providers[0].name);
      setCurrentProvider(providers[0].id);
    }
  }, [providers, currentProvider, setCurrentProvider]);

  // Advanced keyboard navigation event handlers
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Global keyboard shortcuts
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'n':
            event.preventDefault();
            handleNewChat();
            break;
          case 'h':
            event.preventDefault();
            modalActions.showChatHistory();
            break;
          case 'm':
            event.preventDefault();
            modalActions.showSettings();
            break;
          case ',':
            event.preventDefault();
            // Settings now handled by modalStore
            modalActions.showSettings();
            break;
        }
        return;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Model configuration modal handled by modal store

  const handleNewChat = () => {
    createNewSession();
    onNewChat?.();
  };

  const handleShowHistory = () => {
    modalActions.showChatHistory();
  };

  const handleSelectSession = (sessionId: string) => {
    loadSession(sessionId);
    // Chat history modal will be closed automatically by SlideInModalSystem
  };

  // Get model display name in SahAI format
  const modelDisplayName = useMemo(() => {
    if (!currentProvider || !currentModel) {
      return 'Select Model';
    }
    
    // Format: provider:model_name (following SahAI pattern)
    return `${currentProvider.name}:${currentModel.name}`;
  }, [currentProvider, currentModel]);

  // Handle model selector button click - opens modal like other modals
  const handleModelButtonClick = () => {
    modalActions.showModelConfiguration();
  };

  return (
    <div className="top-bar">
      <div className="top-bar-content">
        {/* Status Section */}
        <div className="status-section">
          <ProviderStatusIndicator size="small" />
        </div>

        {/* Model Selector Section */}
        <div className="model-selector-section">
          <div className="model-button-wrapper">
            <button
              className="model-display-button"
              onClick={handleModelButtonClick}
              title="Select Model / API Provider"
              aria-label="Select Model / API Provider"
            >
              <span className="model-display-text">{modelDisplayName}</span>
            </button>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="action-buttons">
          <button
            className="topbar-icon-btn new-chat-btn"
            onClick={handleNewChat}
            title="Start new chat conversation"
            aria-label="Start new chat conversation"
          >
            <PlusIcon size={16} className="icon" />
          </button>

          <button
            className="topbar-icon-btn"
            onClick={handleShowHistory}
            title="View chat history"
            aria-label="View chat history"
          >
            <HistoryIcon size={16} className="icon" />
          </button>

          <button
            className="topbar-icon-btn"
            onClick={() => modalActions.showSettings()}
            title="Settings (Ctrl+M)"
            aria-label="Open settings"
          >
            <MoreVerticalIcon size={16} className="icon" />
          </button>
        </div>
      </div>
    </div>
  );
};

const TopBar = React.memo(TopBarComponent);

export default TopBar;
