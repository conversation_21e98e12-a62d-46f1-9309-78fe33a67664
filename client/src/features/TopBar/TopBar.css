/* TopBar V2 Styles - Adobe CEP Compatible */

.top-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: var(--adobe-bg-primary);
  border-bottom: 1px solid var(--adobe-border);
  min-height: 40px;
  position: relative;
  z-index: 100;
}

.top-bar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 8px;
}

/* Status Section */
.status-section {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

/* Model Selector Section */
.model-selector-section {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  min-width: 0;
  position: relative;
}

.model-button-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 0;
}

.model-display-button {
  display: inline-flex;
  align-items: center;
  padding: 2px 4px;
  background: transparent;
  border: none;
  border-radius: 2px;
  color: var(--adobe-accent);
  font-size: 11px;
  font-family: var(--adobe-font-family);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  outline: none;
  user-select: none;
  text-decoration: underline;
  text-decoration-color: transparent;
}

.model-display-button:hover {
  color: var(--adobe-accent-hover, #0066cc);
  text-decoration-color: var(--adobe-accent-hover, #0066cc);
  background: rgba(0, 119, 255, 0.05);
}

.model-display-button:focus {
  outline: 2px solid var(--adobe-accent);
  outline-offset: 1px;
}

.model-display-button:active {
  color: var(--adobe-accent-active, #004499);
  transform: translateY(1px);
}

.model-display-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
  flex: 1;
}

/* Model selector now uses SlideInModalSystem like other modals */

/* Action Buttons */
.action-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

.topbar-icon-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: transparent;
  border: none;
  border-radius: 3px;
  color: var(--adobe-text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
}

.topbar-icon-btn:hover {
  background: var(--adobe-bg-secondary);
}

.topbar-icon-btn:focus {
  background: var(--adobe-bg-secondary);
  box-shadow: 0 0 0 1px var(--adobe-accent);
}

.topbar-icon-btn:active {
  background: var(--adobe-bg-tertiary);
}

.topbar-icon-btn .icon {
  flex-shrink: 0;
}

/* New Chat Button Specific */
.new-chat-btn {
  color: var(--adobe-accent);
}

.new-chat-btn:hover {
  background: rgba(70, 160, 245, 0.1);
}

/* Responsive Design for Small CEP Panels */
@media (max-width: 400px) {
  .top-bar {
    padding: 6px 8px;
    min-height: 36px;
  }
  
  .top-bar-content {
    gap: 6px;
  }
  
  .model-display-button {
    font-size: 10px;
    padding: 3px 6px;
  }
  
  .topbar-icon-btn {
    width: 24px;
    height: 24px;
  }
  
  .action-buttons {
    gap: 2px;
  }
}

/* High DPI Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .model-display-button,
  .topbar-icon-btn {
    border-width: 0.5px;
  }
}

/* Adobe theme variables automatically handle light/dark themes */
