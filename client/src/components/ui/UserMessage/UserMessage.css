.user-message {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin: 12px 0;
  max-width: 100%;
}

/* FIXED: Converted all CEP variables to Adobe variables */
.user-message-bubble {
  background-color: var(--adobe-bg-tertiary);
  border: 1px solid var(--adobe-border);
  border-radius: 12px;
  padding: 12px 16px;
  max-width: 80%;
  min-width: 120px;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.user-message-bubble:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.user-message-content {
  position: relative;
}

.user-message-text {
  color: var(--adobe-text-primary);
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
}

.user-message-files {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid var(--adobe-border);
}

.user-message-files-label {
  font-size: 12px;
  color: var(--adobe-text-secondary);
  margin-bottom: 4px;
  font-weight: 500;
}

.user-message-file {
  font-size: 12px;
  color: var(--adobe-text-secondary);
  padding: 2px 0;
  font-family: monospace;
}

.user-message-images {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.user-message-image {
  max-width: 200px;
  max-height: 150px;
  border-radius: 6px;
  object-fit: cover;
  border: 1px solid var(--adobe-border);
}

.user-message-actions {
  position: absolute;
  top: -12px;
  right: -12px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
  background-color: var(--adobe-bg-primary);
  border-radius: 6px;
  padding: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  border: 1px solid var(--adobe-border);
}

.user-message-bubble:hover .user-message-actions {
  opacity: 1;
}

.user-message-action-btn {
  background: none;
  border: none;
  color: var(--adobe-text-primary);
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  position: relative;
  min-width: 26px;
  min-height: 26px;
  font-family: var(--adobe-font-family);
  outline: none;
}

.user-message-action-btn:hover {
  background-color: var(--adobe-bg-tertiary);
  color: var(--adobe-accent);
}

.user-message-action-btn:focus {
  outline: 2px solid var(--adobe-accent);
  outline-offset: 2px;
}

.user-message-action-btn:active {
  transform: scale(0.95);
}

.user-message-action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.user-message-action-btn.save {
  color: var(--adobe-success);
}

.user-message-action-btn.save:hover {
  background-color: rgba(76, 175, 80, 0.1);
}

.user-message-action-btn.cancel {
  color: var(--adobe-error);
}

.user-message-action-btn.cancel:hover {
  background-color: rgba(244, 67, 54, 0.1);
}

.user-message-copied {
  position: absolute;
  top: -24px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--adobe-success);
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  white-space: nowrap;
  z-index: 10;
  animation: fadeInOut 2s ease-in-out;
}

.user-message-edit {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* FIXED: Converted CEP variables to Adobe variables to prevent conflicts with globals.css */
.user-message-textarea {
  background-color: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  border-radius: 6px;
  color: var(--adobe-text-primary);
  font-size: 14px;
  line-height: 1.5;
  padding: 8px 12px;
  resize: none;
  outline: none;
  font-family: var(--adobe-font-family);
  min-height: 40px;
  width: 100%;
  box-sizing: border-box;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.user-message-textarea:focus {
  border-color: var(--adobe-accent);
  box-shadow: 0 0 0 2px rgba(70, 160, 245, 0.2);
}

.user-message-edit-actions {
  display: flex;
  justify-content: flex-end;
  gap: 4px;
}

.user-message-timestamp {
  font-size: 11px;
  color: var(--adobe-text-secondary);
  margin-top: 4px;
  margin-right: 4px;
  font-family: monospace;
}

/* REMOVED: Light theme adjustments - Adobe variables handle themes automatically */

/* Responsive design */
@media (max-width: 768px) {
  .user-message-bubble {
    max-width: 90%;
    padding: 10px 12px;
  }
  
  .user-message-text {
    font-size: 13px;
  }
  
  .user-message-actions {
    top: -10px;
    right: -10px;
    padding: 2px;
  }
  
  .user-message-action-btn {
    padding: 4px;
    min-width: 24px;
    min-height: 24px;
  }
  
  .user-message-image {
    max-width: 150px;
    max-height: 100px;
  }
}

@media (max-width: 480px) {
  .user-message-bubble {
    max-width: 95%;
    padding: 8px 10px;
  }
  
  .user-message-text {
    font-size: 12px;
  }
  
  .user-message-timestamp {
    font-size: 10px;
  }
}

/* Animation keyframes */
@keyframes fadeInOut {
  0%, 100% { opacity: 0; }
  10%, 90% { opacity: 1; }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .user-message-bubble {
    border-width: 2px;
  }
  
  .user-message-action-btn:focus {
    outline: 2px solid var(--adobe-accent);
    outline-offset: 2px;
  }

  .user-message-textarea:focus {
    outline: 2px solid var(--adobe-accent);
    outline-offset: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .user-message-bubble,
  .user-message-actions,
  .user-message-action-btn {
    transition: none;
  }
  
  .user-message-action-btn:active {
    transform: none;
  }
  
  .user-message-copied {
    animation: none;
    opacity: 1;
  }
}
