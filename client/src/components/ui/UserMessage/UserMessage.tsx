import React, { useState, useRef, useEffect } from 'react';
import { EditIcon, CheckIcon, XIcon, CopyIcon } from '../Icons/Icons';
import { RichChatMessage } from '../../../types/messages';
import './UserMessage.css';

interface UserMessageProps {
  message: RichChatMessage;
  onEdit?: (messageId: string, newContent: string) => void;
  onCopy?: (content: string) => void;
  onDelete?: (messageId: string) => void;
  className?: string;
}

const UserMessage: React.FC<UserMessageProps> = ({
  message,
  onEdit,
  onCopy,
  onDelete,
  className = ''
}) => {
  const [isEditing, setIsEditing] = useState(message.state?.isEditing || false);
  const [editContent, setEditContent] = useState(
    typeof message.content === 'string' ? message.content : ''
  );
  const [copied, setCopied] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current && isEditing) {
      const textarea = textareaRef.current;
      textarea.style.height = 'auto';
      textarea.style.height = `${textarea.scrollHeight}px`;
      textarea.focus();
      textarea.setSelectionRange(textarea.value.length, textarea.value.length);
    }
  }, [isEditing, editContent]);

  const handleEditStart = () => {
    setIsEditing(true);
    setEditContent(typeof message.content === 'string' ? message.content : '');
  };

  const handleEditSave = () => {
    if (editContent.trim() && onEdit) {
      onEdit(message.id, editContent.trim());
    }
    setIsEditing(false);
  };

  const handleEditCancel = () => {
    setIsEditing(false);
    setEditContent(typeof message.content === 'string' ? message.content : '');
  };

  const handleCopy = async () => {
    const contentToCopy = typeof message.content === 'string' ? message.content : '';
    try {
      await navigator.clipboard.writeText(contentToCopy);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      onCopy?.(contentToCopy);
    } catch (error) {
      console.error('Failed to copy message:', error);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleEditSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleEditCancel();
    }
  };

  const formatTimestamp = (timestamp: number | { created: number; updated?: number }) => {
    const time = typeof timestamp === 'number' ? timestamp : timestamp.created;
    return new Date(time).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
  };

  const getMessageContent = () => {
    if (typeof message.content === 'string') {
      return message.content;
    }
    // For complex content, extract text parts
    return message.content
      .filter(content => content.type === 'text')
      .map(content => content.text)
      .join('\n');
  };

  return (
    <div className={`user-message ${className}`}>
      <div className="user-message-bubble">
        {isEditing ? (
          <div className="user-message-edit">
            <textarea
              ref={textareaRef}
              value={editContent}
              onChange={(e) => setEditContent(e.target.value)}
              onKeyDown={handleKeyDown}
              className="user-message-textarea"
              placeholder="Type your message..."
              rows={1}
            />
            <div className="user-message-edit-actions">
              <button
                className="user-message-action-btn save"
                onClick={handleEditSave}
                disabled={!editContent.trim()}
                title="Save (Ctrl+Enter)"
              >
                <CheckIcon size={14} />
              </button>
              <button
                className="user-message-action-btn cancel"
                onClick={handleEditCancel}
                title="Cancel (Escape)"
              >
                <XIcon size={14} />
              </button>
            </div>
          </div>
        ) : (
          <div className="user-message-content">
            <div className="user-message-text">
              {getMessageContent()}
            </div>
            
            {/* Display attached files if any */}
            {message.files && message.files.length > 0 && (
              <div className="user-message-files">
                <div className="user-message-files-label">Attached files:</div>
                {message.files.map((file, index) => (
                  <div key={index} className="user-message-file">
                    📎 {file}
                  </div>
                ))}
              </div>
            )}
            
            {/* Display attached images if any */}
            {message.images && message.images.length > 0 && (
              <div className="user-message-images">
                {message.images.map((image, index) => (
                  <img
                    key={index}
                    src={image}
                    alt={`Attachment ${index + 1}`}
                    className="user-message-image"
                  />
                ))}
              </div>
            )}
          </div>
        )}
        
        {!isEditing && (
          <div className="user-message-actions">
            <button
              className="user-message-action-btn"
              onClick={handleEditStart}
              title="Edit message"
            >
              <EditIcon size={14} />
            </button>
            <button
              className="user-message-action-btn"
              onClick={handleCopy}
              title="Copy message"
            >
              <CopyIcon size={14} />
              {copied && <span className="user-message-copied">Copied!</span>}
            </button>
          </div>
        )}
      </div>
      
      <div className="user-message-timestamp">
        {formatTimestamp(message.timestamp)}
      </div>
    </div>
  );
};

export default UserMessage;
