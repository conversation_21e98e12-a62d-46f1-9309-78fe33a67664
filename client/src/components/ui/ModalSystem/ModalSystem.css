/**
 * Modal System Styles for V2
 * Adobe CEP-compatible modal styling
 * FIXED: Resolved conflicts with globals.css by using scoped selectors
 */

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-container {
  background: var(--adobe-bg-primary);
  border: 1px solid var(--adobe-border);
  border-radius: 6px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
  max-width: 90vw;
  max-height: 90vh;
  width: 600px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--adobe-border);
  background: var(--adobe-bg-secondary);
}

.modal-header h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--adobe-text-primary);
}

/* FIXED: Scoped button styles to prevent conflicts with globals.css */
.modal-close-button {
  background: transparent;
  border: none;
  color: var(--adobe-text-secondary);
  font-size: 20px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 3px;
  font-family: var(--adobe-font-family);
  transition: all 0.2s ease;
  outline: none;
}

.modal-close-button:hover {
  background: var(--adobe-bg-tertiary);
  color: var(--adobe-text-primary);
}

.modal-close-button:focus {
  box-shadow: 0 0 0 2px var(--adobe-accent);
  outline: none;
}

.modal-close-button:active {
  transform: scale(0.95);
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

/* FIXED: Scoped scrollbar styles to prevent conflicts with globals.css */
.modal-body::-webkit-scrollbar {
  width: 8px;
}

.modal-body::-webkit-scrollbar-track {
  background: var(--adobe-bg-primary);
}

.modal-body::-webkit-scrollbar-thumb {
  background: var(--adobe-border);
  border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: var(--adobe-text-secondary);
}

/* Responsive design */
@media (max-width: 768px) {
  .modal-container {
    width: 95vw;
    max-height: 95vh;
  }
  
  .modal-backdrop {
    padding: 10px;
  }
}

/* Animation */
.modal-backdrop {
  animation: fadeIn 0.2s ease-out;
}

.modal-container {
  animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
