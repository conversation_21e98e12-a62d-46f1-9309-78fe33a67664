.message-renderer {
  width: 100%;
  margin: 0;
  padding: 0;
}

.message-renderer.user {
  display: flex;
  justify-content: flex-end;
}

.message-renderer.assistant {
  display: flex;
  justify-content: flex-start;
}

.message-renderer.system {
  display: flex;
  justify-content: center;
  margin: 8px 0;
}

.system-message {
  background-color: var(--cep-system-message-bg, rgba(85, 85, 85, 0.3));
  border: 1px solid var(--cep-system-message-border, rgba(85, 85, 85, 0.5));
  border-radius: 6px;
  padding: 8px 12px;
  max-width: 80%;
  text-align: center;
}

.system-message-content {
  color: var(--cep-text-secondary, #999999);
  font-size: 12px;
  font-style: italic;
  margin-bottom: 4px;
}

.system-message-timestamp {
  color: var(--adobe-text-secondary);
  font-size: 10px;
  font-family: monospace;
}

/* Light theme adjustments */
[data-theme="light"] .system-message {
  background-color: var(--cep-system-message-bg-light, rgba(208, 208, 208, 0.3));
  border-color: var(--cep-system-message-border-light, rgba(208, 208, 208, 0.5));
}

[data-theme="light"] .system-message-content,
[data-theme="light"] .system-message-timestamp {
  color: var(--cep-text-secondary-light, #666666);
}

/* Responsive design */
@media (max-width: 768px) {
  .system-message {
    max-width: 90%;
    padding: 6px 10px;
  }
  
  .system-message-content {
    font-size: 11px;
  }
  
  .system-message-timestamp {
    font-size: 9px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .system-message {
    border-width: 2px;
  }
}
