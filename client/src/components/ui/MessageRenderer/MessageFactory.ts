import { 
  RichChatMessage, 
  MessageContent, 
  CodeBlockData, 
  ToolUse, 
  MessageFactory,
  MessageRole 
} from '../../../types/messages';
import { generateId } from '../../../utils/id';

/**
 * Factory class for creating different types of chat messages
 * Follows the factory pattern used in Cline for message creation
 */
class MessageFactoryImpl implements MessageFactory {
  private createBaseMessage(role: MessageRole): Omit<RichChatMessage, 'content'> {
    return {
      id: generateId(),
      role,
      timestamp: {
        created: Date.now()
      },
      state: {
        isEditing: false,
        isLoading: false,
        hasError: false,
        isExpanded: false
      }
    };
  }

  createTextMessage(content: string, role: 'user' | 'assistant'): RichChatMessage {
    return {
      ...this.createBaseMessage(role),
      content
    };
  }

  createCodeMessage(code: CodeBlockData, role: 'assistant'): RichChatMessage {
    const content: MessageContent[] = [
      {
        type: 'code',
        code
      }
    ];

    return {
      ...this.createBaseMessage(role),
      content
    };
  }

  createMarkdownMessage(markdown: string, role: 'assistant'): RichChatMessage {
    const content: MessageContent[] = [
      {
        type: 'markdown',
        markdown
      }
    ];

    return {
      ...this.createBaseMessage(role),
      content
    };
  }

  createToolMessage(toolUse: ToolUse, role: 'assistant'): RichChatMessage {
    const content: MessageContent[] = [
      {
        type: 'tool_use',
        toolUse
      }
    ];

    return {
      ...this.createBaseMessage(role),
      content
    };
  }

  createMixedMessage(contents: MessageContent[], role: 'assistant'): RichChatMessage {
    return {
      ...this.createBaseMessage(role),
      content: contents
    };
  }

  /**
   * Create a message with files attached
   */
  createMessageWithFiles(
    content: string, 
    files: string[], 
    role: 'user' | 'assistant'
  ): RichChatMessage {
    return {
      ...this.createBaseMessage(role),
      content,
      files
    };
  }

  /**
   * Create a message with images attached
   */
  createMessageWithImages(
    content: string, 
    images: string[], 
    role: 'user' | 'assistant'
  ): RichChatMessage {
    return {
      ...this.createBaseMessage(role),
      content,
      images
    };
  }

  /**
   * Create a system message
   */
  createSystemMessage(content: string): RichChatMessage {
    return {
      ...this.createBaseMessage('system'),
      content
    };
  }

  /**
   * Parse markdown content and extract code blocks
   */
  parseMarkdownWithCodeBlocks(markdown: string): MessageContent[] {
    const contents: MessageContent[] = [];
    const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
    let lastIndex = 0;
    let match;

    while ((match = codeBlockRegex.exec(markdown)) !== null) {
      // Add text before code block
      const textBefore = markdown.slice(lastIndex, match.index).trim();
      if (textBefore) {
        contents.push({
          type: 'markdown',
          markdown: textBefore
        });
      }

      // Add code block
      const language = match[1] || 'text';
      const code = match[2].trim();
      contents.push({
        type: 'code',
        code: {
          language,
          code,
          collapsed: false
        }
      });

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text
    const textAfter = markdown.slice(lastIndex).trim();
    if (textAfter) {
      contents.push({
        type: 'markdown',
        markdown: textAfter
      });
    }

    return contents.length > 0 ? contents : [{ type: 'markdown', markdown }];
  }

  /**
   * Create a message from markdown with automatic code block extraction
   */
  createFromMarkdown(markdown: string, role: 'assistant' = 'assistant'): RichChatMessage {
    const contents = this.parseMarkdownWithCodeBlocks(markdown);
    
    // If only one content item and it's markdown, use string content
    if (contents.length === 1 && contents[0].type === 'markdown') {
      return this.createTextMessage(contents[0].markdown || '', role);
    }

    return this.createMixedMessage(contents, role);
  }

  /**
   * Clone an existing message with modifications
   */
  cloneMessage(
    message: RichChatMessage, 
    modifications: Partial<RichChatMessage> = {}
  ): RichChatMessage {
    return {
      ...message,
      ...modifications,
      id: generateId(), // Always generate new ID for cloned messages
      timestamp: {
        ...message.timestamp,
        updated: Date.now()
      }
    };
  }

  /**
   * Update message content while preserving other properties
   */
  updateMessageContent(
    message: RichChatMessage, 
    newContent: string | MessageContent[]
  ): RichChatMessage {
    return {
      ...message,
      content: newContent,
      timestamp: {
        ...message.timestamp,
        updated: Date.now()
      }
    };
  }
}

// Export singleton instance
export const messageFactory = new MessageFactoryImpl();

// Export class for testing
export { MessageFactoryImpl };

// Re-export registry for component management
export {
  messageComponentRegistry,
  registerMessageComponent,
  getMessageComponent,
  MESSAGE_COMPONENT_MAP
} from './MessageComponentRegistry';

// Export utility functions
export const createTextMessage = (content: string, role: 'user' | 'assistant') => 
  messageFactory.createTextMessage(content, role);

export const createCodeMessage = (code: CodeBlockData, role: 'assistant' = 'assistant') => 
  messageFactory.createCodeMessage(code, role);

export const createMarkdownMessage = (markdown: string, role: 'assistant' = 'assistant') => 
  messageFactory.createMarkdownMessage(markdown, role);

export const createFromMarkdown = (markdown: string, role: 'assistant' = 'assistant') => 
  messageFactory.createFromMarkdown(markdown, role);
