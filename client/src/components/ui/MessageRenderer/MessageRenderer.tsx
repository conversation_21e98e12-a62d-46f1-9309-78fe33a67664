import React from 'react';
import { RichChatMessage, MessageRendererProps, CodeBlockAction } from '../../../types/messages';
import { messageComponentRegistry, MessageComponentProps } from './MessageComponentRegistry';
import './MessageRenderer.css';

interface MessageRendererComponentProps extends MessageRendererProps {
  onCodeAction?: (action: CodeBlockAction) => void;
}

const MessageRenderer: React.FC<MessageRendererComponentProps> = ({
  message,
  isLast = false,
  isExpanded = false,
  onToggleExpand,
  onEdit,
  onDelete,
  onRetry,
  onCopy,
  onQuote,
  onCodeAction
}) => {
  const handleEdit = (messageId: string, newContent: string) => {
    onEdit?.(messageId, newContent);
  };

  const handleCopy = (content: string) => {
    onCopy?.(content);
  };

  const handleCodeAction = (action: CodeBlockAction) => {
    onCodeAction?.(action);

    // Handle specific code actions using declarative map
    const codeActionHandlers = {
      run: () => {
        // Emit event for terminal execution
        window.dispatchEvent(new CustomEvent('cep:execute-code', {
          detail: {
            language: action.language,
            code: action.code,
            filename: action.filename
          }
        }));
      },
      save: () => {
        // File save is handled in the CodeBlock component
      },
      copy: () => {
        // Copy is handled in the CodeBlock component
      },
    };

    const handler = codeActionHandlers[action.type as keyof typeof codeActionHandlers];
    handler?.();
  };

  // Render using registry system - completely eliminates switch statements
  const componentConfig = messageComponentRegistry.getComponent(message.role);

  if (!componentConfig) {
    console.warn(`No component registered for message role: ${message.role}`);
    return null;
  }

  const { component: MessageComponent, propsMapper } = componentConfig;

  // Create base props for the message
  const baseProps: MessageComponentProps = {
    message,
    onEdit: handleEdit,
    onCopy: handleCopy,
    onDelete,
    onRetry,
    onCodeAction: handleCodeAction,
  };

  // Use the registered props mapper to get the correct props for this component
  const componentProps = propsMapper(baseProps);

  return (
    <div className={`message-renderer ${message.role}`}>
      <MessageComponent {...componentProps} />
    </div>
  );
};

export default MessageRenderer;
