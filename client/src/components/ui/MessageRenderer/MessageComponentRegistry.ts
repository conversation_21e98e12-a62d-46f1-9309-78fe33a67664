/**
 * Message Component Registry
 * Declarative component mapping system replacing switch statements
 * Provides extensible architecture for adding new message types
 */

import React from 'react';
import { MessageRole, RichChatMessage, CodeBlockAction } from '../../../types/messages';
import ChatBubble, { ChatBubbleVariant } from '../ChatBubble';
// Legacy imports for backward compatibility
import UserMessage from '../UserMessage';
import AssistantMessage from '../AssistantMessage';

// System Message Component
export const SystemMessage: React.FC<{ message: RichChatMessage }> = ({ message }) => (
  React.createElement('div', { className: 'system-message' }, [
    React.createElement('div', { 
      key: 'content',
      className: 'system-message-content' 
    }, typeof message.content === 'string' ? message.content : 'System message'),
    React.createElement('div', { 
      key: 'timestamp',
      className: 'system-message-timestamp' 
    }, new Date(
      typeof message.timestamp === 'number' 
        ? message.timestamp 
        : message.timestamp.created
    ).toLocaleTimeString())
  ])
);

// Message component interface for type safety
export interface MessageComponentProps {
  message: RichChatMessage;
  onEdit?: (messageId: string, newContent: string) => void;
  onCopy?: (content: string) => void;
  onDelete?: (messageId: string) => void;
  onRetry?: (messageId: string) => void;
  onCodeAction?: (action: CodeBlockAction) => void;
}

// Component configuration interface
export interface MessageComponentConfig {
  component: React.ComponentType<any>;
  propsMapper: (baseProps: MessageComponentProps) => any;
}

// Unified ChatBubble props mappers
const createUserBubblePropsMapper = (baseProps: MessageComponentProps) => ({
  message: baseProps.message,
  variant: 'user' as ChatBubbleVariant,
  onEdit: baseProps.onEdit,
  onCopy: baseProps.onCopy,
  onDelete: baseProps.onDelete,
  onRetry: baseProps.onRetry,
});

const createAssistantBubblePropsMapper = (baseProps: MessageComponentProps) => ({
  message: baseProps.message,
  variant: 'assistant' as ChatBubbleVariant,
  onCopy: baseProps.onCopy,
  onCodeAction: baseProps.onCodeAction,
});

const createSystemBubblePropsMapper = (baseProps: MessageComponentProps) => ({
  message: baseProps.message,
  variant: 'system' as ChatBubbleVariant,
});

// Legacy props mappers for backward compatibility
const createUserPropsMapper = (baseProps: MessageComponentProps) => ({
  message: baseProps.message,
  onEdit: baseProps.onEdit,
  onCopy: baseProps.onCopy,
  onDelete: baseProps.onDelete,
  onRetry: baseProps.onRetry,
});

const createAssistantPropsMapper = (baseProps: MessageComponentProps) => ({
  message: baseProps.message,
  onCopy: baseProps.onCopy,
  onCodeAction: baseProps.onCodeAction,
});

const createSystemPropsMapper = (baseProps: MessageComponentProps) => ({
  message: baseProps.message,
});

// Message component registry - extensible and type-safe
class MessageComponentRegistryImpl {
  private components: Map<MessageRole, MessageComponentConfig> = new Map();

  constructor() {
    // Register unified ChatBubble components (preferred)
    this.registerComponent('user', ChatBubble, createUserBubblePropsMapper);
    this.registerComponent('assistant', ChatBubble, createAssistantBubblePropsMapper);
    this.registerComponent('system', ChatBubble, createSystemBubblePropsMapper);
  }

  /**
   * Switch to legacy components (for backward compatibility)
   */
  useLegacyComponents(): void {
    this.registerComponent('user', UserMessage, createUserPropsMapper);
    this.registerComponent('assistant', AssistantMessage, createAssistantPropsMapper);
    this.registerComponent('system', SystemMessage, createSystemPropsMapper);
  }

  /**
   * Switch to unified ChatBubble components (default)
   */
  useUnifiedComponents(): void {
    this.registerComponent('user', ChatBubble, createUserBubblePropsMapper);
    this.registerComponent('assistant', ChatBubble, createAssistantBubblePropsMapper);
    this.registerComponent('system', ChatBubble, createSystemBubblePropsMapper);
  }

  /**
   * Register a new message component
   */
  registerComponent(
    role: MessageRole,
    component: React.ComponentType<any>,
    propsMapper: (baseProps: MessageComponentProps) => any
  ): void {
    this.components.set(role, { component, propsMapper });
  }

  /**
   * Get component configuration for a message role
   */
  getComponent(role: MessageRole): MessageComponentConfig | undefined {
    return this.components.get(role);
  }

  /**
   * Get all registered message roles
   */
  getRegisteredRoles(): MessageRole[] {
    return Array.from(this.components.keys());
  }

  /**
   * Check if a role is registered
   */
  hasComponent(role: MessageRole): boolean {
    return this.components.has(role);
  }

  /**
   * Unregister a component (useful for testing or dynamic component loading)
   */
  unregisterComponent(role: MessageRole): boolean {
    return this.components.delete(role);
  }

  /**
   * Clear all registered components
   */
  clear(): void {
    this.components.clear();
  }

  /**
   * Get component map for direct access (backward compatibility)
   */
  getComponentMap(): Record<string, React.ComponentType<any>> {
    const map: Record<string, React.ComponentType<any>> = {};
    this.components.forEach((config, role) => {
      map[role] = config.component;
    });
    return map;
  }
}

// Export singleton instance
export const messageComponentRegistry = new MessageComponentRegistryImpl();

// Export class for testing
export { MessageComponentRegistryImpl };

// Utility functions for easier component registration
export const registerMessageComponent = (
  role: MessageRole,
  component: React.ComponentType<any>,
  propsMapper: (baseProps: MessageComponentProps) => any
) => {
  messageComponentRegistry.registerComponent(role, component, propsMapper);
};

export const getMessageComponent = (role: MessageRole) => {
  return messageComponentRegistry.getComponent(role);
};

// Export default component map for backward compatibility
export const MESSAGE_COMPONENT_MAP = messageComponentRegistry.getComponentMap();
