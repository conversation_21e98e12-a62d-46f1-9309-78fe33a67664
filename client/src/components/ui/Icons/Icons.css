/* Icon Styles for SahAI CEP Extension */

/* Base icon styles */
.icon {
  display: inline-block;
  vertical-align: middle;
  flex-shrink: 0;
  transition: all 0.2s ease;
}

/* Icon hover effects */
.icon:hover {
  opacity: 0.8;
}

/* Icon sizes */
.icon-xs {
  width: 12px;
  height: 12px;
}

.icon-sm {
  width: 14px;
  height: 14px;
}

.icon-md {
  width: 16px;
  height: 16px;
}

.icon-lg {
  width: 20px;
  height: 20px;
}

.icon-xl {
  width: 24px;
  height: 24px;
}

/* Icon colors */
.icon-primary {
  color: var(--cep-accent-color, #007acc);
}

.icon-secondary {
  color: var(--cep-text-secondary, #999999);
}

.icon-success {
  color: var(--cep-success-color, #28a745);
}

.icon-warning {
  color: var(--cep-warning-color, #ffc107);
}

.icon-error {
  color: var(--cep-error-color, #dc3545);
}

.icon-muted {
  color: var(--cep-text-muted, #666666);
}

/* Icon states */
.icon-disabled {
  opacity: 0.5;
  pointer-events: none;
}

.icon-active {
  color: var(--cep-accent-color, #007acc);
}

/* Icon animations */
.icon-spin {
  animation: icon-spin 1s linear infinite;
}

.icon-pulse {
  animation: icon-pulse 2s ease-in-out infinite;
}

.icon-bounce {
  animation: icon-bounce 1s ease-in-out infinite;
}

/* Keyframes */
@keyframes icon-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes icon-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes icon-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-1px);
  }
}

/* Icon button styles */
.icon-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  background: transparent;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
}

.icon-button:hover {
  background: var(--cep-hover-background, rgba(255, 255, 255, 0.1));
}

.icon-button:focus {
  background: var(--cep-focus-background, rgba(255, 255, 255, 0.1));
  box-shadow: 0 0 0 1px var(--cep-focus-border, #007acc);
}

.icon-button:active {
  background: var(--cep-active-background, rgba(255, 255, 255, 0.2));
}

.icon-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Icon with text */
.icon-with-text {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.icon-with-text .icon {
  margin-right: 0;
}

/* Icon positioning */
.icon-left {
  margin-right: 4px;
}

.icon-right {
  margin-left: 4px;
}

.icon-top {
  margin-bottom: 4px;
}

.icon-bottom {
  margin-top: 4px;
}

/* High DPI support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .icon svg {
    shape-rendering: geometricPrecision;
  }
}

/* Dark theme adjustments */
.cep-dark-theme .icon {
  color: var(--cep-text-color, #cccccc);
}

.cep-dark-theme .icon-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.cep-dark-theme .icon-button:focus {
  background: rgba(255, 255, 255, 0.1);
}

.cep-dark-theme .icon-button:active {
  background: rgba(255, 255, 255, 0.2);
}

/* Light theme adjustments */
.cep-light-theme .icon {
  color: var(--cep-text-color, #333333);
}

.cep-light-theme .icon-button:hover {
  background: rgba(0, 0, 0, 0.05);
}

.cep-light-theme .icon-button:focus {
  background: rgba(0, 0, 0, 0.05);
}

.cep-light-theme .icon-button:active {
  background: rgba(0, 0, 0, 0.1);
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .icon-spin,
  .icon-pulse,
  .icon-bounce {
    animation: none;
  }
  
  .icon {
    transition: none;
  }
  
  .icon-button {
    transition: none;
  }
}

/* Focus visible for keyboard navigation */
.icon-button:focus-visible {
  outline: 2px solid var(--cep-focus-border, #007acc);
  outline-offset: 1px;
}
