/**
 * Unified ChatBubble Styles
 * Consolidates UserMessage.css and AssistantMessage.css
 * Supports user, assistant, and system variants
 * Adobe CEP-compatible styling
 */

/* ===== BASE CHAT BUBBLE STYLES ===== */

.chat-bubble {
  display: flex;
  flex-direction: column;
  margin: 12px 0;
  max-width: 100%;
  position: relative;
}

.chat-bubble-container {
  position: relative;
  width: 100%;
}

/* ===== USER VARIANT ===== */

.chat-bubble.user {
  align-items: flex-end;
}

.chat-bubble.user .chat-bubble-container {
  background-color: var(--adobe-bg-tertiary);
  border: 1px solid var(--adobe-border);
  border-radius: 12px;
  padding: 12px 16px;
  max-width: 80%;
  min-width: 120px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.chat-bubble.user .chat-bubble-container:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* ===== ASSISTANT VARIANT ===== */

.chat-bubble.assistant {
  align-items: flex-start;
  margin: 16px 0;
}

.chat-bubble.assistant .chat-bubble-container {
  width: 100%;
  background-color: transparent;
  padding: 0;
}

/* ===== SYSTEM VARIANT ===== */

.chat-bubble.system {
  align-items: center;
  margin: 8px 0;
}

.chat-bubble.system .chat-bubble-container {
  background-color: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  border-radius: 6px;
  padding: 8px 12px;
  max-width: 80%;
  text-align: center;
}

.chat-bubble-system-content {
  color: var(--adobe-text-secondary);
  font-size: 12px;
  font-style: italic;
  margin-bottom: 4px;
}

.chat-bubble-system-timestamp {
  color: var(--adobe-text-secondary);
  font-size: 10px;
  opacity: 0.7;
}

/* ===== CONTENT STYLES ===== */

.chat-bubble-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.chat-bubble-content-item {
  width: 100%;
}

.chat-bubble-text {
  color: var(--adobe-text-primary);
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
}

.chat-bubble.assistant .chat-bubble-text {
  line-height: 1.6;
}

.chat-bubble-markdown {
  color: var(--adobe-text-primary);
  font-size: 14px;
  line-height: 1.6;
}

/* Markdown styling */
.chat-bubble-markdown h1,
.chat-bubble-markdown h2,
.chat-bubble-markdown h3,
.chat-bubble-markdown h4,
.chat-bubble-markdown h5,
.chat-bubble-markdown h6 {
  color: var(--adobe-text-primary);
  margin: 16px 0 8px 0;
  font-weight: 600;
}

.chat-bubble-markdown h1 { font-size: 24px; }
.chat-bubble-markdown h2 { font-size: 20px; }
.chat-bubble-markdown h3 { font-size: 18px; }
.chat-bubble-markdown h4 { font-size: 16px; }
.chat-bubble-markdown h5 { font-size: 14px; }
.chat-bubble-markdown h6 { font-size: 12px; }

.chat-bubble-markdown p {
  margin: 8px 0;
  line-height: 1.6;
}

.chat-bubble-markdown ul,
.chat-bubble-markdown ol {
  margin: 8px 0;
  padding-left: 20px;
}

.chat-bubble-markdown li {
  margin: 4px 0;
  line-height: 1.5;
}

.chat-bubble-markdown blockquote {
  border-left: 3px solid var(--adobe-accent);
  padding-left: 12px;
  margin: 12px 0;
  font-style: italic;
  color: var(--adobe-text-secondary);
}

.chat-bubble-markdown code {
  background: var(--adobe-bg-secondary);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.chat-bubble-markdown table {
  border-collapse: collapse;
  width: 100%;
  margin: 12px 0;
}

.chat-bubble-markdown th,
.chat-bubble-markdown td {
  border: 1px solid var(--adobe-border);
  padding: 8px 12px;
  text-align: left;
}

.chat-bubble-markdown th {
  background: var(--adobe-bg-secondary);
  font-weight: 600;
}

/* ===== TOOL USE STYLES ===== */

.chat-bubble-tool-use {
  background: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  border-radius: 6px;
  padding: 12px;
  margin: 8px 0;
}

.tool-use-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.tool-use-name {
  font-weight: 600;
  color: var(--adobe-accent);
  font-size: 14px;
}

.tool-use-input {
  background: var(--adobe-bg-primary);
  border: 1px solid var(--adobe-border);
  border-radius: 4px;
  padding: 8px;
  overflow-x: auto;
}

.tool-use-input pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: var(--adobe-text-primary);
  white-space: pre-wrap;
}

/* ===== EDITING STYLES ===== */

.chat-bubble-edit {
  position: relative;
}

.chat-bubble-textarea {
  width: 100%;
  min-height: 40px;
  padding: 8px 12px;
  border: 1px solid var(--adobe-border);
  border-radius: 6px;
  background: var(--adobe-bg-primary);
  color: var(--adobe-text-primary);
  font-family: var(--adobe-font-family);
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  outline: none;
  transition: border-color 0.2s ease;
}

.chat-bubble-textarea:focus {
  border-color: var(--adobe-accent);
  box-shadow: 0 0 0 1px var(--adobe-accent);
}

.chat-bubble-edit-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  justify-content: flex-end;
}

/* ===== FOOTER AND ACTIONS ===== */

.chat-bubble-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  gap: 8px;
}

.chat-bubble.user .chat-bubble-footer {
  margin-top: 8px;
}

.chat-bubble.assistant .chat-bubble-footer {
  margin-top: 12px;
}

.chat-bubble-metadata {
  display: flex;
  gap: 8px;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.chat-bubble-provider,
.chat-bubble-model,
.chat-bubble-tokens {
  font-size: 11px;
  color: var(--adobe-text-secondary);
  background: var(--adobe-bg-secondary);
  padding: 2px 6px;
  border-radius: 3px;
  white-space: nowrap;
}

.chat-bubble-actions {
  display: flex;
  gap: 4px;
  align-items: center;
  flex-shrink: 0;
}

.chat-bubble-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  border-radius: 4px;
  color: var(--adobe-text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
  position: relative;
  opacity: 0.8;
}

.chat-bubble-action-btn:hover {
  background: var(--adobe-bg-tertiary);
  border-color: var(--adobe-accent);
  color: var(--adobe-text-primary);
  opacity: 1;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chat-bubble-action-btn:focus {
  background: var(--adobe-bg-tertiary);
  border-color: var(--adobe-accent);
  box-shadow: 0 0 0 2px rgba(70, 160, 245, 0.3);
  opacity: 1;
}

.chat-bubble-action-btn:active {
  background: var(--adobe-bg-tertiary);
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.chat-bubble-action-btn.save {
  color: var(--adobe-color-success);
}

.chat-bubble-action-btn.save:hover {
  background: rgba(76, 175, 80, 0.1);
}

.chat-bubble-action-btn.cancel {
  color: var(--adobe-color-error);
}

.chat-bubble-action-btn.cancel:hover {
  background: rgba(244, 67, 54, 0.1);
}

.chat-bubble-action-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
  transform: none;
}

/* Specific button type styling */
.chat-bubble-action-btn.retry {
  color: var(--adobe-accent);
}

.chat-bubble-action-btn.retry:hover {
  background: rgba(70, 160, 245, 0.1);
  border-color: var(--adobe-accent);
  color: var(--adobe-accent);
}

.chat-bubble-action-btn.edit {
  color: var(--adobe-text-primary);
}

.chat-bubble-action-btn.edit:hover {
  background: rgba(108, 117, 125, 0.1);
  color: var(--adobe-text-primary);
}

.chat-bubble-action-btn.copy {
  color: var(--adobe-text-secondary);
}

.chat-bubble-action-btn.copy:hover {
  background: rgba(108, 117, 125, 0.1);
  color: var(--adobe-text-primary);
}

.chat-bubble-copied {
  position: absolute;
  top: -30px;
  right: 0;
  background: var(--adobe-bg-primary);
  border: 1px solid var(--adobe-border);
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 11px;
  color: var(--adobe-text-primary);
  white-space: nowrap;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
  .chat-bubble.user .chat-bubble-container {
    max-width: 90%;
  }

  .chat-bubble-text,
  .chat-bubble-markdown {
    font-size: 13px;
  }

  .chat-bubble-metadata {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .chat-bubble.user .chat-bubble-container {
    max-width: 95%;
    padding: 10px 12px;
  }

  .chat-bubble-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .chat-bubble-actions {
    align-self: flex-end;
  }
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  .chat-bubble-container,
  .chat-bubble-action-btn,
  .chat-bubble-textarea {
    transition: none;
  }
}

@media (prefers-contrast: high) {
  .chat-bubble.user .chat-bubble-container {
    border-width: 2px;
  }

  .chat-bubble.system .chat-bubble-container {
    border-width: 2px;
  }
}
