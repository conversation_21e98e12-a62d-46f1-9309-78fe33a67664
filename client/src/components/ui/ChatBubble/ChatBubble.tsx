/**
 * Unified ChatBubble Component
 * Consolidates UserMessage and AssistantMessage into a single component with variant props
 * Clinical precision implementation following audit recommendations
 */

import React, { useState, useRef, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { EditIcon, CheckIcon, XIcon, CopyIcon, RefreshIcon } from '../Icons/Icons';
import { RichChatMessage, MessageContent, CodeBlockAction } from '../../../types/messages';
import CodeBlock from '../CodeBlock';
import './ChatBubble.css';

export type ChatBubbleVariant = 'user' | 'assistant' | 'system';

interface ChatBubbleProps {
  message: RichChatMessage;
  variant: ChatBubbleVariant;
  onEdit?: (messageId: string, newContent: string) => void;
  onCopy?: (content: string) => void;
  onDelete?: (messageId: string) => void;
  onRetry?: (messageId: string) => void;
  onCodeAction?: (action: CodeBlockAction) => void;
  className?: string;
}

const ChatBubble: React.FC<ChatBubbleProps> = ({
  message,
  variant,
  onEdit,
  onCopy,
  onDelete,
  onRetry,
  onCodeAction,
  className = ''
}) => {
  const [isEditing, setIsEditing] = useState(message.state?.isEditing || false);
  const [editContent, setEditContent] = useState(
    typeof message.content === 'string' ? message.content : ''
  );
  const [copied, setCopied] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea for user messages
  useEffect(() => {
    if (textareaRef.current && isEditing && variant === 'user') {
      const textarea = textareaRef.current;
      textarea.style.height = 'auto';
      textarea.style.height = `${textarea.scrollHeight}px`;
      textarea.focus();
      textarea.setSelectionRange(textarea.value.length, textarea.value.length);
    }
  }, [isEditing, editContent, variant]);

  // User message editing handlers
  const handleEditStart = () => {
    if (variant !== 'user') return;
    setIsEditing(true);
    setEditContent(typeof message.content === 'string' ? message.content : '');
  };

  const handleEditSave = () => {
    if (editContent.trim() && onEdit) {
      onEdit(message.id, editContent.trim());
    }
    setIsEditing(false);
  };

  const handleEditCancel = () => {
    setIsEditing(false);
    setEditContent(typeof message.content === 'string' ? message.content : '');
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleEditSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleEditCancel();
    }
  };

  // Copy functionality
  const handleCopy = async () => {
    const contentToCopy = getPlainTextContent();
    try {
      await navigator.clipboard.writeText(contentToCopy);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      onCopy?.(contentToCopy);
    } catch (error) {
      console.error('Failed to copy message:', error);
    }
  };

  // Retry functionality
  const handleRetry = () => {
    if (onRetry) {
      onRetry(message.id);
    }
  };

  const getPlainTextContent = (): string => {
    if (typeof message.content === 'string') {
      return message.content;
    }
    
    if (Array.isArray(message.content)) {
      return message.content
        .map(content => {
          switch (content.type) {
            case 'text':
              return content.text || '';
            case 'markdown':
              return content.markdown || '';
            case 'code':
              return content.code?.code || '';
            case 'tool_use':
              return `Tool: ${content.toolUse?.name || 'Unknown'}`;
            default:
              return '';
          }
        })
        .join('\n');
    }
    
    return '';
  };

  // Render content based on type
  const renderContent = () => {
    if (typeof message.content === 'string') {
      return (
        <div className="chat-bubble-text">
          {message.content}
        </div>
      );
    }

    if (Array.isArray(message.content)) {
      return (
        <div className="chat-bubble-content">
          {message.content.map((content, index) => (
            <div key={index} className="chat-bubble-content-item">
              {renderContentItem(content)}
            </div>
          ))}
        </div>
      );
    }

    return null;
  };

  const renderContentItem = (content: MessageContent) => {
    switch (content.type) {
      case 'text':
        return (
          <div className="chat-bubble-text">
            {content.text}
          </div>
        );

      case 'markdown':
        return (
          <div className="chat-bubble-markdown">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                code: ({ node, className, children, ...props }: any) => {
                  const inline = (props as any).inline;
                  const match = /language-(\w+)/.exec(className || '');
                  const language = match ? match[1] : 'text';
                  
                  if (!inline && children) {
                    const code = String(children).replace(/\n$/, '');
                    return (
                      <CodeBlock
                        data={{
                          language,
                          code,
                          collapsed: false
                        }}
                        onAction={onCodeAction}
                      />
                    );
                  }
                  
                  return (
                    <code className={className} {...props}>
                      {children}
                    </code>
                  );
                },
                pre: ({ children }) => <>{children}</>,
              }}
            >
              {content.markdown || ''}
            </ReactMarkdown>
          </div>
        );

      case 'code':
        return content.code ? (
          <CodeBlock
            data={content.code}
            onAction={onCodeAction}
          />
        ) : null;

      case 'tool_use':
        return (
          <div className="chat-bubble-tool-use">
            <div className="tool-use-header">
              <span className="tool-use-name">{content.toolUse?.name || 'Tool'}</span>
            </div>
            {content.toolUse?.input && (
              <div className="tool-use-input">
                <pre>{JSON.stringify(content.toolUse.input, null, 2)}</pre>
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  // Render based on variant
  if (variant === 'system') {
    return (
      <div className={`chat-bubble system ${className}`}>
        <div className="chat-bubble-system-content">
          {typeof message.content === 'string' ? message.content : 'System message'}
        </div>
        <div className="chat-bubble-system-timestamp">
          {new Date(
            typeof message.timestamp === 'number' 
              ? message.timestamp 
              : message.timestamp.created
          ).toLocaleTimeString()}
        </div>
      </div>
    );
  }

  return (
    <div className={`chat-bubble ${variant} ${className}`}>
      <div className="chat-bubble-container">
        {isEditing && variant === 'user' ? (
          <div className="chat-bubble-edit">
            <textarea
              ref={textareaRef}
              value={editContent}
              onChange={(e) => setEditContent(e.target.value)}
              onKeyDown={handleKeyDown}
              className="chat-bubble-textarea"
              placeholder="Type your message..."
              rows={1}
            />
            <div className="chat-bubble-edit-actions">
              <button
                className="chat-bubble-action-btn save"
                onClick={handleEditSave}
                disabled={!editContent.trim()}
                title="Save (Ctrl+Enter)"
              >
                <CheckIcon size={14} />
              </button>
              <button
                className="chat-bubble-action-btn cancel"
                onClick={handleEditCancel}
                title="Cancel (Escape)"
              >
                <XIcon size={14} />
              </button>
            </div>
          </div>
        ) : (
          <>
            {renderContent()}
            
            {/* Actions footer */}
            <div className="chat-bubble-footer">
              {variant === 'assistant' && message.metadata && (
                <div className="chat-bubble-metadata">
                  {message.metadata.provider && (
                    <span className="chat-bubble-provider">
                      {message.metadata.provider}
                    </span>
                  )}
                  {message.metadata.model && (
                    <span className="chat-bubble-model">
                      {message.metadata.model}
                    </span>
                  )}
                  {message.metadata.tokens && (
                    <span className="chat-bubble-tokens">
                      {message.metadata.tokens} tokens
                    </span>
                  )}
                </div>
              )}
              
              <div className="chat-bubble-actions">
                {variant === 'user' && onRetry && (
                  <button
                    className="chat-bubble-action-btn retry"
                    onClick={handleRetry}
                    title="Retry message"
                  >
                    <RefreshIcon size={16} />
                  </button>
                )}

                {variant === 'user' && onEdit && (
                  <button
                    className="chat-bubble-action-btn edit"
                    onClick={handleEditStart}
                    title="Edit message"
                  >
                    <EditIcon size={16} />
                  </button>
                )}

                <button
                  className="chat-bubble-action-btn copy"
                  onClick={handleCopy}
                  title="Copy message"
                >
                  <CopyIcon size={16} />
                  {copied && <span className="chat-bubble-copied">Copied!</span>}
                </button>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default ChatBubble;
