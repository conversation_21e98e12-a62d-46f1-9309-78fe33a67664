import React, { useState, useRef, useEffect } from 'react';
import { QuoteIcon, CopyIcon, EditIcon, TrashIcon } from '../Icons/Icons';
import './MessageInteractions.css';

interface MessageInteractionsProps {
  onQuote?: (text: string) => void;
  onCopy?: (text: string) => void;
  onEdit?: () => void;
  onDelete?: () => void;
  className?: string;
}

interface SelectionInfo {
  text: string;
  x: number;
  y: number;
}

const MessageInteractions: React.FC<MessageInteractionsProps> = ({
  onQuote,
  onCopy,
  onEdit,
  onDelete,
  className = ''
}) => {
  const [selection, setSelection] = useState<SelectionInfo | null>(null);
  const [showActions, setShowActions] = useState(false);
  const actionsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleSelectionChange = () => {
      const sel = window.getSelection();
      if (sel && sel.toString().trim()) {
        const range = sel.getRangeAt(0);
        const rect = range.getBoundingClientRect();
        
        setSelection({
          text: sel.toString().trim(),
          x: rect.left + rect.width / 2,
          y: rect.top - 10
        });
        setShowActions(true);
      } else {
        setSelection(null);
        setShowActions(false);
      }
    };

    const handleClickOutside = (event: MouseEvent) => {
      if (actionsRef.current && !actionsRef.current.contains(event.target as Node)) {
        setShowActions(false);
        setSelection(null);
      }
    };

    document.addEventListener('selectionchange', handleSelectionChange);
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('selectionchange', handleSelectionChange);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleQuote = () => {
    if (selection?.text && onQuote) {
      onQuote(selection.text);
      setShowActions(false);
      setSelection(null);
      window.getSelection()?.removeAllRanges();
    }
  };

  const handleCopy = async () => {
    if (selection?.text && onCopy) {
      try {
        await navigator.clipboard.writeText(selection.text);
        onCopy(selection.text);
        setShowActions(false);
        setSelection(null);
        window.getSelection()?.removeAllRanges();
      } catch (error) {
        console.error('Failed to copy text:', error);
      }
    }
  };

  if (!showActions || !selection) {
    return null;
  }

  return (
    <div
      ref={actionsRef}
      className={`message-interactions ${className}`}
      style={{
        position: 'fixed',
        left: selection.x,
        top: selection.y,
        transform: 'translateX(-50%)',
        zIndex: 1000
      }}
    >
      <div className="message-interactions-popup">
        <button
          className="message-interaction-btn"
          onClick={handleQuote}
          title="Quote selection"
        >
          <QuoteIcon size={14} />
        </button>
        <button
          className="message-interaction-btn"
          onClick={handleCopy}
          title="Copy selection"
        >
          <CopyIcon size={14} />
        </button>
      </div>
    </div>
  );
};

export default MessageInteractions;
