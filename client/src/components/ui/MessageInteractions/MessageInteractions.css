.message-interactions {
  pointer-events: none;
}

.message-interactions-popup {
  background-color: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  border-radius: 6px;
  box-shadow: var(--adobe-shadow);
  display: flex;
  gap: 2px;
  padding: 4px;
  pointer-events: auto;
  animation: fadeInUp 0.2s ease-out;
}

.message-interaction-btn {
  background: none;
  border: none;
  color: var(--adobe-text-primary);
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  min-width: 28px;
  min-height: 28px;
}

.message-interaction-btn:hover {
  background-color: var(--adobe-bg-tertiary);
  color: var(--adobe-accent);
}

.message-interaction-btn:active {
  transform: scale(0.95);
}

/* Arrow pointing down */
.message-interactions-popup::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid var(--cep-popup-background, #2d2d30);
}

.message-interactions-popup::before {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-top: 7px solid var(--cep-popup-border, #555555);
  z-index: -1;
}

/* Light theme adjustments */
[data-theme="light"] .message-interactions-popup {
  background-color: var(--cep-popup-background-light, #ffffff);
  border-color: var(--cep-popup-border-light, #d0d0d0);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

[data-theme="light"] .message-interaction-btn {
  color: var(--cep-text-color-light, #333333);
}

[data-theme="light"] .message-interaction-btn:hover {
  background-color: var(--cep-hover-background-light, #f0f0f0);
  color: var(--cep-accent-color-light, #0066cc);
}

[data-theme="light"] .message-interactions-popup::after {
  border-top-color: var(--cep-popup-background-light, #ffffff);
}

[data-theme="light"] .message-interactions-popup::before {
  border-top-color: var(--cep-popup-border-light, #d0d0d0);
}

/* Animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .message-interactions-popup {
    border-width: 2px;
  }
  
  .message-interaction-btn:focus {
    outline: 2px solid var(--cep-focus-color, #007acc);
    outline-offset: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .message-interactions-popup {
    animation: none;
  }
  
  .message-interaction-btn {
    transition: none;
  }
  
  .message-interaction-btn:active {
    transform: none;
  }
}
