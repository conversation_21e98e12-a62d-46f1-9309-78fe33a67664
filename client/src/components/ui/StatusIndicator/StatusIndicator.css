/**
 * Generic StatusIndicator Styles
 * Supports dot, badge, and pill variants with multiple status types
 * Adobe CEP-compatible styling with accessibility features
 */

/* ===== BASE STATUS INDICATOR ===== */

.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-family: var(--adobe-font-family);
  transition: all 0.2s ease;
  outline: none;
  position: relative;
}

.status-indicator.clickable {
  cursor: pointer;
  background: transparent;
  border: none;
  padding: 2px;
  border-radius: 3px;
}

.status-indicator.clickable:hover {
  background: var(--adobe-bg-secondary);
}

.status-indicator.clickable:focus {
  background: var(--adobe-bg-secondary);
  box-shadow: 0 0 0 1px var(--adobe-accent);
}

.status-indicator.clickable:active {
  background: var(--adobe-bg-tertiary);
}

.status-indicator.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* ===== SIZE VARIANTS ===== */

.status-indicator.small {
  gap: 3px;
  padding: 1px;
  font-size: 10px;
}

.status-indicator.medium {
  gap: 4px;
  padding: 2px;
  font-size: 11px;
}

.status-indicator.large {
  gap: 5px;
  padding: 3px;
  font-size: 12px;
}

/* ===== DOT VARIANT ===== */

.status-indicator.dot .status-dot {
  border-radius: 50%;
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.status-indicator.dot .status-label {
  font-weight: 500;
  white-space: nowrap;
  color: var(--adobe-text-primary);
}

/* ===== BADGE VARIANT ===== */

.status-indicator.badge .status-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 4px;
  background: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
}

.status-indicator.badge .status-badge-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-indicator.badge .status-badge-label {
  font-size: 10px;
  font-weight: 500;
  color: var(--adobe-text-primary);
  white-space: nowrap;
}

/* ===== PILL VARIANT ===== */

.status-indicator.pill .status-pill {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 12px;
  background: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
}

.status-indicator.pill .status-pill-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-indicator.pill .status-pill-label {
  font-size: 11px;
  font-weight: 500;
  color: var(--adobe-text-primary);
  white-space: nowrap;
}

/* ===== STATUS TYPE COLORS ===== */

/* Success Status */
.status-indicator.success .status-dot,
.status-indicator.success .status-badge-dot,
.status-indicator.success .status-pill-dot {
  background: var(--adobe-color-success, #4CAF50);
  box-shadow: 0 0 0 1px rgba(76, 175, 80, 0.3);
}

.status-indicator.success.badge .status-badge,
.status-indicator.success.pill .status-pill {
  background: rgba(76, 175, 80, 0.1);
  border-color: rgba(76, 175, 80, 0.3);
}



/* Error Status */
.status-indicator.error .status-dot,
.status-indicator.error .status-badge-dot,
.status-indicator.error .status-pill-dot {
  background: var(--adobe-color-error, #F44336);
  box-shadow: 0 0 0 1px rgba(244, 67, 54, 0.3);
}

.status-indicator.error.badge .status-badge,
.status-indicator.error.pill .status-pill {
  background: rgba(244, 67, 54, 0.1);
  border-color: rgba(244, 67, 54, 0.3);
}



/* ===== HOVER EFFECTS ===== */

.status-indicator.clickable:hover .status-dot {
  transform: scale(1.2);
}

.status-indicator.clickable:hover.success .status-dot,
.status-indicator.clickable:hover.success .status-badge-dot,
.status-indicator.clickable:hover.success .status-pill-dot {
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.5);
}

.status-indicator.clickable:hover.error .status-dot,
.status-indicator.clickable:hover.error .status-badge-dot,
.status-indicator.clickable:hover.error .status-pill-dot {
  box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.5);
}

/* ===== ANIMATIONS ===== */



.status-indicator.animated .status-dot,
.status-indicator.animated .status-badge-dot,
.status-indicator.animated .status-pill-dot {
  animation: status-change 0.3s ease-in-out;
}

@keyframes status-change {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.3);
  }
  100% {
    transform: scale(1);
  }
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  .status-indicator,
  .status-indicator .status-dot,
  .status-indicator .status-badge-dot,
  .status-indicator .status-pill-dot {
    transition: none;
    animation: none;
  }
  
  .status-indicator.clickable:hover .status-dot {
    transform: none;
  }
}

@media (prefers-contrast: high) {
  .status-indicator.success .status-dot,
  .status-indicator.success .status-badge-dot,
  .status-indicator.success .status-pill-dot {
    background: #28a745;
    border: 2px solid white;
  }

  .status-indicator.warning .status-dot,
  .status-indicator.warning .status-badge-dot,
  .status-indicator.warning .status-pill-dot {
    background: #ffc107;
    border: 2px solid black;
  }

  .status-indicator.error .status-dot,
  .status-indicator.error .status-badge-dot,
  .status-indicator.error .status-pill-dot {
    background: #dc3545;
    border: 2px solid white;
  }

  .status-indicator.info .status-dot,
  .status-indicator.info .status-badge-dot,
  .status-indicator.info .status-pill-dot {
    background: #007bff;
    border: 2px solid white;
  }

  .status-indicator.none .status-dot,
  .status-indicator.none .status-badge-dot,
  .status-indicator.none .status-pill-dot {
    background: #6c757d;
    border: 2px solid white;
  }
}
