/**
 * Generic StatusIndicator Component
 * Reusable status indicator that can be used across the application
 * Abstracts status logic from ProviderStatusIndicator for better reusability
 * Clinical precision implementation following audit recommendations
 */

import React from 'react';
import './StatusIndicator.css';

export type StatusType = 'success' | 'error';
export type StatusSize = 'small' | 'medium' | 'large';
export type StatusVariant = 'dot' | 'badge' | 'pill';

export interface StatusInfo {
  type: StatusType;
  label: string;
  description?: string;
}

interface StatusIndicatorProps {
  status: StatusInfo;
  size?: StatusSize;
  variant?: StatusVariant;
  showLabel?: boolean;
  onClick?: () => void;
  className?: string;
  disabled?: boolean;
  animated?: boolean;
}

const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  size = 'medium',
  variant = 'dot',
  showLabel = false,
  onClick,
  className = '',
  disabled = false,
  animated = false,
}) => {
  const sizeMap = {
    small: { dot: 8, badge: 16, pill: 20 },
    medium: { dot: 10, badge: 20, pill: 24 },
    large: { dot: 12, badge: 24, pill: 28 },
  };

  const indicatorSize = sizeMap[size][variant];

  const handleClick = () => {
    if (!disabled && onClick) {
      onClick();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!disabled && onClick && (e.key === 'Enter' || e.key === ' ')) {
      e.preventDefault();
      onClick();
    }
  };

  const baseClasses = [
    'status-indicator',
    variant,
    size,
    status.type,
    className,
    onClick && !disabled ? 'clickable' : '',
    disabled ? 'disabled' : '',
    animated ? 'animated' : '',
  ].filter(Boolean).join(' ');

  const renderDot = () => (
    <div
      className="status-dot"
      style={{
        width: `${indicatorSize}px`,
        height: `${indicatorSize}px`,
      }}
    />
  );

  const renderBadge = () => (
    <div
      className="status-badge"
      style={{
        minWidth: `${indicatorSize}px`,
        height: `${indicatorSize}px`,
      }}
    >
      <div className="status-badge-dot" />
      {showLabel && <span className="status-badge-label">{status.label}</span>}
    </div>
  );

  const renderPill = () => (
    <div
      className="status-pill"
      style={{
        minHeight: `${indicatorSize}px`,
      }}
    >
      <div className="status-pill-dot" />
      <span className="status-pill-label">{status.label}</span>
    </div>
  );

  const renderIndicator = () => {
    switch (variant) {
      case 'badge':
        return renderBadge();
      case 'pill':
        return renderPill();
      case 'dot':
      default:
        return renderDot();
    }
  };

  const Element = onClick && !disabled ? 'button' : 'div';

  return (
    <Element
      className={baseClasses}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      title={status.description || status.label}
      aria-label={`Status: ${status.label}${status.description ? `. ${status.description}` : ''}`}
      role={onClick && !disabled ? 'button' : undefined}
      tabIndex={onClick && !disabled ? 0 : undefined}
      disabled={disabled}
    >
      {renderIndicator()}
      
      {showLabel && variant === 'dot' && (
        <span className="status-label">{status.label}</span>
      )}
    </Element>
  );
};

export default StatusIndicator;

// Utility functions for status types
export const createSuccessStatus = (label: string, description?: string): StatusInfo => ({
  type: 'success',
  label,
  description,
});

export const createErrorStatus = (label: string, description?: string): StatusInfo => ({
  type: 'error',
  label,
  description,
});


