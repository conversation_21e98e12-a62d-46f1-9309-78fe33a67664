/**
 * SlideInModalSystem Component for V2
 * Adobe CEP-compatible slide-in modal system with settings panel and sub-modal navigation
 * Exactly matching V1 structure with V2 modal components
 */

import React, { useCallback, useTransition } from 'react';
import { useModalStore, ModalType } from '../../../stores/modalStore';
import styles from './SlideInModalSystem.module.css';

// Modal content components - V2 versions
import { ChatHistoryModal, ProviderHealthModal, SettingsModal } from '../../modals';
import { SahAIModelConfiguration } from '../../SahAIModelConfiguration/SahAIModelConfiguration';

interface SlideInModalSystemProps {
  isOpen: boolean;
  onClose: () => void;
}

interface ModalConfig {
  id: ModalType;
  label: string;
  component: React.ComponentType<{ onClose: () => void; onBack: () => void }>;
}

// Modal categorization exactly like V1
const SETTINGS_ONLY_MODALS: ModalType[] = [
  'analytics',
  'advancedConfig',
  'modelComparison',
  'multiModel',
  'help',
  'about'
];

const INDEPENDENT_MODALS: ModalType[] = [
  'providerHealth',
  'chatHistory',
  'modelConfiguration'
];

const MODAL_CONFIGS: ModalConfig[] = [
  { id: 'providerHealth', label: 'Provider Health', component: ProviderHealthModal },
  { id: 'chatHistory', label: 'Chat History', component: ChatHistoryModal },
  { id: 'settings', label: 'Settings', component: SettingsModal },
  // Settings-only modals (placeholders for now)
  { id: 'analytics', label: 'Analytics', component: SettingsModal },
  { id: 'advancedConfig', label: 'Advanced Config', component: SettingsModal },
  { id: 'modelComparison', label: 'Model Comparison', component: SettingsModal },
  { id: 'multiModel', label: 'Multi Model', component: SettingsModal },
  { id: 'help', label: 'Help', component: SettingsModal },
  { id: 'about', label: 'About', component: SettingsModal },
];

// Filter modal configs for settings panel (exclude independent modals)
const SETTINGS_MODAL_CONFIGS = MODAL_CONFIGS.filter(config =>
  SETTINGS_ONLY_MODALS.includes(config.id)
);

// Settings categories exactly like V1
const SETTINGS_CATEGORIES = {
  core: {
    title: 'Core Features',
    items: ['analytics', 'advancedConfig']
  },
  models: {
    title: 'Model Tools',
    items: ['modelComparison', 'multiModel']
  },
  support: {
    title: 'Help & Support',
    items: ['help', 'about']
  }
};

// Helper functions exactly like V1
const getSettingsIcon = (configId: string): string => {
  const iconMap: Record<string, string> = {
    analytics: '📊',
    advancedConfig: '⚙️',
    modelComparison: '🔄',
    multiModel: '🤖',
    help: '❓',
    about: 'ℹ️'
  };
  return iconMap[configId] || '•';
};

const getSettingsDescription = (configId: string): string => {
  const descriptionMap: Record<string, string> = {
    analytics: 'View usage analytics and insights',
    advancedConfig: 'Advanced configuration options',
    modelComparison: 'Compare model performance',
    multiModel: 'Multi-model conversation tools',
    help: 'Help and documentation',
    about: 'About SahAI'
  };
  return descriptionMap[configId] || '';
};

export const SlideInModalSystem: React.FC<SlideInModalSystemProps> = ({
  isOpen,
  onClose,
}) => {
  const { currentModal, modalData, accessMethod } = useModalStore();
  const [isPending, startTransition] = useTransition();

  const handleOpenSubModal = useCallback((modalType: ModalType) => {
    if (modalType === 'settings') return;

    startTransition(() => {
      useModalStore.getState().openSubModal(modalType);
    });
  }, []);

  const handleBackToSettings = useCallback(() => {
    startTransition(() => {
      useModalStore.getState().goBackToSettings();
    });
  }, []);

  const handleClose = useCallback(() => {
    startTransition(() => {
      useModalStore.getState().closeModal();
    });
    onClose();
  }, [onClose]);

  // Determine if we're showing the main settings view - exactly like V1
  const isSettingsView = currentModal === 'settings' ||
    (currentModal && SETTINGS_ONLY_MODALS.includes(currentModal) && accessMethod === 'settings');

  // Find the current modal configuration
  const currentConfig = MODAL_CONFIGS.find(config => config.id === currentModal);

  // Handle breadcrumb navigation - exactly like V1
  const getBreadcrumbPath = () => {
    if (isSettingsView || !currentModal) return [];

    if (INDEPENDENT_MODALS.includes(currentModal)) {
      return [{ label: currentConfig?.label || 'Modal', onClick: null }];
    }

    return [
      { label: 'Settings', onClick: handleBackToSettings },
      { label: currentConfig?.label || 'Unknown', onClick: null }
    ];
  };

  const breadcrumbPath = getBreadcrumbPath();

  if (!isOpen) {
    return null;
  }

  return (
    <div
      className={`${styles.slideInModal} ${isOpen ? styles.open : ''}`}
      data-animating={isPending}
      role="dialog"
      aria-modal="true"
      aria-label={isSettingsView ? "Settings" : currentConfig?.label || "Modal"}
    >
      {/* Settings Panel - exactly like V1 */}
      <div className={`${styles.settingsPanel} ${!isSettingsView ? styles.hidden : ''}`}>
        <div className={styles.slideModalHeader}>
          <span className={styles.slideModalTitle}>Settings</span>
          <button
            className={styles.slideCloseButton}
            onClick={handleClose}
            aria-label="Close settings"
          >
            ×
          </button>
        </div>
        <div className={styles.slideModalBody}>
          {Object.entries(SETTINGS_CATEGORIES).map(([key, category]) => (
            <div key={key} className={styles.settingsSection}>
              <h3 className={styles.settingsSectionTitle}>{category.title}</h3>
              {category.items.map((configId) => {
                const config = SETTINGS_MODAL_CONFIGS.find(c => c.id === configId);
                if (!config) return null;

                return (
                  <button
                    key={configId}
                    className={styles.settingsLink}
                    onClick={() => handleOpenSubModal(configId as ModalType)}
                    aria-label={`Open ${config.label}`}
                  >
                    <span className={styles.settingsIcon}>{getSettingsIcon(configId)}</span>
                    <span className={styles.settingsText}>{config.label}</span>
                    <span className={styles.settingsArrow}>›</span>
                  </button>
                );
              })}
            </div>
          ))}
        </div>
      </div>

      {/* Sub-modal Panel - exactly like V1 */}
      {!isSettingsView && currentModal && (
        <div className={`${styles.subModalPanel} ${isSettingsView ? styles.hidden : ''}`}>
          <div className={styles.slideModalHeader}>
            {/* Breadcrumb navigation */}
            <div className={styles.breadcrumbNav}>
              {breadcrumbPath.map((crumb, index) => (
                <React.Fragment key={index}>
                  {index > 0 && <span className={styles.breadcrumbSeparator}>›</span>}
                  {crumb.onClick ? (
                    <button
                      className={styles.breadcrumbLink}
                      onClick={crumb.onClick}
                      aria-label={`Go back to ${crumb.label}`}
                    >
                      {crumb.label}
                    </button>
                  ) : (
                    <span className={styles.breadcrumbCurrent}>{crumb.label}</span>
                  )}
                </React.Fragment>
              ))}
            </div>
            <button
              className={styles.slideCloseButton}
              onClick={handleClose}
              aria-label="Close modal"
            >
              ×
            </button>
          </div>
          <div className={styles.slideModalBody}>
            {/* Handle modelConfiguration separately */}
            {currentModal === 'modelConfiguration' && (
              <SahAIModelConfiguration
                onClose={handleClose}
                isPopup={false}
              />
            )}

            {/* Render other modal content - exactly like V1 */}
            {currentConfig && currentModal !== 'modelConfiguration' && (
              <currentConfig.component
                onClose={handleClose}
                onBack={handleBackToSettings}
              />
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SlideInModalSystem;
