/**
 * SlideInModalSystem Styles for V2
 * Adobe CEP-compatible slide-in modal system with smooth animations
 * Based on V1 design with V2 Adobe theme variables
 * FIXED: Resolved conflicts with globals.css by using proper scoping and Adobe variables
 */

/* Main modal container */
.slideInModal {
  position: absolute;
  top: 0;
  right: -100%;
  width: 100%;
  height: 100%;
  background: var(--adobe-bg-secondary);
  border-left: 1px solid var(--adobe-border);
  box-shadow: -2px 0 6px rgba(0, 0, 0, 0.5);
  transition: right 0.3s ease;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  font-family: var(--adobe-font-family);
}

.slideInModal.open {
  right: 0;
}

/* Settings and Sub-modal panels */
.settingsPanel,
.subModalPanel {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease, opacity 0.3s ease;
  background: var(--adobe-bg-secondary);
  font-family: var(--adobe-font-family);
}

.settingsPanel.hidden {
  transform: translateX(-100%);
  opacity: 0;
}

.subModalPanel.hidden {
  transform: translateX(100%);
  opacity: 0;
}

/* Modal header */
.slideModalHeader {
  height: 48px;
  background: var(--adobe-bg-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  border-bottom: 1px solid var(--adobe-border);
  font-family: var(--adobe-font-family);
  flex-shrink: 0;
}

.slideModalTitle {
  color: var(--adobe-text-primary);
  font-weight: 600;
  font-size: 14px;
}

/* Breadcrumb navigation */
.breadcrumbNav {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.breadcrumbLink {
  color: var(--adobe-accent);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  padding: 0;
  text-decoration: none;
  transition: color 0.15s ease;
  font-family: var(--adobe-font-family);
}

.breadcrumbLink:hover {
  color: var(--adobe-accent-hover);
  text-decoration: underline;
}

.breadcrumbLink:focus-visible {
  outline: 2px solid var(--adobe-accent);
  outline-offset: 2px;
  border-radius: 2px;
}

.breadcrumbSeparator {
  color: var(--adobe-text-secondary);
  margin: 0 8px;
  font-size: 12px;
}

.breadcrumbCurrent {
  color: var(--adobe-text-primary);
  font-size: 12px;
  font-weight: 500;
}

/* Close button */
.slideCloseButton {
  width: 32px;
  height: 32px;
  background: transparent;
  border: none;
  color: var(--adobe-text-primary);
  font-size: 18px;
  cursor: pointer;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.15s ease;
  font-family: var(--adobe-font-family);
}

.slideCloseButton:hover {
  background: var(--adobe-bg-tertiary);
  color: var(--adobe-text-primary);
}

.slideCloseButton:focus,
.slideCloseButton:focus-visible {
  outline: 2px solid var(--adobe-accent);
  outline-offset: 2px;
}

.slideCloseButton:active {
  transform: scale(0.95);
}

/* Modal body */
.slideModalBody {
  padding: 16px;
  flex: 1;
  overflow-y: auto;
  background: var(--adobe-bg-secondary);
  font-family: var(--adobe-font-family);
}

/* Settings links */
.settingsLink {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  margin-bottom: 2px;
  font-size: 12px;
  color: var(--adobe-text-primary);
  background: transparent;
  border: none;
  cursor: pointer;
  text-align: left;
  padding: 12px 16px;
  border-radius: 6px;
  transition: all 0.15s ease;
  text-decoration: none;
  font-family: var(--adobe-font-family);
  font-weight: 400;
}

.settingsLink:hover {
  background: var(--adobe-bg-tertiary);
  color: var(--adobe-text-primary);
}

.settingsLink:focus,
.settingsLink:focus-visible {
  outline: 2px solid var(--adobe-accent);
  outline-offset: 2px;
}

.settingsLink:focus-visible {
  outline: 2px solid var(--adobe-accent);
  outline-offset: 2px;
}

.settingsIcon {
  font-size: 16px;
  width: 20px;
  text-align: center;
  flex-shrink: 0;
}

.settingsText {
  flex: 1;
  font-weight: 500;
}

.settingsArrow {
  color: var(--adobe-text-secondary);
  font-size: 14px;
  flex-shrink: 0;
}

/* Settings sections */
.settingsSection {
  margin-bottom: 24px;
}

.settingsSectionTitle {
  font-size: 14px;
  font-weight: 600;
  color: var(--adobe-text-primary);
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--adobe-border);
  font-family: var(--adobe-font-family);
}

/* Responsive adjustments for small CEP panels */
@media (max-width: 400px) {
  .slideModalHeader {
    padding: 0 8px;
    height: 40px;
  }

  .slideModalBody {
    padding: 12px;
  }

  .settingsLink {
    padding: 8px 12px;
    font-size: 11px;
    gap: 8px;
  }

  .slideModalTitle {
    font-size: 12px;
  }

  .breadcrumbLink,
  .breadcrumbCurrent {
    font-size: 11px;
  }

  .settingsSectionTitle {
    font-size: 12px;
  }
}

/* Animation states */
.slideInModal[data-animating="true"] {
  pointer-events: none;
}

.slideInModal[data-animating="true"] .settingsPanel,
.slideInModal[data-animating="true"] .subModalPanel {
  transition-duration: 0.15s;
}

/* Smooth scrolling for modal body */
.slideModalBody {
  scroll-behavior: smooth;
}

.slideModalBody::-webkit-scrollbar {
  width: 8px;
}

.slideModalBody::-webkit-scrollbar-track {
  background: var(--adobe-bg-primary);
}

.slideModalBody::-webkit-scrollbar-thumb {
  background: var(--adobe-border);
  border-radius: 4px;
}

.slideModalBody::-webkit-scrollbar-thumb:hover {
  background: var(--adobe-text-secondary);
}
