/**
 * UI Components Index for SahAI CEP Extension V2
 * Exports all reusable UI components
 */

// Icons
export {
  PlusIcon,
  HistoryIcon,
  MoreVerticalIcon,
  RefreshIcon,
  SearchIcon,
  TrashIcon,
  MessageSquareIcon,
  ClockIcon,
  CheckIcon,
  XIcon,
  SettingsIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  InfoIcon,
  AttachIcon,
  SendIcon,
  MicIcon,
  LoadingSpinner,
} from './Icons/Icons';

// Message Composer Components
export { default as CodeBlock } from './CodeBlock';
export { default as UserMessage } from './UserMessage';
export { default as AssistantMessage } from './AssistantMessage';
export { ChatBubble } from './ChatBubble';
export { default as MessageRenderer } from './MessageRenderer';
export { default as MessageInteractions } from './MessageInteractions';

// Re-export message factory utilities
export * from './MessageRenderer/MessageFactory';

// Modal System Components
export { ModalSystem } from './ModalSystem';
export { SlideInModalSystem } from './SlideInModal';
export { UnifiedModal } from './Modal';

// Status Components
export {
  StatusIndicator,
  createSuccessStatus,
  createErrorStatus,
} from './StatusIndicator';

// Re-export types
export type { CodeBlockData, CodeBlockAction } from '../../types/messages';
export type { RichChatMessage, MessageRendererProps } from '../../types/messages';
export type { ChatBubbleVariant } from './ChatBubble';
export type { StatusType, StatusSize, StatusVariant, StatusInfo } from './StatusIndicator';

// Note: Additional UI components will be exported here as they are created
// Examples of components that will be added:
// export { Button } from './Button/Button';
// export { Input } from './Input/Input';
// export { Modal } from './Modal/Modal';
// export { Select } from './Select/Select';
// export { Loading } from './Loading/Loading';
// export { Tooltip } from './Tooltip/Tooltip';
