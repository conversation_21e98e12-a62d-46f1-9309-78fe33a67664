import React, { useState, useEffect, useRef } from 'react';
import { createHighlighter, type Highlighter } from 'shiki';
import { CopyIcon, SaveIcon, ChevronDownIcon, ChevronRightIcon, PlayIcon } from '../Icons/Icons';
import { CodeBlockData, CodeBlockAction } from '../../../types/messages';
import './CodeBlock.css';

interface CodeBlockProps {
  data: CodeBlockData;
  onAction?: (action: CodeBlockAction) => void;
  className?: string;
  showToolbar?: boolean;
  maxHeight?: string;
}

const CodeBlock: React.FC<CodeBlockProps> = ({
  data,
  onAction,
  className = '',
  showToolbar = true,
  maxHeight = '400px'
}) => {
  const [highlighter, setHighlighter] = useState<Highlighter | null>(null);
  const [highlightedCode, setHighlightedCode] = useState<string>('');
  const [isCollapsed, setIsCollapsed] = useState(data.collapsed || false);
  const [copied, setCopied] = useState(false);
  const codeRef = useRef<HTMLDivElement>(null);

  // Initialize shiki highlighter
  useEffect(() => {
    const initHighlighter = async () => {
      try {
        const hl = await createHighlighter({
          themes: ['github-dark', 'github-light'],
          langs: [
            'javascript', 'typescript', 'python', 'java', 'cpp', 'c',
            'html', 'css', 'scss', 'json', 'xml', 'yaml', 'markdown',
            'bash', 'shell', 'sql', 'php', 'ruby', 'go', 'rust',
            'swift', 'kotlin', 'dart', 'r', 'matlab', 'powershell'
          ]
        });
        setHighlighter(hl);
      } catch (error) {
        console.error('Failed to initialize syntax highlighter:', error);
      }
    };

    initHighlighter();
  }, []);

  // Highlight code when highlighter or data changes
  useEffect(() => {
    if (highlighter && data.code) {
      try {
        const theme = document.documentElement.getAttribute('data-theme') === 'dark' 
          ? 'github-dark' 
          : 'github-light';
        
        const highlighted = highlighter.codeToHtml(data.code, {
          lang: data.language || 'text',
          theme: theme
        });
        setHighlightedCode(highlighted);
      } catch (error) {
        console.error('Failed to highlight code:', error);
        setHighlightedCode(`<pre><code>${data.code}</code></pre>`);
      }
    }
  }, [highlighter, data.code, data.language]);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(data.code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      onAction?.({
        type: 'copy',
        language: data.language,
        code: data.code,
        filename: data.filename
      });
    } catch (error) {
      console.error('Failed to copy code:', error);
    }
  };

  const handleSave = () => {
    const extension = getFileExtension(data.language);
    const filename = data.filename || `code.${extension}`;
    
    const blob = new Blob([data.code], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    onAction?.({
      type: 'save',
      language: data.language,
      code: data.code,
      filename: filename
    });
  };

  const handleCollapse = () => {
    const newCollapsed = !isCollapsed;
    setIsCollapsed(newCollapsed);
    onAction?.({
      type: newCollapsed ? 'collapse' : 'expand',
      language: data.language,
      code: data.code,
      filename: data.filename
    });
  };

  const handleRun = () => {
    onAction?.({
      type: 'run',
      language: data.language,
      code: data.code,
      filename: data.filename
    });
  };

  const getFileExtension = (language: string): string => {
    const extensions: Record<string, string> = {
      javascript: 'js',
      typescript: 'ts',
      python: 'py',
      java: 'java',
      cpp: 'cpp',
      c: 'c',
      html: 'html',
      css: 'css',
      scss: 'scss',
      json: 'json',
      xml: 'xml',
      yaml: 'yml',
      markdown: 'md',
      bash: 'sh',
      shell: 'sh',
      sql: 'sql',
      php: 'php',
      ruby: 'rb',
      go: 'go',
      rust: 'rs',
      swift: 'swift',
      kotlin: 'kt',
      dart: 'dart',
      r: 'r',
      matlab: 'm',
      powershell: 'ps1'
    };
    return extensions[language] || 'txt';
  };

  const isExecutable = (language: string): boolean => {
    const executableLanguages = [
      'javascript', 'typescript', 'python', 'bash', 'shell', 'powershell'
    ];
    return executableLanguages.includes(language);
  };

  return (
    <div className={`codeblock ${className}`}>
      {showToolbar && (
        <div className="codeblock-toolbar">
          <div className="codeblock-info">
            <button 
              className="codeblock-collapse-btn"
              onClick={handleCollapse}
              aria-label={isCollapsed ? 'Expand code' : 'Collapse code'}
            >
              {isCollapsed ? <ChevronRightIcon size={16} /> : <ChevronDownIcon size={16} />}
            </button>
            <span className="codeblock-language">{data.language}</span>
            {data.filename && (
              <span className="codeblock-filename">{data.filename}</span>
            )}
          </div>
          
          <div className="codeblock-actions">
            <button
              className="codeblock-action-btn"
              onClick={handleCopy}
              aria-label="Copy code"
              title="Copy to clipboard"
            >
              <CopyIcon size={16} />
              {copied && <span className="codeblock-copied">Copied!</span>}
            </button>

            <button
              className="codeblock-action-btn"
              onClick={handleSave}
              aria-label="Save code"
              title="Save as file"
            >
              <SaveIcon size={16} />
            </button>

            {isExecutable(data.language) && (
              <button
                className="codeblock-action-btn"
                onClick={handleRun}
                aria-label="Run code"
                title="Run in terminal"
              >
                <PlayIcon size={16} />
              </button>
            )}
          </div>
        </div>
      )}
      
      {!isCollapsed && (
        <div 
          className="codeblock-content"
          style={{ maxHeight }}
          ref={codeRef}
        >
          <div 
            className="codeblock-code"
            dangerouslySetInnerHTML={{ __html: highlightedCode }}
          />
        </div>
      )}
    </div>
  );
};

export default CodeBlock;
