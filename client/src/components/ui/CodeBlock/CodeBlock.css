.codeblock {
  border: 1px solid var(--cep-border-color, #3c3c3c);
  border-radius: 6px;
  background-color: var(--cep-editor-background, #1e1e1e);
  margin: 8px 0;
  overflow: hidden;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.codeblock-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: var(--cep-toolbar-background, #2d2d30);
  border-bottom: 1px solid var(--cep-border-color, #3c3c3c);
  min-height: 36px;
}

.codeblock-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.codeblock-collapse-btn {
  background: none;
  border: none;
  color: var(--cep-text-color, #cccccc);
  cursor: pointer;
  padding: 2px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.codeblock-collapse-btn:hover {
  background-color: var(--cep-hover-background, #404040);
}

.codeblock-language {
  font-size: 12px;
  font-weight: 600;
  color: var(--cep-accent-color, #007acc);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.codeblock-filename {
  font-size: 12px;
  color: var(--cep-text-secondary, #999999);
  font-style: italic;
}

.codeblock-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.codeblock-action-btn {
  background: none;
  border: none;
  color: var(--cep-text-color, #cccccc);
  cursor: pointer;
  padding: 6px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  position: relative;
  min-width: 28px;
  min-height: 28px;
}

.codeblock-action-btn:hover {
  background-color: var(--cep-hover-background, #404040);
  color: var(--cep-accent-color, #007acc);
}

.codeblock-action-btn:active {
  transform: scale(0.95);
}

.codeblock-copied {
  position: absolute;
  top: -24px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--cep-success-color, #4caf50);
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  white-space: nowrap;
  z-index: 10;
  animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0; }
  10%, 90% { opacity: 1; }
}

.codeblock-content {
  overflow: auto;
  background-color: var(--cep-editor-background, #1e1e1e);
}

.codeblock-code {
  padding: 16px;
  font-size: 13px;
  line-height: 1.5;
  color: var(--cep-text-color, #cccccc);
}

/* Override shiki styles to match CEP theme */
.codeblock-code pre {
  margin: 0;
  padding: 0;
  background: transparent !important;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  overflow: visible;
}

.codeblock-code code {
  background: transparent !important;
  padding: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

/* FIXED: Standardized scrollbar for code content to match globals.css */
.codeblock-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.codeblock-content::-webkit-scrollbar-track {
  background: var(--adobe-bg-primary);
}

.codeblock-content::-webkit-scrollbar-thumb {
  background: var(--adobe-border);
  border-radius: 4px;
}

.codeblock-content::-webkit-scrollbar-thumb:hover {
  background: var(--adobe-text-secondary);
}

/* Light theme adjustments */
[data-theme="light"] .codeblock {
  background-color: var(--cep-editor-background-light, #ffffff);
  border-color: var(--cep-border-color-light, #e1e1e1);
}

[data-theme="light"] .codeblock-toolbar {
  background-color: var(--cep-toolbar-background-light, #f3f3f3);
  border-bottom-color: var(--cep-border-color-light, #e1e1e1);
}

[data-theme="light"] .codeblock-collapse-btn {
  color: var(--cep-text-color-light, #333333);
}

[data-theme="light"] .codeblock-collapse-btn:hover {
  background-color: var(--cep-hover-background-light, #e8e8e8);
}

[data-theme="light"] .codeblock-language {
  color: var(--cep-accent-color-light, #0066cc);
}

[data-theme="light"] .codeblock-filename {
  color: var(--cep-text-secondary-light, #666666);
}

[data-theme="light"] .codeblock-action-btn {
  color: var(--cep-text-color-light, #333333);
}

[data-theme="light"] .codeblock-action-btn:hover {
  background-color: var(--cep-hover-background-light, #e8e8e8);
  color: var(--cep-accent-color-light, #0066cc);
}

[data-theme="light"] .codeblock-content {
  background-color: var(--cep-editor-background-light, #ffffff);
}

[data-theme="light"] .codeblock-code {
  color: var(--cep-text-color-light, #333333);
}

/* Responsive design */
@media (max-width: 480px) {
  .codeblock-toolbar {
    padding: 6px 8px;
    flex-wrap: wrap;
    gap: 4px;
  }
  
  .codeblock-info {
    flex: 1;
    min-width: 0;
  }
  
  .codeblock-filename {
    display: none;
  }
  
  .codeblock-actions {
    gap: 2px;
  }
  
  .codeblock-action-btn {
    padding: 4px;
    min-width: 24px;
    min-height: 24px;
  }
  
  .codeblock-code {
    padding: 12px;
    font-size: 12px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .codeblock {
    border-width: 2px;
  }
  
  .codeblock-toolbar {
    border-bottom-width: 2px;
  }
  
  .codeblock-action-btn:focus {
    outline: 2px solid var(--cep-focus-color, #007acc);
    outline-offset: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .codeblock-action-btn {
    transition: none;
  }
  
  .codeblock-action-btn:active {
    transform: none;
  }
  
  .codeblock-copied {
    animation: none;
    opacity: 1;
  }
}
