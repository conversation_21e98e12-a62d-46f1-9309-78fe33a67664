.assistant-message {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin: 16px 0;
  max-width: 100%;
}

.assistant-message-container {
  width: 100%;
  background-color: transparent; /* No bubble - same as UI background */
  position: relative;
}

.assistant-message-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.assistant-message-content-item {
  width: 100%;
}

.assistant-message-text {
  color: var(--cep-text-color, #ffffff);
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
}

.assistant-message-markdown {
  color: var(--cep-text-color, #ffffff);
  font-size: 14px;
  line-height: 1.6;
}

/* Markdown styling */
.assistant-message-markdown h1,
.assistant-message-markdown h2,
.assistant-message-markdown h3,
.assistant-message-markdown h4,
.assistant-message-markdown h5,
.assistant-message-markdown h6 {
  color: var(--cep-text-color, #ffffff);
  margin: 16px 0 8px 0;
  font-weight: 600;
}

.assistant-message-markdown h1 { font-size: 1.5em; }
.assistant-message-markdown h2 { font-size: 1.3em; }
.assistant-message-markdown h3 { font-size: 1.1em; }
.assistant-message-markdown h4 { font-size: 1em; }

.assistant-message-markdown p {
  margin: 8px 0;
}

.assistant-message-markdown ul,
.assistant-message-markdown ol {
  margin: 8px 0;
  padding-left: 20px;
}

.assistant-message-markdown li {
  margin: 4px 0;
}

.assistant-message-markdown blockquote {
  border-left: 3px solid var(--cep-accent-color, #007acc);
  margin: 12px 0;
  padding: 8px 16px;
  background-color: var(--cep-blockquote-background, rgba(0, 122, 204, 0.1));
  border-radius: 0 4px 4px 0;
}

.assistant-message-markdown code {
  background-color: var(--cep-inline-code-background, #3c3c3c);
  color: var(--cep-inline-code-color, #e06c75);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.9em;
}

.assistant-message-markdown table {
  border-collapse: collapse;
  margin: 12px 0;
  width: 100%;
}

.assistant-message-markdown th,
.assistant-message-markdown td {
  border: 1px solid var(--cep-border-color, #555555);
  padding: 8px 12px;
  text-align: left;
}

.assistant-message-markdown th {
  background-color: var(--cep-table-header-background, #404040);
  font-weight: 600;
}

.assistant-message-markdown a {
  color: var(--cep-link-color, #007acc);
  text-decoration: none;
}

.assistant-message-markdown a:hover {
  text-decoration: underline;
}

/* Tool use styling */
.assistant-message-tool-use {
  border: 1px solid var(--cep-tool-border, #555555);
  border-radius: 6px;
  background-color: var(--cep-tool-background, #2d2d30);
  margin: 8px 0;
  overflow: hidden;
}

.tool-use-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: var(--cep-tool-header-background, #404040);
  border-bottom: 1px solid var(--cep-border-color, #555555);
}

.tool-use-icon {
  font-size: 16px;
}

.tool-use-name {
  font-size: 13px;
  font-weight: 600;
  color: var(--cep-accent-color, #007acc);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tool-use-input {
  padding: 0;
}

/* Tool result styling */
.assistant-message-tool-result {
  border: 1px solid var(--cep-border-color, #555555);
  border-radius: 6px;
  margin: 8px 0;
  overflow: hidden;
}

.assistant-message-tool-result.success {
  border-color: var(--cep-success-color, #4caf50);
}

.assistant-message-tool-result.error {
  border-color: var(--cep-error-color, #f44336);
}

.tool-result-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: var(--cep-tool-header-background, #404040);
}

.assistant-message-tool-result.success .tool-result-header {
  background-color: var(--cep-success-background, rgba(76, 175, 80, 0.1));
}

.assistant-message-tool-result.error .tool-result-header {
  background-color: var(--cep-error-background, rgba(244, 67, 54, 0.1));
}

.tool-result-icon {
  font-size: 14px;
}

.tool-result-label {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.assistant-message-tool-result.success .tool-result-label {
  color: var(--cep-success-color, #4caf50);
}

.assistant-message-tool-result.error .tool-result-label {
  color: var(--cep-error-color, #f44336);
}

.tool-result-content {
  padding: 12px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
  background-color: var(--cep-code-background, #1e1e1e);
  color: var(--cep-text-color, #ffffff);
}

/* Footer styling */
.assistant-message-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid var(--cep-border-color, rgba(85, 85, 85, 0.3));
}

.assistant-message-metadata {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 11px;
  color: var(--cep-text-secondary, #999999);
}

.assistant-message-provider,
.assistant-message-model,
.assistant-message-tokens {
  padding: 2px 6px;
  background-color: var(--cep-metadata-background, rgba(85, 85, 85, 0.3));
  border-radius: 3px;
  font-family: monospace;
}

.assistant-message-actions {
  display: flex;
  gap: 4px;
}

.assistant-message-action-btn {
  background: none;
  border: none;
  color: var(--cep-text-secondary, #999999);
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  position: relative;
  min-width: 26px;
  min-height: 26px;
}

.assistant-message-action-btn:hover {
  background-color: var(--cep-hover-background, #404040);
  color: var(--cep-accent-color, #007acc);
}

.assistant-message-action-btn:active {
  transform: scale(0.95);
}

.assistant-message-copied {
  position: absolute;
  top: -24px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--cep-success-color, #4caf50);
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  white-space: nowrap;
  z-index: 10;
  animation: fadeInOut 2s ease-in-out;
}

.assistant-message-timestamp {
  font-size: 11px;
  color: var(--cep-text-secondary, #999999);
  margin-top: 4px;
  margin-left: 4px;
  font-family: monospace;
}

/* Light theme adjustments */
[data-theme="light"] .assistant-message-text,
[data-theme="light"] .assistant-message-markdown {
  color: var(--cep-text-color-light, #333333);
}

[data-theme="light"] .assistant-message-markdown h1,
[data-theme="light"] .assistant-message-markdown h2,
[data-theme="light"] .assistant-message-markdown h3,
[data-theme="light"] .assistant-message-markdown h4,
[data-theme="light"] .assistant-message-markdown h5,
[data-theme="light"] .assistant-message-markdown h6 {
  color: var(--cep-text-color-light, #333333);
}

[data-theme="light"] .assistant-message-markdown blockquote {
  border-left-color: var(--cep-accent-color-light, #0066cc);
  background-color: var(--cep-blockquote-background-light, rgba(0, 102, 204, 0.1));
}

[data-theme="light"] .assistant-message-markdown code {
  background-color: var(--cep-inline-code-background-light, #f5f5f5);
  color: var(--cep-inline-code-color-light, #d73a49);
}

[data-theme="light"] .assistant-message-markdown th,
[data-theme="light"] .assistant-message-markdown td {
  border-color: var(--cep-border-color-light, #d0d0d0);
}

[data-theme="light"] .assistant-message-markdown th {
  background-color: var(--cep-table-header-background-light, #f5f5f5);
}

[data-theme="light"] .assistant-message-markdown a {
  color: var(--cep-link-color-light, #0066cc);
}

[data-theme="light"] .assistant-message-tool-use {
  border-color: var(--cep-tool-border-light, #d0d0d0);
  background-color: var(--cep-tool-background-light, #ffffff);
}

[data-theme="light"] .tool-use-header {
  background-color: var(--cep-tool-header-background-light, #f5f5f5);
  border-bottom-color: var(--cep-border-color-light, #d0d0d0);
}

[data-theme="light"] .tool-use-name {
  color: var(--cep-accent-color-light, #0066cc);
}

[data-theme="light"] .assistant-message-tool-result {
  border-color: var(--cep-border-color-light, #d0d0d0);
}

[data-theme="light"] .tool-result-header {
  background-color: var(--cep-tool-header-background-light, #f5f5f5);
}

[data-theme="light"] .tool-result-content {
  background-color: var(--cep-code-background-light, #ffffff);
  color: var(--cep-text-color-light, #333333);
}

[data-theme="light"] .assistant-message-footer {
  border-top-color: var(--cep-border-color-light, rgba(208, 208, 208, 0.5));
}

[data-theme="light"] .assistant-message-metadata {
  color: var(--cep-text-secondary-light, #666666);
}

[data-theme="light"] .assistant-message-provider,
[data-theme="light"] .assistant-message-model,
[data-theme="light"] .assistant-message-tokens {
  background-color: var(--cep-metadata-background-light, rgba(208, 208, 208, 0.3));
}

[data-theme="light"] .assistant-message-action-btn {
  color: var(--cep-text-secondary-light, #666666);
}

[data-theme="light"] .assistant-message-action-btn:hover {
  background-color: var(--cep-hover-background-light, #f0f0f0);
  color: var(--cep-accent-color-light, #0066cc);
}

[data-theme="light"] .assistant-message-timestamp {
  color: var(--cep-text-secondary-light, #666666);
}

/* Responsive design */
@media (max-width: 768px) {
  .assistant-message-text,
  .assistant-message-markdown {
    font-size: 13px;
  }
  
  .assistant-message-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .assistant-message-metadata {
    flex-wrap: wrap;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .assistant-message-text,
  .assistant-message-markdown {
    font-size: 12px;
  }
  
  .assistant-message-metadata {
    font-size: 10px;
  }
  
  .assistant-message-timestamp {
    font-size: 10px;
  }
}

/* Animation keyframes */
@keyframes fadeInOut {
  0%, 100% { opacity: 0; }
  10%, 90% { opacity: 1; }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .assistant-message-tool-use,
  .assistant-message-tool-result {
    border-width: 2px;
  }
  
  .assistant-message-action-btn:focus {
    outline: 2px solid var(--cep-focus-color, #007acc);
    outline-offset: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .assistant-message-action-btn {
    transition: none;
  }
  
  .assistant-message-action-btn:active {
    transform: none;
  }
  
  .assistant-message-copied {
    animation: none;
    opacity: 1;
  }
}
