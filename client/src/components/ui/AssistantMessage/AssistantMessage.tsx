import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { CopyIcon } from '../Icons/Icons';
import { RichChatMessage, MessageContent, CodeBlockAction } from '../../../types/messages';
import CodeBlock from '../CodeBlock';
import './AssistantMessage.css';

interface AssistantMessageProps {
  message: RichChatMessage;
  onCopy?: (content: string) => void;
  onCodeAction?: (action: CodeBlockAction) => void;
  className?: string;
}

const AssistantMessage: React.FC<AssistantMessageProps> = ({
  message,
  onCopy,
  onCodeAction,
  className = ''
}) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    const contentToCopy = getPlainTextContent();
    try {
      await navigator.clipboard.writeText(contentToCopy);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      onCopy?.(contentToCopy);
    } catch (error) {
      console.error('Failed to copy message:', error);
    }
  };

  const getPlainTextContent = (): string => {
    if (typeof message.content === 'string') {
      return message.content;
    }
    
    return message.content
      .map(content => {
        switch (content.type) {
          case 'text':
            return content.text || '';
          case 'markdown':
            return content.markdown || '';
          case 'code':
            return content.code?.code || '';
          case 'tool_use':
            return `Tool: ${content.toolUse?.name}\nInput: ${JSON.stringify(content.toolUse?.input, null, 2)}`;
          case 'tool_result':
            return `Result: ${content.toolResult?.content || ''}`;
          default:
            return '';
        }
      })
      .join('\n\n');
  };

  const formatTimestamp = (timestamp: number | { created: number; updated?: number }) => {
    const time = typeof timestamp === 'number' ? timestamp : timestamp.created;
    return new Date(time).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
  };

  const renderContent = () => {
    if (typeof message.content === 'string') {
      return (
        <div className="assistant-message-text">
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={{
              code: ({ node, className, children, ...props }: any) => {
                const inline = (props as any).inline;
                const match = /language-(\w+)/.exec(className || '');
                const language = match ? match[1] : 'text';
                
                if (!inline && children) {
                  const code = String(children).replace(/\n$/, '');
                  return (
                    <CodeBlock
                      data={{
                        language,
                        code,
                        collapsed: false
                      }}
                      onAction={onCodeAction}
                    />
                  );
                }
                
                return (
                  <code className={className} {...props}>
                    {children}
                  </code>
                );
              },
              pre: ({ children }) => <>{children}</>,
            }}
          >
            {message.content}
          </ReactMarkdown>
        </div>
      );
    }

    return (
      <div className="assistant-message-content">
        {message.content.map((content, index) => (
          <div key={index} className="assistant-message-content-item">
            {renderContentItem(content)}
          </div>
        ))}
      </div>
    );
  };

  const renderContentItem = (content: MessageContent) => {
    switch (content.type) {
      case 'text':
        return (
          <div className="assistant-message-text">
            {content.text}
          </div>
        );

      case 'markdown':
        return (
          <div className="assistant-message-markdown">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                code: ({ node, className, children, ...props }: any) => {
                  const inline = (props as any).inline;
                  const match = /language-(\w+)/.exec(className || '');
                  const language = match ? match[1] : 'text';
                  
                  if (!inline && children) {
                    const code = String(children).replace(/\n$/, '');
                    return (
                      <CodeBlock
                        data={{
                          language,
                          code,
                          collapsed: false
                        }}
                        onAction={onCodeAction}
                      />
                    );
                  }
                  
                  return (
                    <code className={className} {...props}>
                      {children}
                    </code>
                  );
                },
                pre: ({ children }) => <>{children}</>,
              }}
            >
              {content.markdown || ''}
            </ReactMarkdown>
          </div>
        );

      case 'code':
        return content.code ? (
          <CodeBlock
            data={content.code}
            onAction={onCodeAction}
          />
        ) : null;

      case 'tool_use':
        return content.toolUse ? (
          <div className="assistant-message-tool-use">
            <div className="tool-use-header">
              <span className="tool-use-icon">🔧</span>
              <span className="tool-use-name">{content.toolUse.name}</span>
            </div>
            <div className="tool-use-input">
              <CodeBlock
                data={{
                  language: 'json',
                  code: JSON.stringify(content.toolUse.input, null, 2),
                  collapsed: true
                }}
                onAction={onCodeAction}
              />
            </div>
          </div>
        ) : null;

      case 'tool_result':
        return content.toolResult ? (
          <div className={`assistant-message-tool-result ${content.toolResult.isError ? 'error' : 'success'}`}>
            <div className="tool-result-header">
              <span className="tool-result-icon">
                {content.toolResult.isError ? '❌' : '✅'}
              </span>
              <span className="tool-result-label">
                {content.toolResult.isError ? 'Error' : 'Result'}
              </span>
            </div>
            <div className="tool-result-content">
              {content.toolResult.content}
            </div>
          </div>
        ) : null;

      default:
        return null;
    }
  };

  return (
    <div className={`assistant-message ${className}`}>
      <div className="assistant-message-container">
        {renderContent()}
        
        <div className="assistant-message-footer">
          <div className="assistant-message-metadata">
            {message.metadata?.provider && (
              <span className="assistant-message-provider">
                {message.metadata.provider}
              </span>
            )}
            {message.metadata?.model && (
              <span className="assistant-message-model">
                {message.metadata.model}
              </span>
            )}
            {message.metadata?.tokens && (
              <span className="assistant-message-tokens">
                {message.metadata.tokens} tokens
              </span>
            )}
          </div>
          
          <div className="assistant-message-actions">
            <button
              className="assistant-message-action-btn"
              onClick={handleCopy}
              title="Copy message"
            >
              <CopyIcon size={16} />
              {copied && <span className="assistant-message-copied">Copied!</span>}
            </button>
          </div>
        </div>
      </div>
      
      <div className="assistant-message-timestamp">
        {formatTimestamp(message.timestamp)}
      </div>
    </div>
  );
};

export default AssistantMessage;
