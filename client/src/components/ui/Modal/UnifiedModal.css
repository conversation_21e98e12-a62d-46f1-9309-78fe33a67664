/**
 * Unified Modal Styles for V2
 * Slide-in modal system for consistent V2 user experience
 * Adobe CEP-compatible styling with consistent design
 */

/* ===== SLIDE-IN MODAL (V2 STANDARD) ===== */

.slide-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 1000;
  pointer-events: none;
  overflow: hidden;
}

.slide-modal-overlay.open {
  pointer-events: auto;
}

.slide-modal {
  position: absolute;
  top: 0;
  right: -100%;
  width: 100%;
  height: 100%;
  background: var(--adobe-bg-secondary);
  border-left: 1px solid var(--adobe-border);
  box-shadow: -2px 0 6px rgba(0, 0, 0, 0.5);
  transition: right 0.3s ease;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  font-family: var(--adobe-font-family);
}

.slide-modal.open {
  right: 0;
}

.slide-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--adobe-border);
  background: var(--adobe-bg-primary);
  min-height: 44px;
  flex-shrink: 0;
}

.slide-header-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.slide-back-button {
  background: transparent;
  border: none;
  color: var(--adobe-text-secondary);
  font-size: 14px;
  cursor: pointer;
  padding: 6px 8px;
  border-radius: 3px;
  font-family: var(--adobe-font-family);
  transition: all 0.2s ease;
  outline: none;
  flex-shrink: 0;
}

.slide-back-button:hover {
  background: var(--adobe-bg-secondary);
  color: var(--adobe-text-primary);
}

.slide-back-button:focus {
  background: var(--adobe-bg-secondary);
  box-shadow: 0 0 0 1px var(--adobe-accent);
}

.slide-modal-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--adobe-text-primary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
  flex: 1;
}

.slide-close-button {
  background: transparent;
  border: none;
  color: var(--adobe-text-secondary);
  font-size: 20px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 3px;
  font-family: var(--adobe-font-family);
  transition: all 0.2s ease;
  outline: none;
  flex-shrink: 0;
}

.slide-close-button:hover {
  background: var(--adobe-bg-secondary);
  color: var(--adobe-text-primary);
}

.slide-close-button:focus {
  background: var(--adobe-bg-secondary);
  box-shadow: 0 0 0 1px var(--adobe-accent);
}

.slide-modal-body {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: var(--adobe-bg-primary);
}

/* ===== SHARED STYLES ===== */

/* Scrollbar styling for both variants */
.modal-body::-webkit-scrollbar,
.slide-modal-body::-webkit-scrollbar {
  width: 8px;
}

.modal-body::-webkit-scrollbar-track,
.slide-modal-body::-webkit-scrollbar-track {
  background: var(--adobe-bg-primary);
}

.modal-body::-webkit-scrollbar-thumb,
.slide-modal-body::-webkit-scrollbar-thumb {
  background: var(--adobe-border);
  border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover,
.slide-modal-body::-webkit-scrollbar-thumb:hover {
  background: var(--adobe-text-secondary);
}

/* Responsive design */
@media (max-width: 400px) {
  .modal-container {
    width: 95vw;
    max-width: none;
  }
  
  .modal-header,
  .slide-modal-header {
    padding: 10px 12px;
  }
  
  .modal-header h2,
  .slide-modal-title {
    font-size: 14px;
  }
  
  .slide-back-button {
    font-size: 12px;
    padding: 4px 6px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .modal-backdrop {
    background: rgba(0, 0, 0, 0.9);
  }
  
  .modal-container,
  .slide-modal {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .slide-modal {
    transition: none;
  }
}
