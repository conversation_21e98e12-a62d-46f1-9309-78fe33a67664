/* SahAI Model Configuration Styles */
/* FIXED: Converted all CEP variables to Adobe variables for consistency */

.sahai-model-configuration {
  background: var(--adobe-bg-primary);
  color: var(--adobe-text-primary);
  font-family: var(--adobe-font-family);
  font-size: 12px;
  display: flex;
  flex-direction: column;
}

.sahai-model-configuration.popup {
  max-height: 80vh;
  overflow-y: auto;
  padding: 16px;
  gap: 16px;
}

/* Modal header for full modal layout */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--adobe-border);
  background: var(--adobe-bg-secondary);
  margin-bottom: 16px;
}

.modal-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--adobe-text-primary);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.demo-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.demo-button:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.demo-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: transparent;
  border: none;
  border-radius: 3px;
  color: var(--adobe-text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: var(--adobe-bg-tertiary);
}

.close-button:focus {
  outline: 2px solid var(--adobe-accent);
  outline-offset: 2px;
}

/* Content area */
.sahai-model-configuration:not(.popup) {
  padding: 0 16px 16px 16px;
}

/* Config sections */
.config-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-label {
  font-size: 12px;
  font-weight: 600;
  color: var(--adobe-text-primary);
  margin-bottom: 4px;
}

/* Dropdown styles */
.dropdown-container {
  position: relative;
}

.dropdown-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 8px 12px;
  background: var(--adobe-bg-primary);
  border: 1px solid var(--adobe-border);
  border-radius: 3px;
  color: var(--adobe-text-primary);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
}

.dropdown-trigger:hover {
  border-color: var(--adobe-border);
  background: var(--adobe-bg-tertiary);
}

.dropdown-trigger:focus {
  border-color: var(--adobe-accent);
  box-shadow: 0 0 0 1px var(--adobe-accent);
}

.dropdown-trigger.open {
  border-color: var(--adobe-accent);
}

.dropdown-text {
  flex: 1;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-icon {
  flex-shrink: 0;
  transition: transform 0.2s ease;
}

.dropdown-trigger.open .dropdown-icon {
  transform: rotate(180deg);
}

/* Dropdown menu */
.dropdown-menu {
  position: absolute;
  top: calc(100% + 2px);
  left: 0;
  right: 0;
  background: var(--adobe-bg-primary);
  border: 1px solid var(--adobe-border);
  border-radius: 3px;
  box-shadow: var(--adobe-shadow);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.dropdown-menu.model-dropdown {
  max-height: 300px;
}

.dropdown-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 8px 12px;
  background: var(--adobe-bg-primary);
  border: none;
  color: var(--adobe-text-primary);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.dropdown-item:hover {
  background: var(--adobe-bg-tertiary);
}

.dropdown-item.selected {
  background: var(--adobe-accent);
  color: white;
}

.provider-name {
  flex: 1;
}

.configured-icon {
  color: var(--adobe-success);
}

/* Model item styles */
.model-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.model-name {
  font-weight: 500;
}

.model-description {
  font-size: 11px;
  opacity: 0.7;
  line-height: 1.3;
}

/* Provider description */
.provider-description {
  margin: 0;
  font-size: 11px;
  color: var(--adobe-text-secondary);
  line-height: 1.4;
}

/* API Key input */
.api-key-input-container {
  display: flex;
  gap: 8px;
  align-items: center;
}

.api-key-input {
  flex: 1;
  padding: 8px 12px;
  background: var(--adobe-bg-primary);
  border: 1px solid var(--adobe-border);
  border-radius: 3px;
  color: var(--adobe-text-primary);
  font-size: 12px;
  outline: none;
  transition: border-color 0.2s ease;
}

.api-key-input:focus {
  border-color: var(--adobe-accent);
  box-shadow: 0 0 0 1px var(--adobe-accent);
}

.api-key-submit {
  padding: 8px 16px;
  background: var(--adobe-accent);
  border: none;
  border-radius: 3px;
  color: white;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.api-key-submit:hover:not(:disabled) {
  background: var(--adobe-accent-hover);
}

.api-key-submit:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.api-key-help {
  margin: 4px 0 0 0;
  font-size: 11px;
  color: var(--adobe-text-secondary);
}

.api-key-link {
  color: var(--adobe-accent);
  text-decoration: none;
}

.api-key-link:hover {
  text-decoration: underline;
}

/* Model search */
.model-search-container {
  position: relative;
  margin-bottom: 8px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--adobe-text-secondary);
  pointer-events: none;
}

.model-search-input {
  width: 100%;
  padding: 8px 12px 8px 36px;
  background: var(--adobe-bg-primary);
  border: 1px solid var(--adobe-border);
  border-radius: 3px;
  color: var(--adobe-text-primary);
  font-size: 12px;
  outline: none;
  transition: border-color 0.2s ease;
}

.model-search-input:focus {
  border-color: var(--adobe-accent);
  box-shadow: 0 0 0 1px var(--adobe-accent);
}

/* Loading and error states */
.loading-state,
.error-state,
.no-models {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  color: var(--adobe-text-secondary);
  font-size: 12px;
}

.error-state {
  color: var(--adobe-error);
}

.loading-icon {
  animation: spin 1s linear infinite;
}

.error-icon {
  color: var(--adobe-error);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Model information */
.model-info {
  background: rgba(70, 160, 245, 0.1);
  border: 1px solid rgba(70, 160, 245, 0.2);
  border-radius: 3px;
  padding: 12px;
}

.model-info-item {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  align-items: flex-start;
}

.model-info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-weight: 600;
  min-width: 80px;
  color: var(--adobe-text-primary);
}

.info-value {
  flex: 1;
  color: var(--adobe-text-secondary);
}

.capabilities-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.capability-tag {
  padding: 2px 6px;
  background: var(--adobe-bg-tertiary);
  border-radius: 2px;
  font-size: 10px;
  color: var(--adobe-text-primary);
}

/* Responsive adjustments */
@media (max-width: 500px) {
  .sahai-model-configuration {
    min-width: 300px;
    max-width: 90vw;
  }
  
  .configuration-content {
    padding: 12px;
  }
  
  .api-key-input-container {
    flex-direction: column;
    align-items: stretch;
  }
}

/* Adobe theme variables automatically handle light/dark themes */
