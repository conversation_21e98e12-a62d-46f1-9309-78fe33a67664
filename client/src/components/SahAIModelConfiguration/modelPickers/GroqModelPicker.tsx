/**
 * Specialized Model Picker for Groq
 * Enhanced with search, filtering, and keyboard navigation
 */

import React, { useState, useMemo, useRef, useEffect } from 'react';
import { ModelInfo } from '../../../stores/settingsStore';
import { SearchIcon, InfoIcon, ChevronDownIcon } from '../../ui';

interface GroqModelPickerProps {
  models: ModelInfo[];
  selectedModelId: string;
  onModelSelect: (modelId: string) => void;
  loading?: boolean;
  error?: string;
  isPopup?: boolean;
}

export const GroqModelPicker: React.FC<GroqModelPickerProps> = ({
  models,
  selectedModelId,
  onModelSelect,
  loading = false,
  error,
  isPopup = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Categorize models by provider/family
  const categorizedModels = useMemo(() => {
    const categories: Record<string, ModelInfo[]> = {
      'llama': [],
      'mixtral': [],
      'gemma': [],
      'other': []
    };

    models.forEach(model => {
      const modelName = model.name.toLowerCase();
      if (modelName.includes('llama')) {
        categories.llama.push(model);
      } else if (modelName.includes('mixtral')) {
        categories.mixtral.push(model);
      } else if (modelName.includes('gemma')) {
        categories.gemma.push(model);
      } else {
        categories.other.push(model);
      }
    });

    return categories;
  }, [models]);

  // Filter models based on search and category
  const filteredModels = useMemo(() => {
    let modelsToFilter = models;
    
    if (selectedCategory !== 'all') {
      modelsToFilter = categorizedModels[selectedCategory] || [];
    }

    if (!searchQuery.trim()) {
      return modelsToFilter;
    }
    
    const query = searchQuery.toLowerCase();
    return modelsToFilter.filter(model =>
      model.name.toLowerCase().includes(query) ||
      model.id.toLowerCase().includes(query) ||
      (model.description && model.description.toLowerCase().includes(query))
    );
  }, [models, categorizedModels, selectedCategory, searchQuery]);

  const selectedModel = models.find(m => m.id === selectedModelId);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setFocusedIndex(prev => 
            prev < filteredModels.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setFocusedIndex(prev => 
            prev > 0 ? prev - 1 : filteredModels.length - 1
          );
          break;
        case 'Enter':
          e.preventDefault();
          if (focusedIndex >= 0 && filteredModels[focusedIndex]) {
            handleModelSelect(filteredModels[focusedIndex].id);
          }
          break;
        case 'Escape':
          e.preventDefault();
          setIsOpen(false);
          setFocusedIndex(-1);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, focusedIndex, filteredModels]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setFocusedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleModelSelect = (modelId: string) => {
    onModelSelect(modelId);
    setIsOpen(false);
    setSearchQuery('');
    setFocusedIndex(-1);
  };

  const handleToggle = () => {
    if (!loading) {
      setIsOpen(!isOpen);
      if (!isOpen) {
        setTimeout(() => searchInputRef.current?.focus(), 100);
      }
    }
  };

  const formatSpeed = (model: ModelInfo) => {
    // Groq is known for speed, show estimated tokens/sec if available
    if (model.id.includes('8b')) return '~500 tok/s';
    if (model.id.includes('70b')) return '~100 tok/s';
    if (model.id.includes('7b')) return '~600 tok/s';
    return 'Ultra-fast';
  };

  if (loading) {
    return (
      <div className="groq-model-picker loading">
        <InfoIcon size={16} className="loading-icon" />
        <span>Loading Groq models...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="groq-model-picker error">
        <InfoIcon size={16} className="error-icon" />
        <span>Error: {error}</span>
      </div>
    );
  }

  return (
    <div className="groq-model-picker" ref={dropdownRef}>
      <button
        className={`model-picker-trigger ${isOpen ? 'open' : ''}`}
        onClick={handleToggle}
        aria-expanded={isOpen}
      >
        <span className="selected-model-text">
          {selectedModel ? selectedModel.name : 'Select Groq model'}
        </span>
        <ChevronDownIcon size={14} className="dropdown-icon" />
      </button>
      
      {isOpen && (
        <div className="model-picker-dropdown">
          <div className="picker-header">
            <div className="search-container">
              <SearchIcon size={14} className="search-icon" />
              <input
                ref={searchInputRef}
                type="text"
                className="model-search-input"
                placeholder="Search Groq models..."
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                  setFocusedIndex(-1);
                }}
              />
            </div>
            
            <div className="category-filters">
              {Object.keys(categorizedModels).map(category => (
                <button
                  key={category}
                  className={`category-filter ${selectedCategory === category ? 'active' : ''}`}
                  onClick={() => {
                    setSelectedCategory(category);
                    setFocusedIndex(-1);
                  }}
                >
                  {category === 'all' ? 'All' : category.charAt(0).toUpperCase() + category.slice(1)}
                  <span className="category-count">
                    ({category === 'all' ? models.length : categorizedModels[category]?.length || 0})
                  </span>
                </button>
              ))}
            </div>
          </div>
          
          <div className="models-list">
            {filteredModels.length > 0 ? (
              filteredModels.map((model, index) => (
                <button
                  key={model.id}
                  className={`model-item ${selectedModelId === model.id ? 'selected' : ''} ${
                    index === focusedIndex ? 'focused' : ''
                  }`}
                  onClick={() => handleModelSelect(model.id)}
                  onMouseEnter={() => setFocusedIndex(index)}
                >
                  <div className="model-info">
                    <span className="model-name">{model.name}</span>
                    <div className="model-meta">
                      <span className="model-speed">{formatSpeed(model)}</span>
                      {model.contextLength && (
                        <span className="model-context">
                          {model.contextLength.toLocaleString()} ctx
                        </span>
                      )}
                    </div>
                    {model.description && (
                      <span className="model-description">{model.description}</span>
                    )}
                  </div>
                </button>
              ))
            ) : (
              <div className="no-models">
                <InfoIcon size={16} />
                <span>{searchQuery ? 'No models found' : 'No models available'}</span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
