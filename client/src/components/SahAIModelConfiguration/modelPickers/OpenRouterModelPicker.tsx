/**
 * Specialized Model Picker for OpenRouter
 * Enhanced with search, filtering, favorites, and extensive model catalog
 */

import React, { useState, useMemo, useRef, useEffect } from 'react';
import { ModelInfo } from '../../../stores/settingsStore';
import { SearchIcon, InfoIcon, ChevronDownIcon, StarIcon } from '../../ui';

interface OpenRouterModelPickerProps {
  models: ModelInfo[];
  selectedModelId: string;
  onModelSelect: (modelId: string) => void;
  loading?: boolean;
  error?: string;
  isPopup?: boolean;
}

export const OpenRouterModelPicker: React.FC<OpenRouterModelPickerProps> = ({
  models,
  selectedModelId,
  onModelSelect,
  loading = false,
  error,
  isPopup = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'name' | 'price' | 'context'>('name');
  const [showFeatured, setShowFeatured] = useState(false);
  
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Featured/popular models for OpenRouter
  const featuredModels = [
    'openai/gpt-4',
    'openai/gpt-4-turbo',
    'anthropic/claude-3-sonnet',
    'anthropic/claude-3-haiku',
    'google/gemini-pro',
    'meta-llama/llama-3-70b-instruct',
    'mistralai/mixtral-8x7b-instruct',
    'cohere/command-r-plus'
  ];

  // Categorize models by provider
  const categorizedModels = useMemo(() => {
    const categories: Record<string, ModelInfo[]> = {
      'openai': [],
      'anthropic': [],
      'google': [],
      'meta': [],
      'mistral': [],
      'cohere': [],
      'other': []
    };

    models.forEach(model => {
      const modelId = model.id.toLowerCase();
      if (modelId.includes('openai/') || modelId.includes('gpt')) {
        categories.openai.push(model);
      } else if (modelId.includes('anthropic/') || modelId.includes('claude')) {
        categories.anthropic.push(model);
      } else if (modelId.includes('google/') || modelId.includes('gemini')) {
        categories.google.push(model);
      } else if (modelId.includes('meta/') || modelId.includes('llama')) {
        categories.meta.push(model);
      } else if (modelId.includes('mistral')) {
        categories.mistral.push(model);
      } else if (modelId.includes('cohere')) {
        categories.cohere.push(model);
      } else {
        categories.other.push(model);
      }
    });

    return categories;
  }, [models]);

  // Filter and sort models
  const filteredModels = useMemo(() => {
    let modelsToFilter = models;
    
    // Filter by category
    if (selectedCategory !== 'all') {
      modelsToFilter = categorizedModels[selectedCategory] || [];
    }

    // Filter by featured
    if (showFeatured) {
      modelsToFilter = modelsToFilter.filter(model => 
        featuredModels.includes(model.id)
      );
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      modelsToFilter = modelsToFilter.filter(model =>
        model.name.toLowerCase().includes(query) ||
        model.id.toLowerCase().includes(query) ||
        (model.description && model.description.toLowerCase().includes(query))
      );
    }

    // Sort models
    return modelsToFilter.sort((a, b) => {
      switch (sortBy) {
        case 'price':
          const priceA = (a.inputCost || 0) + (a.outputCost || 0);
          const priceB = (b.inputCost || 0) + (b.outputCost || 0);
          return priceA - priceB;
        case 'context':
          return (b.contextLength || 0) - (a.contextLength || 0);
        case 'name':
        default:
          return a.name.localeCompare(b.name);
      }
    });
  }, [models, categorizedModels, selectedCategory, showFeatured, searchQuery, sortBy]);

  const selectedModel = models.find(m => m.id === selectedModelId);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setFocusedIndex(prev => 
            prev < filteredModels.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setFocusedIndex(prev => 
            prev > 0 ? prev - 1 : filteredModels.length - 1
          );
          break;
        case 'Enter':
          e.preventDefault();
          if (focusedIndex >= 0 && filteredModels[focusedIndex]) {
            handleModelSelect(filteredModels[focusedIndex].id);
          }
          break;
        case 'Escape':
          e.preventDefault();
          setIsOpen(false);
          setFocusedIndex(-1);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, focusedIndex, filteredModels]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setFocusedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleModelSelect = (modelId: string) => {
    onModelSelect(modelId);
    setIsOpen(false);
    setSearchQuery('');
    setFocusedIndex(-1);
  };

  const handleToggle = () => {
    if (!loading) {
      setIsOpen(!isOpen);
      if (!isOpen) {
        setTimeout(() => searchInputRef.current?.focus(), 100);
      }
    }
  };

  const formatPrice = (inputCost?: number, outputCost?: number) => {
    if (!inputCost && !outputCost) return 'Free';
    const total = (inputCost || 0) + (outputCost || 0);
    if (total < 0.001) return `$${(total * 1000).toFixed(3)}/1K`;
    return `$${total.toFixed(3)}/1K`;
  };

  if (loading) {
    return (
      <div className="openrouter-model-picker loading">
        <InfoIcon size={16} className="loading-icon" />
        <span>Loading OpenRouter models...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="openrouter-model-picker error">
        <InfoIcon size={16} className="error-icon" />
        <span>Error: {error}</span>
      </div>
    );
  }

  return (
    <div className="openrouter-model-picker" ref={dropdownRef}>
      <button
        className={`model-picker-trigger ${isOpen ? 'open' : ''}`}
        onClick={handleToggle}
        aria-expanded={isOpen}
      >
        <span className="selected-model-text">
          {selectedModel ? selectedModel.name : 'Select OpenRouter model'}
        </span>
        <ChevronDownIcon size={14} className="dropdown-icon" />
      </button>
      
      {isOpen && (
        <div className="model-picker-dropdown openrouter-dropdown">
          <div className="picker-header">
            <div className="search-container">
              <SearchIcon size={14} className="search-icon" />
              <input
                ref={searchInputRef}
                type="text"
                className="model-search-input"
                placeholder="Search OpenRouter models..."
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                  setFocusedIndex(-1);
                }}
              />
            </div>
            
            <div className="filter-controls">
              <div className="filter-row">
                <div className="category-filters">
                  {Object.keys(categorizedModels).map(category => (
                    <button
                      key={category}
                      className={`category-filter ${selectedCategory === category ? 'active' : ''}`}
                      onClick={() => {
                        setSelectedCategory(category);
                        setFocusedIndex(-1);
                      }}
                    >
                      {category === 'all' ? 'All' : category.charAt(0).toUpperCase() + category.slice(1)}
                      <span className="category-count">
                        ({category === 'all' ? models.length : categorizedModels[category]?.length || 0})
                      </span>
                    </button>
                  ))}
                </div>
                
                <div className="sort-controls">
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as 'name' | 'price' | 'context')}
                    className="sort-select"
                  >
                    <option value="name">Name</option>
                    <option value="price">Price</option>
                    <option value="context">Context</option>
                  </select>
                  
                  <button
                    className={`featured-toggle ${showFeatured ? 'active' : ''}`}
                    onClick={() => setShowFeatured(!showFeatured)}
                    title="Show featured models"
                  >
                    <StarIcon size={12} />
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <div className="models-list">
            {filteredModels.length > 0 ? (
              filteredModels.map((model, index) => (
                <button
                  key={model.id}
                  className={`model-item ${selectedModelId === model.id ? 'selected' : ''} ${
                    index === focusedIndex ? 'focused' : ''
                  } ${featuredModels.includes(model.id) ? 'featured' : ''}`}
                  onClick={() => handleModelSelect(model.id)}
                  onMouseEnter={() => setFocusedIndex(index)}
                >
                  <div className="model-info">
                    <div className="model-header">
                      <span className="model-name">{model.name}</span>
                      {featuredModels.includes(model.id) && (
                        <StarIcon size={12} className="featured-star" />
                      )}
                    </div>
                    <div className="model-meta">
                      <span className="model-price">
                        {formatPrice(model.inputCost, model.outputCost)}
                      </span>
                      {model.contextLength && (
                        <span className="model-context">
                          {model.contextLength.toLocaleString()} ctx
                        </span>
                      )}
                    </div>
                    {model.description && (
                      <span className="model-description">{model.description}</span>
                    )}
                  </div>
                </button>
              ))
            ) : (
              <div className="no-models">
                <InfoIcon size={16} />
                <span>{searchQuery ? 'No models found' : 'No models available'}</span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
