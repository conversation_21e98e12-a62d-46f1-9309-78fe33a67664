/**
 * Specialized Model Picker for Together AI
 * Enhanced with open-source model categorization and filtering
 */

import React, { useState, useMemo, useRef, useEffect } from 'react';
import { ModelInfo } from '../../../stores/settingsStore';
import { SearchIcon, InfoIcon, ChevronDownIcon } from '../../ui';

interface TogetherModelPickerProps {
  models: ModelInfo[];
  selectedModelId: string;
  onModelSelect: (modelId: string) => void;
  loading?: boolean;
  error?: string;
  isPopup?: boolean;
}

export const TogetherModelPicker: React.FC<TogetherModelPickerProps> = ({
  models,
  selectedModelId,
  onModelSelect,
  loading = false,
  error,
  isPopup = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Categorize models by type/family
  const categorizedModels = useMemo(() => {
    const categories: Record<string, ModelInfo[]> = {
      'llama': [],
      'mistral': [],
      'qwen': [],
      'code': [],
      'chat': [],
      'other': []
    };

    models.forEach(model => {
      const modelName = model.name.toLowerCase();
      const modelId = model.id.toLowerCase();
      
      if (modelName.includes('llama') || modelId.includes('llama')) {
        categories.llama.push(model);
      } else if (modelName.includes('mistral') || modelId.includes('mistral')) {
        categories.mistral.push(model);
      } else if (modelName.includes('qwen') || modelId.includes('qwen')) {
        categories.qwen.push(model);
      } else if (modelName.includes('code') || modelId.includes('code') || 
                 modelName.includes('starcoder') || modelId.includes('starcoder')) {
        categories.code.push(model);
      } else if (modelName.includes('chat') || modelId.includes('chat') ||
                 modelName.includes('instruct') || modelId.includes('instruct')) {
        categories.chat.push(model);
      } else {
        categories.other.push(model);
      }
    });

    return categories;
  }, [models]);

  // Filter models based on search and category
  const filteredModels = useMemo(() => {
    let modelsToFilter = models;
    
    if (selectedCategory !== 'all') {
      modelsToFilter = categorizedModels[selectedCategory] || [];
    }

    if (!searchQuery.trim()) {
      return modelsToFilter;
    }
    
    const query = searchQuery.toLowerCase();
    return modelsToFilter.filter(model =>
      model.name.toLowerCase().includes(query) ||
      model.id.toLowerCase().includes(query) ||
      (model.description && model.description.toLowerCase().includes(query))
    );
  }, [models, categorizedModels, selectedCategory, searchQuery]);

  const selectedModel = models.find(m => m.id === selectedModelId);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setFocusedIndex(prev => 
            prev < filteredModels.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setFocusedIndex(prev => 
            prev > 0 ? prev - 1 : filteredModels.length - 1
          );
          break;
        case 'Enter':
          e.preventDefault();
          if (focusedIndex >= 0 && filteredModels[focusedIndex]) {
            handleModelSelect(filteredModels[focusedIndex].id);
          }
          break;
        case 'Escape':
          e.preventDefault();
          setIsOpen(false);
          setFocusedIndex(-1);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, focusedIndex, filteredModels]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setFocusedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleModelSelect = (modelId: string) => {
    onModelSelect(modelId);
    setIsOpen(false);
    setSearchQuery('');
    setFocusedIndex(-1);
  };

  const handleToggle = () => {
    if (!loading) {
      setIsOpen(!isOpen);
      if (!isOpen) {
        setTimeout(() => searchInputRef.current?.focus(), 100);
      }
    }
  };

  const getModelType = (model: ModelInfo) => {
    const name = model.name.toLowerCase();
    if (name.includes('code')) return 'Code';
    if (name.includes('chat') || name.includes('instruct')) return 'Chat';
    if (name.includes('base')) return 'Base';
    return 'General';
  };

  if (loading) {
    return (
      <div className="together-model-picker loading">
        <InfoIcon size={16} className="loading-icon" />
        <span>Loading Together AI models...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="together-model-picker error">
        <InfoIcon size={16} className="error-icon" />
        <span>Error: {error}</span>
      </div>
    );
  }

  return (
    <div className="together-model-picker" ref={dropdownRef}>
      <button
        className={`model-picker-trigger ${isOpen ? 'open' : ''}`}
        onClick={handleToggle}
        aria-expanded={isOpen}
      >
        <span className="selected-model-text">
          {selectedModel ? selectedModel.name : 'Select Together AI model'}
        </span>
        <ChevronDownIcon size={14} className="dropdown-icon" />
      </button>
      
      {isOpen && (
        <div className="model-picker-dropdown together-dropdown">
          <div className="picker-header">
            <div className="search-container">
              <SearchIcon size={14} className="search-icon" />
              <input
                ref={searchInputRef}
                type="text"
                className="model-search-input"
                placeholder="Search Together AI models..."
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                  setFocusedIndex(-1);
                }}
              />
            </div>
            
            <div className="category-filters">
              {Object.keys(categorizedModels).map(category => (
                <button
                  key={category}
                  className={`category-filter ${selectedCategory === category ? 'active' : ''}`}
                  onClick={() => {
                    setSelectedCategory(category);
                    setFocusedIndex(-1);
                  }}
                >
                  {category === 'all' ? 'All' : category.charAt(0).toUpperCase() + category.slice(1)}
                  <span className="category-count">
                    ({category === 'all' ? models.length : categorizedModels[category]?.length || 0})
                  </span>
                </button>
              ))}
            </div>
          </div>
          
          <div className="models-list">
            {filteredModels.length > 0 ? (
              filteredModels.map((model, index) => (
                <button
                  key={model.id}
                  className={`model-item ${selectedModelId === model.id ? 'selected' : ''} ${
                    index === focusedIndex ? 'focused' : ''
                  }`}
                  onClick={() => handleModelSelect(model.id)}
                  onMouseEnter={() => setFocusedIndex(index)}
                >
                  <div className="model-info">
                    <span className="model-name">{model.name}</span>
                    <div className="model-meta">
                      <span className="model-type">{getModelType(model)}</span>
                      {model.contextLength && (
                        <span className="model-context">
                          {model.contextLength.toLocaleString()} ctx
                        </span>
                      )}
                      <span className="model-source">Open Source</span>
                    </div>
                    {model.description && (
                      <span className="model-description">{model.description}</span>
                    )}
                  </div>
                </button>
              ))
            ) : (
              <div className="no-models">
                <InfoIcon size={16} />
                <span>{searchQuery ? 'No models found' : 'No models available'}</span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
