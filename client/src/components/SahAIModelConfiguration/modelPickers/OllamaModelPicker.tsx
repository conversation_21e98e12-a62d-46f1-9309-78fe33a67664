/**
 * Specialized Model Picker for Ollama
 * Enhanced with local model management and pull functionality
 */

import React, { useState, useMemo, useRef, useEffect } from 'react';
import { ModelInfo } from '../../../stores/settingsStore';
import { SearchIcon, InfoIcon, ChevronDownIcon, PlusIcon } from '../../ui';

interface OllamaModelPickerProps {
  models: ModelInfo[];
  selectedModelId: string;
  onModelSelect: (modelId: string) => void;
  loading?: boolean;
  error?: string;
  baseUrl: string;
  isPopup?: boolean;
}

export const OllamaModelPicker: React.FC<OllamaModelPickerProps> = ({
  models,
  selectedModelId,
  onModelSelect,
  loading = false,
  error,
  baseUrl,
  isPopup = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const [showPullDialog, setShowPullDialog] = useState(false);
  const [pullModelName, setPullModelName] = useState('');
  const [isPulling, setIsPulling] = useState(false);
  const [pullProgress, setPullProgress] = useState('');
  
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Popular Ollama models for quick access
  const popularModels = [
    'llama2',
    'llama2:13b',
    'llama2:70b',
    'codellama',
    'codellama:13b',
    'mistral',
    'mixtral',
    'neural-chat',
    'starling-lm',
    'vicuna',
    'orca-mini'
  ];

  // Filter models based on search query
  const filteredModels = useMemo(() => {
    if (!searchQuery.trim()) {
      return models;
    }
    
    const query = searchQuery.toLowerCase();
    return models.filter(model =>
      model.name.toLowerCase().includes(query) ||
      model.id.toLowerCase().includes(query) ||
      (model.description && model.description.toLowerCase().includes(query))
    );
  }, [models, searchQuery]);

  const selectedModel = models.find(m => m.id === selectedModelId);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setFocusedIndex(prev => 
            prev < filteredModels.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setFocusedIndex(prev => 
            prev > 0 ? prev - 1 : filteredModels.length - 1
          );
          break;
        case 'Enter':
          e.preventDefault();
          if (focusedIndex >= 0 && filteredModels[focusedIndex]) {
            handleModelSelect(filteredModels[focusedIndex].id);
          }
          break;
        case 'Escape':
          e.preventDefault();
          setIsOpen(false);
          setFocusedIndex(-1);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, focusedIndex, filteredModels]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setFocusedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleModelSelect = (modelId: string) => {
    onModelSelect(modelId);
    setIsOpen(false);
    setSearchQuery('');
    setFocusedIndex(-1);
  };

  const handleToggle = () => {
    if (!loading) {
      setIsOpen(!isOpen);
      if (!isOpen) {
        setTimeout(() => searchInputRef.current?.focus(), 100);
      }
    }
  };

  const handlePullModel = async () => {
    if (!pullModelName.trim()) return;

    setIsPulling(true);
    setPullProgress('Starting download...');

    try {
      const response = await fetch(`${baseUrl}/api/pull`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: pullModelName.trim() }),
      });

      if (!response.ok) {
        throw new Error(`Failed to pull model: ${response.statusText}`);
      }

      // Handle streaming response for progress updates
      const reader = response.body?.getReader();
      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const text = new TextDecoder().decode(value);
          const lines = text.split('\n').filter(line => line.trim());
          
          for (const line of lines) {
            try {
              const data = JSON.parse(line);
              if (data.status) {
                setPullProgress(data.status);
              }
            } catch (e) {
              // Ignore JSON parse errors
            }
          }
        }
      }

      setPullProgress('Model downloaded successfully!');
      setTimeout(() => {
        setShowPullDialog(false);
        setPullModelName('');
        setPullProgress('');
        // Refresh models list here if needed
      }, 2000);
    } catch (err) {
      setPullProgress(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setIsPulling(false);
    }
  };

  const formatModelSize = (model: ModelInfo) => {
    // Estimate size based on model name
    if (model.id.includes('70b')) return '~40GB';
    if (model.id.includes('13b')) return '~7GB';
    if (model.id.includes('7b')) return '~4GB';
    if (model.id.includes('3b')) return '~2GB';
    return 'Unknown';
  };

  if (loading) {
    return (
      <div className="ollama-model-picker loading">
        <InfoIcon size={16} className="loading-icon" />
        <span>Loading local models...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="ollama-model-picker error">
        <InfoIcon size={16} className="error-icon" />
        <span>Error: {error}</span>
      </div>
    );
  }

  return (
    <div className="ollama-model-picker" ref={dropdownRef}>
      <div className="picker-controls">
        <button
          className={`model-picker-trigger ${isOpen ? 'open' : ''}`}
          onClick={handleToggle}
          aria-expanded={isOpen}
        >
          <span className="selected-model-text">
            {selectedModel ? selectedModel.name : 'Select local model'}
          </span>
          <ChevronDownIcon size={14} className="dropdown-icon" />
        </button>
        
        <button
          className="pull-model-button"
          onClick={() => setShowPullDialog(true)}
          title="Pull new model"
        >
          <PlusIcon size={14} />
        </button>
      </div>
      
      {isOpen && (
        <div className="model-picker-dropdown">
          <div className="picker-header">
            <div className="search-container">
              <SearchIcon size={14} className="search-icon" />
              <input
                ref={searchInputRef}
                type="text"
                className="model-search-input"
                placeholder="Search local models..."
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                  setFocusedIndex(-1);
                }}
              />
            </div>
          </div>
          
          <div className="models-list">
            {filteredModels.length > 0 ? (
              filteredModels.map((model, index) => (
                <button
                  key={model.id}
                  className={`model-item ${selectedModelId === model.id ? 'selected' : ''} ${
                    index === focusedIndex ? 'focused' : ''
                  }`}
                  onClick={() => handleModelSelect(model.id)}
                  onMouseEnter={() => setFocusedIndex(index)}
                >
                  <div className="model-info">
                    <span className="model-name">{model.name}</span>
                    <div className="model-meta">
                      <span className="model-size">{formatModelSize(model)}</span>
                      <span className="model-local">Local</span>
                    </div>
                    {model.description && (
                      <span className="model-description">{model.description}</span>
                    )}
                  </div>
                </button>
              ))
            ) : (
              <div className="no-models">
                <InfoIcon size={16} />
                <span>{searchQuery ? 'No models found' : 'No local models available'}</span>
                <button
                  className="pull-first-model"
                  onClick={() => setShowPullDialog(true)}
                >
                  Pull a model
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {showPullDialog && (
        <div className="pull-model-dialog">
          <div className="dialog-content">
            <h4>Pull Ollama Model</h4>
            
            <div className="pull-input-section">
              <label>Model Name</label>
              <input
                type="text"
                value={pullModelName}
                onChange={(e) => setPullModelName(e.target.value)}
                placeholder="e.g., llama2, mistral, codellama"
                disabled={isPulling}
              />
            </div>
            
            <div className="popular-models">
              <label>Popular Models</label>
              <div className="popular-models-grid">
                {popularModels.map(modelName => (
                  <button
                    key={modelName}
                    className="popular-model-button"
                    onClick={() => setPullModelName(modelName)}
                    disabled={isPulling}
                  >
                    {modelName}
                  </button>
                ))}
              </div>
            </div>
            
            {pullProgress && (
              <div className="pull-progress">
                {pullProgress}
              </div>
            )}
            
            <div className="dialog-actions">
              <button
                onClick={() => setShowPullDialog(false)}
                disabled={isPulling}
              >
                Cancel
              </button>
              <button
                onClick={handlePullModel}
                disabled={isPulling || !pullModelName.trim()}
                className="primary"
              >
                {isPulling ? 'Pulling...' : 'Pull Model'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
