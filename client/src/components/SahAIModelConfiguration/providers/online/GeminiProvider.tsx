/**
 * Google Gemini Provider Configuration Component
 * Adapted from Cline for SahAI V2 architecture
 */

import React, { useState, useEffect } from 'react';
import { useSettingsStore } from '../../../../stores/settingsStore';
import { A<PERSON><PERSON>ey<PERSON>ield, ModelSelector, ModelInfoView } from '../../common';
import { getProviderMetadata } from '../../utils/providerUtils';

interface GeminiProviderProps {
  showModelOptions?: boolean;
  isPopup?: boolean;
}

export const GeminiProvider: React.FC<GeminiProviderProps> = ({
  showModelOptions = true,
  isPopup = false
}) => {
  const {
    providers,
    currentProvider,
    availableModels,
    currentModel,
    modelsLoading,
    modelsError,
    setCurrentProvider,
    setCurrentModel,
    loadModelsForProvider,
    updateProviderApiKey
  } = useSettingsStore();

  const [apiKey, setApiKey] = useState('');
  const [baseUrl, setBaseUrl] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const provider = providers.find(p => p.id === 'gemini');
  const metadata = getProviderMetadata('gemini');
  const selectedModel = currentModel && currentProvider?.id === 'gemini' ? currentModel : null;

  // Initialize values from provider if available
  useEffect(() => {
    if (provider?.apiKey) {
      setApiKey(provider.apiKey);
    }
    if (provider?.baseURL) {
      setBaseUrl(provider.baseURL);
    }
  }, [provider]);

  // Load models when provider becomes configured
  useEffect(() => {
    if (provider?.isConfigured && currentProvider?.id === 'gemini') {
      loadModelsForProvider('gemini');
    }
  }, [provider?.isConfigured, currentProvider?.id, loadModelsForProvider]);

  const handleApiKeySubmit = async () => {
    if (!apiKey.trim()) {
      setError('API key is required');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      await updateProviderApiKey('gemini', apiKey.trim());
      await setCurrentProvider('gemini');
      await loadModelsForProvider('gemini');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save API key');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleModelSelect = (modelId: string) => {
    setCurrentModel(modelId);
  };

  const isConfigured = provider?.isConfigured ?? false;

  return (
    <div className="provider-config gemini-provider">
      <div className="provider-header">
        <h3 className="provider-title">{metadata?.name || 'Google Gemini'}</h3>
        <p className="provider-description">
          {metadata?.description || 'Google\'s Gemini models for multimodal AI'}
        </p>
      </div>

      <div className="config-section">
        <ApiKeyField
          label="Gemini API Key"
          placeholder="AIza..."
          value={apiKey}
          onChange={setApiKey}
          onSubmit={handleApiKeySubmit}
          helpText="Get your API key from"
          helpLink={metadata?.website}
          helpLinkText="Google AI Studio"
          disabled={isSubmitting}
          error={error || undefined}
          submitDisabled={isSubmitting || !apiKey.trim()}
          submitButtonText={isSubmitting ? 'Saving...' : 'Save'}
        />
      </div>

      <div className="config-section">
        <label className="config-label">
          Base URL (Optional)
        </label>
        <div className="base-url-container">
          <input
            type="text"
            className="base-url-input"
            placeholder="https://generativelanguage.googleapis.com (default)"
            value={baseUrl}
            onChange={(e) => setBaseUrl(e.target.value)}
            disabled={isSubmitting}
          />
          <p className="base-url-help">
            Use a custom base URL for proxy or alternative endpoints
          </p>
        </div>
      </div>

      {showModelOptions && isConfigured && (
        <>
          <div className="config-section">
            <label className="config-label">Model</label>
            <ModelSelector
              models={availableModels}
              selectedModelId={selectedModel?.id || ''}
              onModelSelect={handleModelSelect}
              placeholder="Select Gemini model"
              loading={modelsLoading}
              error={modelsError || undefined}
              showSearch={availableModels.length > 5}
              showModelInfo={true}
              emptyMessage="No Gemini models available"
            />
          </div>

          {selectedModel && (
            <div className="config-section">
              <ModelInfoView
                model={selectedModel}
                showPricing={true}
                showCapabilities={true}
                showDescription={true}
                compact={isPopup}
              />
            </div>
          )}
        </>
      )}

      {!isConfigured && (
        <div className="provider-status">
          <p className="status-message">
            Configure your Google AI Studio API key to access Gemini models
          </p>
        </div>
      )}
    </div>
  );
};

// Provider-specific styles
export const geminiProviderStyles = `
.gemini-provider {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.base-url-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.base-url-input {
  width: 100%;
  padding: 8px 12px;
  background: var(--adobe-bg-primary);
  border: 1px solid var(--adobe-border);
  border-radius: 3px;
  color: var(--adobe-text-primary);
  font-size: 12px;
  outline: none;
  transition: border-color 0.2s ease;
}

.base-url-input:focus {
  border-color: var(--adobe-accent);
  box-shadow: 0 0 0 1px var(--adobe-accent);
}

.base-url-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.base-url-help {
  font-size: 11px;
  color: var(--adobe-text-secondary);
  margin: 0;
  line-height: 1.4;
}
`;
