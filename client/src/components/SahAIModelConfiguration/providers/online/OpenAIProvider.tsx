/**
 * OpenAI Provider Configuration Component
 * Adapted from Cline for SahAI V2 architecture
 */

import React, { useState, useEffect } from 'react';
import { useSettingsStore } from '../../../../stores/settingsStore';
import { A<PERSON><PERSON>ey<PERSON>ield, ModelSelector, ModelInfoView } from '../../common';
import { getProviderMetadata } from '../../utils/providerUtils';

interface OpenAIProviderProps {
  showModelOptions?: boolean;
  isPopup?: boolean;
}

export const OpenAIProvider: React.FC<OpenAIProviderProps> = ({
  showModelOptions = true,
  isPopup = false
}) => {
  const {
    providers,
    currentProvider,
    availableModels,
    currentModel,
    modelsLoading,
    modelsError,
    setCurrentProvider,
    setCurrentModel,
    loadModelsForProvider,
    updateProviderApiKey
  } = useSettingsStore();

  const [apiKey, setApiKey] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const provider = providers.find(p => p.id === 'openai');
  const metadata = getProviderMetadata('openai');
  const selectedModel = currentModel && currentProvider?.id === 'openai' ? currentModel : null;

  // Initialize API key from provider if available
  useEffect(() => {
    if (provider?.apiKey) {
      setApiKey(provider.apiKey);
    }
  }, [provider]);

  // Load models when provider becomes configured
  useEffect(() => {
    if (provider?.isConfigured && currentProvider?.id === 'openai') {
      loadModelsForProvider('openai');
    }
  }, [provider?.isConfigured, currentProvider?.id, loadModelsForProvider]);

  const handleApiKeySubmit = async () => {
    if (!apiKey.trim()) {
      setError('API key is required');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      await updateProviderApiKey('openai', apiKey.trim());
      await setCurrentProvider('openai');
      await loadModelsForProvider('openai');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save API key');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleModelSelect = (modelId: string) => {
    setCurrentModel(modelId);
  };

  const isConfigured = provider?.isConfigured ?? false;

  return (
    <div className="provider-config openai-provider">
      <div className="provider-header">
        <h3 className="provider-title">{metadata?.name || 'OpenAI'}</h3>
        <p className="provider-description">
          {metadata?.description || 'OpenAI\'s GPT models including GPT-4 and GPT-3.5'}
        </p>
      </div>

      <div className="config-section">
        <ApiKeyField
          label="OpenAI API Key"
          placeholder="sk-..."
          value={apiKey}
          onChange={setApiKey}
          onSubmit={handleApiKeySubmit}
          helpText="Get your API key from"
          helpLink={metadata?.website}
          helpLinkText="OpenAI Platform"
          disabled={isSubmitting}
          error={error || undefined}
          submitDisabled={isSubmitting || !apiKey.trim()}
          submitButtonText={isSubmitting ? 'Saving...' : 'Save'}
        />
      </div>

      {showModelOptions && isConfigured && (
        <>
          <div className="config-section">
            <label className="config-label">Model</label>
            <ModelSelector
              models={availableModels}
              selectedModelId={selectedModel?.id || ''}
              onModelSelect={handleModelSelect}
              placeholder="Select OpenAI model"
              loading={modelsLoading}
              error={modelsError || undefined}
              showSearch={availableModels.length > 5}
              showModelInfo={true}
              emptyMessage="No OpenAI models available"
            />
          </div>

          {selectedModel && (
            <div className="config-section">
              <ModelInfoView
                model={selectedModel}
                showPricing={true}
                showCapabilities={true}
                showDescription={true}
                compact={isPopup}
              />
            </div>
          )}
        </>
      )}

      {!isConfigured && (
        <div className="provider-status">
          <p className="status-message">
            Configure your OpenAI API key to access models
          </p>
        </div>
      )}
    </div>
  );
};

// Provider-specific styles
export const openAIProviderStyles = `
.openai-provider {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.provider-header {
  border-bottom: 1px solid var(--adobe-border);
  padding-bottom: 12px;
}

.provider-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--adobe-text-primary);
  margin: 0 0 4px 0;
}

.provider-description {
  font-size: 12px;
  color: var(--adobe-text-secondary);
  margin: 0;
  line-height: 1.4;
}

.config-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-label {
  font-size: 12px;
  font-weight: 600;
  color: var(--adobe-text-primary);
  margin-bottom: 4px;
}

.provider-status {
  padding: 12px;
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 3px;
}

.status-message {
  font-size: 12px;
  color: var(--adobe-text-secondary);
  margin: 0;
  text-align: center;
}
`;
