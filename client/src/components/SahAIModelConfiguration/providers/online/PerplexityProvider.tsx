/**
 * Perplexity Provider Configuration Component
 * Adapted from Cline for SahAI V2 architecture
 */

import React, { useState, useEffect } from 'react';
import { useSettingsStore } from '../../../../stores/settingsStore';
import { A<PERSON><PERSON><PERSON><PERSON><PERSON>, ModelSelector, ModelInfoView } from '../../common';
import { getProviderMetadata } from '../../utils/providerUtils';

interface PerplexityProviderProps {
  showModelOptions?: boolean;
  isPopup?: boolean;
}

export const PerplexityProvider: React.FC<PerplexityProviderProps> = ({
  showModelOptions = true,
  isPopup = false
}) => {
  const {
    providers,
    currentProvider,
    availableModels,
    currentModel,
    modelsLoading,
    modelsError,
    setCurrentProvider,
    setCurrentModel,
    loadModelsForProvider,
    updateProviderApiKey
  } = useSettingsStore();

  const [apiKey, setApiKey] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const provider = providers.find(p => p.id === 'perplexity');
  const metadata = getProviderMetadata('perplexity');
  const selectedModel = currentModel && currentProvider?.id === 'perplexity' ? currentModel : null;

  // Initialize API key from provider if available
  useEffect(() => {
    if (provider?.apiKey) {
      setApiKey(provider.apiKey);
    }
  }, [provider]);

  // Load models when provider becomes configured
  useEffect(() => {
    if (provider?.isConfigured && currentProvider?.id === 'perplexity') {
      loadModelsForProvider('perplexity');
    }
  }, [provider?.isConfigured, currentProvider?.id, loadModelsForProvider]);

  const handleApiKeySubmit = async () => {
    if (!apiKey.trim()) {
      setError('API key is required');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      await updateProviderApiKey('perplexity', apiKey.trim());
      await setCurrentProvider('perplexity');
      await loadModelsForProvider('perplexity');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save API key');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleModelSelect = (modelId: string) => {
    setCurrentModel(modelId);
  };

  const isConfigured = provider?.isConfigured ?? false;

  return (
    <div className="provider-config perplexity-provider">
      <div className="provider-header">
        <h3 className="provider-title">{metadata?.name || 'Perplexity'}</h3>
        <p className="provider-description">
          {metadata?.description || 'Perplexity\'s search-augmented language models'}
        </p>
      </div>

      <div className="config-section">
        <ApiKeyField
          label="Perplexity API Key"
          placeholder="pplx-..."
          value={apiKey}
          onChange={setApiKey}
          onSubmit={handleApiKeySubmit}
          helpText="Get your API key from"
          helpLink={metadata?.website}
          helpLinkText="Perplexity Settings"
          disabled={isSubmitting}
          error={error || undefined}
          submitDisabled={isSubmitting || !apiKey.trim()}
          submitButtonText={isSubmitting ? 'Saving...' : 'Save'}
        />
        
        <div className="perplexity-note">
          <p className="note-text">
            <strong>Search-Augmented Models:</strong> Perplexity models have access to real-time web search 
            and can provide up-to-date information with citations.
          </p>
        </div>
      </div>

      {showModelOptions && isConfigured && (
        <>
          <div className="config-section">
            <label className="config-label">Model</label>
            <ModelSelector
              models={availableModels}
              selectedModelId={selectedModel?.id || ''}
              onModelSelect={handleModelSelect}
              placeholder="Select Perplexity model"
              loading={modelsLoading}
              error={modelsError || undefined}
              showSearch={availableModels.length > 5}
              showModelInfo={true}
              emptyMessage="No Perplexity models available"
            />
          </div>

          {selectedModel && (
            <div className="config-section">
              <ModelInfoView
                model={selectedModel}
                showPricing={true}
                showCapabilities={true}
                showDescription={true}
                compact={isPopup}
              />
            </div>
          )}
        </>
      )}

      {!isConfigured && (
        <div className="provider-status">
          <p className="status-message">
            Configure your Perplexity API key to access search-augmented models
          </p>
        </div>
      )}
    </div>
  );
};

// Provider-specific styles
export const perplexityProviderStyles = `
.perplexity-provider {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.perplexity-note {
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 3px;
}

.note-text {
  font-size: 11px;
  color: var(--adobe-text-secondary);
  margin: 0;
  line-height: 1.4;
}

.note-text strong {
  color: var(--adobe-text-primary);
}
`;
