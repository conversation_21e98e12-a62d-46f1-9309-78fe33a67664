/**
 * Together AI Provider Configuration Component
 * Adapted from Cline for SahAI V2 architecture with specialized model picker
 */

import React, { useState, useEffect } from 'react';
import { useSettingsStore } from '../../../../stores/settingsStore';
import { <PERSON><PERSON><PERSON>eyField, ModelInfoView } from '../../common';
import { getProviderMetadata } from '../../utils/providerUtils';
import { TogetherModelPicker } from '../../modelPickers/TogetherModelPicker';

interface TogetherProviderProps {
  showModelOptions?: boolean;
  isPopup?: boolean;
}

export const TogetherProvider: React.FC<TogetherProviderProps> = ({
  showModelOptions = true,
  isPopup = false
}) => {
  const {
    providers,
    currentProvider,
    availableModels,
    currentModel,
    modelsLoading,
    modelsError,
    setCurrentProvider,
    setCurrentModel,
    loadModelsForProvider,
    updateProviderApiKey
  } = useSettingsStore();

  const [apiKey, setApiKey] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const provider = providers.find(p => p.id === 'together');
  const metadata = getProviderMetadata('together');
  const selectedModel = currentModel && currentProvider?.id === 'together' ? currentModel : null;

  // Initialize API key from provider if available
  useEffect(() => {
    if (provider?.apiKey) {
      setApiKey(provider.apiKey);
    }
  }, [provider]);

  // Load models when provider becomes configured
  useEffect(() => {
    if (provider?.isConfigured && currentProvider?.id === 'together') {
      loadModelsForProvider('together');
    }
  }, [provider?.isConfigured, currentProvider?.id, loadModelsForProvider]);

  const handleApiKeySubmit = async () => {
    if (!apiKey.trim()) {
      setError('API key is required');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      await updateProviderApiKey('together', apiKey.trim());
      await setCurrentProvider('together');
      await loadModelsForProvider('together');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save API key');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleModelSelect = (modelId: string) => {
    setCurrentModel(modelId);
  };

  const isConfigured = provider?.isConfigured ?? false;

  return (
    <div className="provider-config together-provider">
      <div className="provider-header">
        <h3 className="provider-title">{metadata?.name || 'Together AI'}</h3>
        <p className="provider-description">
          {metadata?.description || 'Together AI\'s platform for open-source models'}
        </p>
      </div>

      <div className="config-section">
        <ApiKeyField
          label="Together AI API Key"
          placeholder="..."
          value={apiKey}
          onChange={setApiKey}
          onSubmit={handleApiKeySubmit}
          helpText="Get your API key from"
          helpLink={metadata?.website}
          helpLinkText="Together AI"
          disabled={isSubmitting}
          error={error || undefined}
          submitDisabled={isSubmitting || !apiKey.trim()}
          submitButtonText={isSubmitting ? 'Saving...' : 'Save'}
        />
        
        <div className="together-note">
          <p className="note-text">
            <strong>Open Source Models:</strong> Together AI specializes in hosting open-source 
            language models with fast inference and competitive pricing.
          </p>
        </div>
      </div>

      {showModelOptions && isConfigured && (
        <>
          <div className="config-section">
            <label className="config-label">Model</label>
            <TogetherModelPicker
              models={availableModels}
              selectedModelId={selectedModel?.id || ''}
              onModelSelect={handleModelSelect}
              loading={modelsLoading}
              error={modelsError || undefined}
              isPopup={isPopup}
            />
          </div>

          {selectedModel && (
            <div className="config-section">
              <ModelInfoView
                model={selectedModel}
                showPricing={true}
                showCapabilities={true}
                showDescription={true}
                compact={isPopup}
              />
            </div>
          )}
        </>
      )}

      {!isConfigured && (
        <div className="provider-status">
          <p className="status-message">
            Configure your Together AI API key to access open-source models
          </p>
        </div>
      )}
    </div>
  );
};

// Provider-specific styles
export const togetherProviderStyles = `
.together-provider {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.together-note {
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(139, 69, 19, 0.1);
  border: 1px solid rgba(139, 69, 19, 0.3);
  border-radius: 3px;
}

.note-text {
  font-size: 11px;
  color: var(--adobe-text-secondary);
  margin: 0;
  line-height: 1.4;
}

.note-text strong {
  color: var(--adobe-text-primary);
}
`;
