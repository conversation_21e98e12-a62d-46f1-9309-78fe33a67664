/**
 * Qwen Provider Configuration Component
 * Adapted from Cline for SahAI V2 architecture with API line selection
 */

import React, { useState, useEffect } from 'react';
import { useSettingsStore } from '../../../../stores/settingsStore';
import { A<PERSON><PERSON><PERSON><PERSON>ield, ModelSelector, ModelInfoView } from '../../common';
import { getProviderMetadata } from '../../utils/providerUtils';

interface QwenProviderProps {
  showModelOptions?: boolean;
  isPopup?: boolean;
}

// Models that support thinking/reasoning features
const SUPPORTED_THINKING_MODELS = [
  'qwen3-235b-a22b',
  'qwen3-32b',
  'qwen3-30b-a3b',
  'qwen3-14b',
  'qwen3-8b',
  'qwen3-4b',
  'qwen3-1.7b',
  'qwen3-0.6b',
  'qwen-plus-latest',
  'qwen-turbo-latest',
];

export const QwenProvider: React.FC<QwenProviderProps> = ({
  showModelOptions = true,
  isPopup = false
}) => {
  const {
    providers,
    currentProvider,
    availableModels,
    currentModel,
    modelsLoading,
    modelsError,
    setCurrentProvider,
    setCurrentModel,
    loadModelsForProvider,
    updateProviderApiKey
  } = useSettingsStore();

  const [apiKey, setApiKey] = useState('');
  const [apiLine, setApiLine] = useState<'china' | 'international'>('china');
  const [thinkingBudget, setThinkingBudget] = useState(10);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const provider = providers.find(p => p.id === 'qwen');
  const metadata = getProviderMetadata('qwen');
  const selectedModel = currentModel && currentProvider?.id === 'qwen' ? currentModel : null;

  // Initialize values from provider if available
  useEffect(() => {
    if (provider?.apiKey) {
      setApiKey(provider.apiKey);
    }
    // Initialize API line from provider settings if available
    if (provider?.settings?.apiLine) {
      setApiLine(provider.settings.apiLine as 'china' | 'international');
    }
  }, [provider]);

  // Load models when provider becomes configured
  useEffect(() => {
    if (provider?.isConfigured && currentProvider?.id === 'qwen') {
      loadModelsForProvider('qwen');
    }
  }, [provider?.isConfigured, currentProvider?.id, loadModelsForProvider]);

  const handleApiKeySubmit = async () => {
    if (!apiKey.trim()) {
      setError('API key is required');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      await updateProviderApiKey('qwen', apiKey.trim());
      await setCurrentProvider('qwen');
      await loadModelsForProvider('qwen');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save API key');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleModelSelect = (modelId: string) => {
    setCurrentModel(modelId);
  };

  const isConfigured = provider?.isConfigured ?? false;
  const supportsThinking = selectedModel && SUPPORTED_THINKING_MODELS.includes(selectedModel.id);

  return (
    <div className="provider-config qwen-provider">
      <div className="provider-header">
        <h3 className="provider-title">{metadata?.name || 'Qwen'}</h3>
        <p className="provider-description">
          {metadata?.description || 'Alibaba\'s Qwen series models'}
        </p>
      </div>

      <div className="config-section">
        <label className="config-label">Alibaba API Line</label>
        <select
          value={apiLine}
          onChange={(e) => setApiLine(e.target.value as 'china' | 'international')}
          className="api-line-select"
          disabled={isSubmitting}
        >
          <option value="china">China API</option>
          <option value="international">International API</option>
        </select>
        <p className="api-line-help">
          Please select the appropriate API interface based on your location. 
          If you are in China, choose the China API interface. Otherwise, choose the International API interface.
        </p>
      </div>

      <div className="config-section">
        <ApiKeyField
          label="Qwen API Key"
          placeholder="sk-..."
          value={apiKey}
          onChange={setApiKey}
          onSubmit={handleApiKeySubmit}
          helpText="Get your API key from"
          helpLink={metadata?.website}
          helpLinkText="Alibaba Cloud Console"
          disabled={isSubmitting}
          error={error || undefined}
          submitDisabled={isSubmitting || !apiKey.trim()}
          submitButtonText={isSubmitting ? 'Saving...' : 'Save'}
        />
      </div>

      {showModelOptions && isConfigured && (
        <>
          <div className="config-section">
            <label className="config-label">Model</label>
            <ModelSelector
              models={availableModels}
              selectedModelId={selectedModel?.id || ''}
              onModelSelect={handleModelSelect}
              placeholder="Select Qwen model"
              loading={modelsLoading}
              error={modelsError || undefined}
              showSearch={availableModels.length > 5}
              showModelInfo={true}
              emptyMessage="No Qwen models available"
            />
          </div>

          {supportsThinking && (
            <div className="config-section">
              <label className="config-label">Thinking Budget</label>
              <div className="thinking-budget-container">
                <input
                  type="range"
                  min="1"
                  max="100"
                  value={thinkingBudget}
                  onChange={(e) => setThinkingBudget(Number(e.target.value))}
                  className="thinking-budget-slider"
                />
                <div className="thinking-budget-display">
                  <span className="budget-value">{thinkingBudget}</span>
                  <span className="budget-unit">tokens</span>
                </div>
              </div>
              <p className="thinking-budget-help">
                Controls the computational budget for reasoning. Higher values allow more thorough thinking but use more tokens.
              </p>
            </div>
          )}

          {selectedModel && (
            <div className="config-section">
              <ModelInfoView
                model={selectedModel}
                showPricing={true}
                showCapabilities={true}
                showDescription={true}
                compact={isPopup}
              />
            </div>
          )}
        </>
      )}

      {!isConfigured && (
        <div className="provider-status">
          <p className="status-message">
            Configure your Qwen API key to access Alibaba's language models
          </p>
        </div>
      )}
    </div>
  );
};

// Provider-specific styles
export const qwenProviderStyles = `
.qwen-provider {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.api-line-select {
  width: 100%;
  padding: 8px 12px;
  background: var(--adobe-bg-primary);
  border: 1px solid var(--adobe-border);
  border-radius: 3px;
  color: var(--adobe-text-primary);
  font-size: 12px;
  outline: none;
  transition: border-color 0.2s ease;
}

.api-line-select:focus {
  border-color: var(--adobe-accent);
  box-shadow: 0 0 0 1px var(--adobe-accent);
}

.api-line-help {
  font-size: 11px;
  color: var(--adobe-text-secondary);
  margin: 4px 0 0 0;
  line-height: 1.4;
}

.thinking-budget-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.thinking-budget-slider {
  flex: 1;
  height: 4px;
  background: var(--adobe-bg-tertiary);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
}

.thinking-budget-slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  background: var(--adobe-accent);
  border-radius: 50%;
  cursor: pointer;
}

.thinking-budget-display {
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 60px;
}

.budget-value {
  font-weight: 600;
  color: var(--adobe-text-primary);
  font-size: 12px;
}

.budget-unit {
  font-size: 10px;
  color: var(--adobe-text-secondary);
}

.thinking-budget-help {
  font-size: 11px;
  color: var(--adobe-text-secondary);
  margin: 4px 0 0 0;
  line-height: 1.4;
}
`;
