/**
 * xAI (Grok) Provider Configuration Component
 * Adapted from Cline for SahAI V2 architecture
 */

import React, { useState, useEffect } from 'react';
import { useSettingsStore } from '../../../../stores/settingsStore';
import { <PERSON><PERSON><PERSON>ey<PERSON>ield, ModelSelector, ModelInfoView } from '../../common';
import { getProviderMetadata } from '../../utils/providerUtils';

interface XAIProviderProps {
  showModelOptions?: boolean;
  isPopup?: boolean;
}

export const XAIProvider: React.FC<XAIProviderProps> = ({
  showModelOptions = true,
  isPopup = false
}) => {
  const {
    providers,
    currentProvider,
    availableModels,
    currentModel,
    modelsLoading,
    modelsError,
    setCurrentProvider,
    setCurrentModel,
    loadModelsForProvider,
    updateProviderApiKey
  } = useSettingsStore();

  const [apiKey, setApiKey] = useState('');
  const [reasoningEffort, setReasoningEffort] = useState<'low' | 'high'>('high');
  const [enableReasoningEffort, setEnableReasoningEffort] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const provider = providers.find(p => p.id === 'xai');
  const metadata = getProviderMetadata('xai');
  const selectedModel = currentModel && currentProvider?.id === 'xai' ? currentModel : null;

  // Initialize API key from provider if available
  useEffect(() => {
    if (provider?.apiKey) {
      setApiKey(provider.apiKey);
    }
  }, [provider]);

  // Load models when provider becomes configured
  useEffect(() => {
    if (provider?.isConfigured && currentProvider?.id === 'xai') {
      loadModelsForProvider('xai');
    }
  }, [provider?.isConfigured, currentProvider?.id, loadModelsForProvider]);

  const handleApiKeySubmit = async () => {
    if (!apiKey.trim()) {
      setError('API key is required');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      await updateProviderApiKey('xai', apiKey.trim());
      await setCurrentProvider('xai');
      await loadModelsForProvider('xai');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save API key');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleModelSelect = (modelId: string) => {
    setCurrentModel(modelId);
    // Reset reasoning effort for non-mini models
    if (!modelId.includes('mini')) {
      setEnableReasoningEffort(false);
    }
  };

  const isConfigured = provider?.isConfigured ?? false;
  const showReasoningEffort = selectedModel?.id?.includes('mini') ?? false;

  return (
    <div className="provider-config xai-provider">
      <div className="provider-header">
        <h3 className="provider-title">{metadata?.name || 'xAI (Grok)'}</h3>
        <p className="provider-description">
          {metadata?.description || 'xAI\'s Grok models with real-time information'}
        </p>
      </div>

      <div className="config-section">
        <ApiKeyField
          label="xAI API Key"
          placeholder="xai-..."
          value={apiKey}
          onChange={setApiKey}
          onSubmit={handleApiKeySubmit}
          helpText="Get your API key from"
          helpLink={metadata?.website}
          helpLinkText="xAI Console"
          disabled={isSubmitting}
          error={error || undefined}
          submitDisabled={isSubmitting || !apiKey.trim()}
          submitButtonText={isSubmitting ? 'Saving...' : 'Save'}
        />
        
        <div className="provider-note">
          <p className="note-text">
            <strong>Note:</strong> SahAI uses complex prompts and works best with Claude models. 
            Less capable models may not work as expected.
          </p>
        </div>
      </div>

      {showModelOptions && isConfigured && (
        <>
          <div className="config-section">
            <label className="config-label">Model</label>
            <ModelSelector
              models={availableModels}
              selectedModelId={selectedModel?.id || ''}
              onModelSelect={handleModelSelect}
              placeholder="Select Grok model"
              loading={modelsLoading}
              error={modelsError || undefined}
              showSearch={availableModels.length > 5}
              showModelInfo={true}
              emptyMessage="No Grok models available"
            />
          </div>

          {showReasoningEffort && (
            <div className="config-section">
              <div className="reasoning-effort-section">
                <label className="checkbox-container">
                  <input
                    type="checkbox"
                    checked={enableReasoningEffort}
                    onChange={(e) => setEnableReasoningEffort(e.target.checked)}
                    className="reasoning-checkbox"
                  />
                  <span className="checkbox-label">Modify reasoning effort</span>
                </label>

                {enableReasoningEffort && (
                  <div className="reasoning-effort-controls">
                    <label className="config-label">Reasoning Effort</label>
                    <select
                      value={reasoningEffort}
                      onChange={(e) => setReasoningEffort(e.target.value as 'low' | 'high')}
                      className="reasoning-effort-select"
                    >
                      <option value="low">Low</option>
                      <option value="high">High</option>
                    </select>
                    <p className="reasoning-effort-help">
                      High effort may produce more thorough analysis but takes longer and uses more tokens.
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {selectedModel && (
            <div className="config-section">
              <ModelInfoView
                model={selectedModel}
                showPricing={true}
                showCapabilities={true}
                showDescription={true}
                compact={isPopup}
              />
            </div>
          )}
        </>
      )}

      {!isConfigured && (
        <div className="provider-status">
          <p className="status-message">
            Configure your xAI API key to access Grok models
          </p>
        </div>
      )}
    </div>
  );
};

// Provider-specific styles
export const xaiProviderStyles = `
.xai-provider {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.provider-note {
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 3px;
}

.note-text {
  font-size: 11px;
  color: var(--adobe-text-secondary);
  margin: 0;
  line-height: 1.4;
}

.reasoning-effort-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.reasoning-checkbox {
  width: 14px;
  height: 14px;
  cursor: pointer;
}

.checkbox-label {
  font-size: 12px;
  color: var(--adobe-text-primary);
  cursor: pointer;
}

.reasoning-effort-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-left: 22px;
}

.reasoning-effort-select {
  width: 100%;
  padding: 8px 12px;
  background: var(--adobe-bg-primary);
  border: 1px solid var(--adobe-border);
  border-radius: 3px;
  color: var(--adobe-text-primary);
  font-size: 12px;
  outline: none;
  transition: border-color 0.2s ease;
}

.reasoning-effort-select:focus {
  border-color: var(--adobe-accent);
  box-shadow: 0 0 0 1px var(--adobe-accent);
}

.reasoning-effort-help {
  font-size: 11px;
  color: var(--adobe-text-secondary);
  margin: 0;
  line-height: 1.4;
}
`;
