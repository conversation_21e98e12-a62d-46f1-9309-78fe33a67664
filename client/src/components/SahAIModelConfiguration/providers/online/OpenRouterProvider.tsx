/**
 * OpenRouter Provider Configuration Component
 * Adapted from Cline for SahAI V2 architecture with specialized model picker
 */

import React, { useState, useEffect } from 'react';
import { useSettingsStore } from '../../../../stores/settingsStore';
import { ApiKeyField, ModelInfoView } from '../../common';
import { getProviderMetadata } from '../../utils/providerUtils';
import { OpenRouterModelPicker } from '../../modelPickers/OpenRouterModelPicker';

interface OpenRouterProviderProps {
  showModelOptions?: boolean;
  isPopup?: boolean;
}

export const OpenRouterProvider: React.FC<OpenRouterProviderProps> = ({
  showModelOptions = true,
  isPopup = false
}) => {
  const {
    providers,
    currentProvider,
    availableModels,
    currentModel,
    modelsLoading,
    modelsError,
    setCurrentProvider,
    setCurrentModel,
    loadModelsForProvider,
    updateProviderApiKey
  } = useSettingsStore();

  const [apiKey, setApiKey] = useState('');
  const [providerSorting, setProviderSorting] = useState<string>('');
  const [enableProviderSorting, setEnableProviderSorting] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const provider = providers.find(p => p.id === 'openrouter');
  const metadata = getProviderMetadata('openrouter');
  const selectedModel = currentModel && currentProvider?.id === 'openrouter' ? currentModel : null;

  // Initialize values from provider if available
  useEffect(() => {
    if (provider?.apiKey) {
      setApiKey(provider.apiKey);
    }
    if (provider?.settings?.providerSorting) {
      setProviderSorting(provider.settings.providerSorting);
      setEnableProviderSorting(true);
    }
  }, [provider]);

  // Load models when provider becomes configured
  useEffect(() => {
    if (provider?.isConfigured && currentProvider?.id === 'openrouter') {
      loadModelsForProvider('openrouter');
    }
  }, [provider?.isConfigured, currentProvider?.id, loadModelsForProvider]);

  const handleApiKeySubmit = async () => {
    if (!apiKey.trim()) {
      setError('API key is required');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      await updateProviderApiKey('openrouter', apiKey.trim());
      await setCurrentProvider('openrouter');
      await loadModelsForProvider('openrouter');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save API key');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleModelSelect = (modelId: string) => {
    setCurrentModel(modelId);
  };

  const isConfigured = provider?.isConfigured ?? false;

  return (
    <div className="provider-config openrouter-provider">
      <div className="provider-header">
        <h3 className="provider-title">{metadata?.name || 'OpenRouter'}</h3>
        <p className="provider-description">
          {metadata?.description || 'Access to multiple AI models through one API'}
        </p>
      </div>

      <div className="config-section">
        <ApiKeyField
          label="OpenRouter API Key"
          placeholder="sk-or-..."
          value={apiKey}
          onChange={setApiKey}
          onSubmit={handleApiKeySubmit}
          helpText="Get your API key from"
          helpLink={metadata?.website}
          helpLinkText="OpenRouter"
          disabled={isSubmitting}
          error={error || undefined}
          submitDisabled={isSubmitting || !apiKey.trim()}
          submitButtonText={isSubmitting ? 'Saving...' : 'Save'}
        />
        
        <div className="openrouter-note">
          <p className="note-text">
            This key is stored locally and only used to make API requests from this extension.
          </p>
        </div>
      </div>

      {showModelOptions && isConfigured && (
        <>
          <div className="config-section">
            <div className="provider-sorting-section">
              <label className="checkbox-container">
                <input
                  type="checkbox"
                  checked={enableProviderSorting}
                  onChange={(e) => {
                    setEnableProviderSorting(e.target.checked);
                    if (!e.target.checked) {
                      setProviderSorting('');
                    }
                  }}
                  className="sorting-checkbox"
                />
                <span className="checkbox-label">Sort underlying provider routing</span>
              </label>

              {enableProviderSorting && (
                <div className="sorting-controls">
                  <select
                    value={providerSorting}
                    onChange={(e) => setProviderSorting(e.target.value)}
                    className="sorting-select"
                  >
                    <option value="">Default</option>
                    <option value="price">Price</option>
                    <option value="throughput">Throughput</option>
                    <option value="latency">Latency</option>
                  </select>
                  
                  <div className="sorting-help">
                    {!providerSorting && (
                      <p>Default behavior is to load balance requests across providers (like AWS, Google Vertex, Anthropic), prioritizing price while considering provider uptime</p>
                    )}
                    {providerSorting === 'price' && (
                      <p>Sort providers by price, prioritizing the lowest cost provider</p>
                    )}
                    {providerSorting === 'throughput' && (
                      <p>Sort providers by throughput, prioritizing the provider with the highest throughput (may increase cost)</p>
                    )}
                    {providerSorting === 'latency' && (
                      <p>Sort providers by response time, prioritizing the provider with the lowest latency</p>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="config-section">
            <label className="config-label">Model</label>
            <OpenRouterModelPicker
              models={availableModels}
              selectedModelId={selectedModel?.id || ''}
              onModelSelect={handleModelSelect}
              loading={modelsLoading}
              error={modelsError || undefined}
              isPopup={isPopup}
            />
          </div>

          {selectedModel && (
            <div className="config-section">
              <ModelInfoView
                model={selectedModel}
                showPricing={true}
                showCapabilities={true}
                showDescription={true}
                compact={isPopup}
              />
            </div>
          )}
        </>
      )}

      {!isConfigured && (
        <div className="provider-status">
          <p className="status-message">
            Configure your OpenRouter API key to access multiple AI models
          </p>
        </div>
      )}
    </div>
  );
};

// Provider-specific styles
export const openRouterProviderStyles = `
.openrouter-provider {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.openrouter-note {
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(70, 160, 245, 0.1);
  border: 1px solid rgba(70, 160, 245, 0.2);
  border-radius: 3px;
}

.note-text {
  font-size: 11px;
  color: var(--adobe-text-secondary);
  margin: 0;
  line-height: 1.4;
}

.provider-sorting-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.sorting-checkbox {
  width: 14px;
  height: 14px;
  cursor: pointer;
}

.checkbox-label {
  font-size: 12px;
  color: var(--adobe-text-primary);
  cursor: pointer;
}

.sorting-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-left: 22px;
}

.sorting-select {
  width: 100%;
  padding: 8px 12px;
  background: var(--adobe-bg-primary);
  border: 1px solid var(--adobe-border);
  border-radius: 3px;
  color: var(--adobe-text-primary);
  font-size: 12px;
  outline: none;
  transition: border-color 0.2s ease;
}

.sorting-select:focus {
  border-color: var(--adobe-accent);
  box-shadow: 0 0 0 1px var(--adobe-accent);
}

.sorting-help p {
  font-size: 11px;
  color: var(--adobe-text-secondary);
  margin: 0;
  line-height: 1.4;
}
`;
