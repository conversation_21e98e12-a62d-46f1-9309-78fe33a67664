/**
 * Groq Provider Configuration Component
 * Adapted from Cline for SahAI V2 architecture with specialized model picker
 */

import React, { useState, useEffect } from 'react';
import { useSettingsStore } from '../../../../stores/settingsStore';
import { <PERSON>pi<PERSON>eyField, ModelInfoView } from '../../common';
import { getProviderMetadata } from '../../utils/providerUtils';
import { GroqModelPicker } from '../../modelPickers/GroqModelPicker';

interface GroqProviderProps {
  showModelOptions?: boolean;
  isPopup?: boolean;
}

export const GroqProvider: React.FC<GroqProviderProps> = ({
  showModelOptions = true,
  isPopup = false
}) => {
  const {
    providers,
    currentProvider,
    availableModels,
    currentModel,
    modelsLoading,
    modelsError,
    setCurrentProvider,
    setCurrentModel,
    loadModelsForProvider,
    updateProviderApiKey
  } = useSettingsStore();

  const [apiKey, setApiKey] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const provider = providers.find(p => p.id === 'groq');
  const metadata = getProviderMetadata('groq');
  const selectedModel = currentModel && currentProvider?.id === 'groq' ? currentModel : null;

  // Initialize API key from provider if available
  useEffect(() => {
    if (provider?.apiKey) {
      setApiKey(provider.apiKey);
    }
  }, [provider]);

  // Load models when provider becomes configured
  useEffect(() => {
    if (provider?.isConfigured && currentProvider?.id === 'groq') {
      loadModelsForProvider('groq');
    }
  }, [provider?.isConfigured, currentProvider?.id, loadModelsForProvider]);

  const handleApiKeySubmit = async () => {
    if (!apiKey.trim()) {
      setError('API key is required');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      await updateProviderApiKey('groq', apiKey.trim());
      await setCurrentProvider('groq');
      await loadModelsForProvider('groq');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save API key');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleModelSelect = (modelId: string) => {
    setCurrentModel(modelId);
  };

  const isConfigured = provider?.isConfigured ?? false;

  return (
    <div className="provider-config groq-provider">
      <div className="provider-header">
        <h3 className="provider-title">{metadata?.name || 'Groq'}</h3>
        <p className="provider-description">
          {metadata?.description || 'Ultra-fast inference with Groq\'s LPU technology'}
        </p>
      </div>

      <div className="config-section">
        <ApiKeyField
          label="Groq API Key"
          placeholder="gsk_..."
          value={apiKey}
          onChange={setApiKey}
          onSubmit={handleApiKeySubmit}
          helpText="Get your API key from"
          helpLink={metadata?.website}
          helpLinkText="Groq Console"
          disabled={isSubmitting}
          error={error || undefined}
          submitDisabled={isSubmitting || !apiKey.trim()}
          submitButtonText={isSubmitting ? 'Saving...' : 'Save'}
        />
      </div>

      {showModelOptions && isConfigured && (
        <>
          <div className="config-section">
            <label className="config-label">Model</label>
            <GroqModelPicker
              models={availableModels}
              selectedModelId={selectedModel?.id || ''}
              onModelSelect={handleModelSelect}
              loading={modelsLoading}
              error={modelsError || undefined}
              isPopup={isPopup}
            />
          </div>

          {selectedModel && (
            <div className="config-section">
              <ModelInfoView
                model={selectedModel}
                showPricing={true}
                showCapabilities={true}
                showDescription={true}
                compact={isPopup}
              />
            </div>
          )}
        </>
      )}

      {!isConfigured && (
        <div className="provider-status">
          <p className="status-message">
            Configure your Groq API key to access ultra-fast LLM inference
          </p>
        </div>
      )}
    </div>
  );
};

// Provider-specific styles
export const groqProviderStyles = `
.groq-provider {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
`;
