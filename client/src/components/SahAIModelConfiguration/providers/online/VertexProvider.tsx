/**
 * Google Vertex AI Provider Configuration Component
 * Adapted from Cline for SahAI V2 architecture with region selection
 */

import React, { useState, useEffect } from 'react';
import { useSettingsStore } from '../../../../stores/settingsStore';
import { ModelSelector, ModelInfoView } from '../../common';
import { getProviderMetadata } from '../../utils/providerUtils';

interface VertexProviderProps {
  showModelOptions?: boolean;
  isPopup?: boolean;
}

// Models that support thinking/reasoning features
const SUPPORTED_THINKING_MODELS = [
  'claude-3-7-sonnet@20250219',
  'claude-sonnet-4@20250514',
  'claude-opus-4@20250514',
  'gemini-2.5-flash',
  'gemini-2.5-pro',
  'gemini-2.5-flash-lite-preview-06-17',
];

export const VertexProvider: React.FC<VertexProviderProps> = ({
  showModelOptions = true,
  isPopup = false
}) => {
  const {
    providers,
    currentProvider,
    availableModels,
    currentModel,
    modelsLoading,
    modelsError,
    setCurrentProvider,
    setCurrentModel,
    loadModelsForProvider
  } = useSettingsStore();

  const [projectId, setProjectId] = useState('');
  const [region, setRegion] = useState('us-central1');
  const [thinkingBudget, setThinkingBudget] = useState(10);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const provider = providers.find(p => p.id === 'vertex');
  const metadata = getProviderMetadata('vertex');
  const selectedModel = currentModel && currentProvider?.id === 'vertex' ? currentModel : null;

  // Initialize values from provider if available
  useEffect(() => {
    if (provider?.settings?.projectId) {
      setProjectId(provider.settings.projectId);
    }
    if (provider?.settings?.region) {
      setRegion(provider.settings.region);
    }
  }, [provider]);

  // Auto-connect when project ID and region are set
  useEffect(() => {
    if (projectId.trim() && region && currentProvider?.id === 'vertex') {
      handleConnect();
    }
  }, [projectId, region, currentProvider?.id]);

  const handleConnect = async () => {
    if (!projectId.trim()) {
      setError('Project ID is required');
      return;
    }

    setIsConnecting(true);
    setError(null);

    try {
      await setCurrentProvider('vertex');
      await loadModelsForProvider('vertex');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to connect to Vertex AI');
    } finally {
      setIsConnecting(false);
    }
  };

  const handleModelSelect = (modelId: string) => {
    setCurrentModel(modelId);
  };

  const isConfigured = projectId.trim() && region;
  const supportsThinking = selectedModel && SUPPORTED_THINKING_MODELS.includes(selectedModel.id);

  return (
    <div className="provider-config vertex-provider">
      <div className="provider-header">
        <h3 className="provider-title">{metadata?.name || 'Google Vertex AI'}</h3>
        <p className="provider-description">
          {metadata?.description || 'Google Cloud\'s Vertex AI platform'}
        </p>
      </div>

      <div className="config-section">
        <label className="config-label">Google Cloud Project ID</label>
        <input
          type="text"
          className="project-id-input"
          placeholder="Enter Project ID..."
          value={projectId}
          onChange={(e) => setProjectId(e.target.value)}
          disabled={isConnecting}
        />
      </div>

      <div className="config-section">
        <label className="config-label">Google Cloud Region</label>
        <select
          value={region}
          onChange={(e) => setRegion(e.target.value)}
          className="region-select"
          disabled={isConnecting}
        >
          <option value="">Select a region...</option>
          <option value="us-east5">us-east5</option>
          <option value="us-central1">us-central1</option>
          <option value="europe-west1">europe-west1</option>
          <option value="europe-west4">europe-west4</option>
          <option value="asia-southeast1">asia-southeast1</option>
          <option value="global">global</option>
        </select>
      </div>

      <div className="vertex-setup-info">
        <p className="setup-text">
          To use Google Cloud Vertex AI, you need to:
        </p>
        <ol className="setup-steps">
          <li>
            <a 
              href="https://cloud.google.com/vertex-ai/generative-ai/docs/partner-models/use-claude#before_you_begin"
              target="_blank"
              rel="noopener noreferrer"
              className="setup-link"
            >
              Create a Google Cloud account › enable the Vertex AI API › enable the desired Claude models
            </a>
          </li>
          <li>
            <a 
              href="https://cloud.google.com/docs/authentication/provide-credentials-adc#google-idp"
              target="_blank"
              rel="noopener noreferrer"
              className="setup-link"
            >
              Install the Google Cloud CLI › configure Application Default Credentials
            </a>
          </li>
        </ol>
      </div>

      {error && (
        <div className="vertex-error">
          <span>{error}</span>
        </div>
      )}

      {showModelOptions && isConfigured && (
        <>
          <div className="config-section">
            <label className="config-label">Model</label>
            <ModelSelector
              models={availableModels}
              selectedModelId={selectedModel?.id || ''}
              onModelSelect={handleModelSelect}
              placeholder="Select Vertex AI model"
              loading={modelsLoading}
              error={modelsError || undefined}
              showSearch={availableModels.length > 5}
              showModelInfo={true}
              emptyMessage="No Vertex AI models available"
            />
          </div>

          {supportsThinking && (
            <div className="config-section">
              <label className="config-label">Thinking Budget</label>
              <div className="thinking-budget-container">
                <input
                  type="range"
                  min="1"
                  max="100"
                  value={thinkingBudget}
                  onChange={(e) => setThinkingBudget(Number(e.target.value))}
                  className="thinking-budget-slider"
                />
                <div className="thinking-budget-display">
                  <span className="budget-value">{thinkingBudget}</span>
                  <span className="budget-unit">tokens</span>
                </div>
              </div>
              <p className="thinking-budget-help">
                Controls the computational budget for reasoning. Higher values allow more thorough thinking but use more tokens.
              </p>
            </div>
          )}

          {selectedModel && (
            <div className="config-section">
              <ModelInfoView
                model={selectedModel}
                showPricing={true}
                showCapabilities={true}
                showDescription={true}
                compact={isPopup}
              />
            </div>
          )}
        </>
      )}

      {!isConfigured && (
        <div className="provider-status">
          <p className="status-message">
            Configure your Google Cloud Project ID and region to access Vertex AI models
          </p>
        </div>
      )}
    </div>
  );
};

// Provider-specific styles
export const vertexProviderStyles = `
.vertex-provider {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.project-id-input,
.region-select {
  width: 100%;
  padding: 8px 12px;
  background: var(--adobe-bg-primary);
  border: 1px solid var(--adobe-border);
  border-radius: 3px;
  color: var(--adobe-text-primary);
  font-size: 12px;
  outline: none;
  transition: border-color 0.2s ease;
}

.project-id-input:focus,
.region-select:focus {
  border-color: var(--adobe-accent);
  box-shadow: 0 0 0 1px var(--adobe-accent);
}

.vertex-setup-info {
  padding: 12px;
  background: rgba(70, 160, 245, 0.1);
  border: 1px solid rgba(70, 160, 245, 0.2);
  border-radius: 3px;
}

.setup-text {
  font-size: 12px;
  color: var(--adobe-text-primary);
  margin: 0 0 8px 0;
  font-weight: 500;
}

.setup-steps {
  margin: 0;
  padding-left: 16px;
  font-size: 11px;
  color: var(--adobe-text-secondary);
  line-height: 1.4;
}

.setup-steps li {
  margin-bottom: 4px;
}

.setup-link {
  color: var(--adobe-accent);
  text-decoration: none;
}

.setup-link:hover {
  text-decoration: underline;
}

.vertex-error {
  padding: 8px 12px;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 3px;
  color: var(--adobe-error);
  font-size: 11px;
}

.thinking-budget-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.thinking-budget-slider {
  flex: 1;
  height: 4px;
  background: var(--adobe-bg-tertiary);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
}

.thinking-budget-slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  background: var(--adobe-accent);
  border-radius: 50%;
  cursor: pointer;
}

.thinking-budget-display {
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 60px;
}

.budget-value {
  font-weight: 600;
  color: var(--adobe-text-primary);
  font-size: 12px;
}

.budget-unit {
  font-size: 10px;
  color: var(--adobe-text-secondary);
}

.thinking-budget-help {
  font-size: 11px;
  color: var(--adobe-text-secondary);
  margin: 4px 0 0 0;
  line-height: 1.4;
}
`;
