/**
 * Moonshot AI Provider Configuration Component
 * Adapted from Cline for SahAI V2 architecture
 */

import React, { useState, useEffect } from 'react';
import { useSettingsStore } from '../../../../stores/settingsStore';
import { A<PERSON><PERSON><PERSON><PERSON>ield, ModelSelector, ModelInfoView } from '../../common';
import { getProviderMetadata } from '../../utils/providerUtils';

interface MoonshotProviderProps {
  showModelOptions?: boolean;
  isPopup?: boolean;
}

export const MoonshotProvider: React.FC<MoonshotProviderProps> = ({
  showModelOptions = true,
  isPopup = false
}) => {
  const {
    providers,
    currentProvider,
    availableModels,
    currentModel,
    modelsLoading,
    modelsError,
    setCurrentProvider,
    setCurrentModel,
    loadModelsForProvider,
    updateProviderApiKey
  } = useSettingsStore();

  const [apiKey, setApiKey] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const provider = providers.find(p => p.id === 'moonshot');
  const metadata = getProviderMetadata('moonshot');
  const selectedModel = currentModel && currentProvider?.id === 'moonshot' ? currentModel : null;

  // Initialize API key from provider if available
  useEffect(() => {
    if (provider?.apiKey) {
      setApiKey(provider.apiKey);
    }
  }, [provider]);

  // Load models when provider becomes configured
  useEffect(() => {
    if (provider?.isConfigured && currentProvider?.id === 'moonshot') {
      loadModelsForProvider('moonshot');
    }
  }, [provider?.isConfigured, currentProvider?.id, loadModelsForProvider]);

  const handleApiKeySubmit = async () => {
    if (!apiKey.trim()) {
      setError('API key is required');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      await updateProviderApiKey('moonshot', apiKey.trim());
      await setCurrentProvider('moonshot');
      await loadModelsForProvider('moonshot');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save API key');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleModelSelect = (modelId: string) => {
    setCurrentModel(modelId);
  };

  const isConfigured = provider?.isConfigured ?? false;

  return (
    <div className="provider-config moonshot-provider">
      <div className="provider-header">
        <h3 className="provider-title">{metadata?.name || 'Moonshot AI'}</h3>
        <p className="provider-description">
          {metadata?.description || 'Moonshot\'s Kimi models with long context'}
        </p>
      </div>

      <div className="config-section">
        <ApiKeyField
          label="Moonshot API Key"
          placeholder="sk-..."
          value={apiKey}
          onChange={setApiKey}
          onSubmit={handleApiKeySubmit}
          helpText="Get your API key from"
          helpLink={metadata?.website}
          helpLinkText="Moonshot Platform"
          disabled={isSubmitting}
          error={error || undefined}
          submitDisabled={isSubmitting || !apiKey.trim()}
          submitButtonText={isSubmitting ? 'Saving...' : 'Save'}
        />
        
        <div className="moonshot-note">
          <p className="note-text">
            <strong>Long Context Models:</strong> Moonshot's Kimi models are optimized for 
            long-context understanding and can handle extensive documents and conversations.
          </p>
        </div>
      </div>

      {showModelOptions && isConfigured && (
        <>
          <div className="config-section">
            <label className="config-label">Model</label>
            <ModelSelector
              models={availableModels}
              selectedModelId={selectedModel?.id || ''}
              onModelSelect={handleModelSelect}
              placeholder="Select Moonshot model"
              loading={modelsLoading}
              error={modelsError || undefined}
              showSearch={availableModels.length > 5}
              showModelInfo={true}
              emptyMessage="No Moonshot models available"
            />
          </div>

          {selectedModel && (
            <div className="config-section">
              <ModelInfoView
                model={selectedModel}
                showPricing={true}
                showCapabilities={true}
                showDescription={true}
                compact={isPopup}
              />
            </div>
          )}
        </>
      )}

      {!isConfigured && (
        <div className="provider-status">
          <p className="status-message">
            Configure your Moonshot API key to access Kimi models with long context
          </p>
        </div>
      )}
    </div>
  );
};

// Provider-specific styles
export const moonshotProviderStyles = `
.moonshot-provider {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.moonshot-note {
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(147, 51, 234, 0.1);
  border: 1px solid rgba(147, 51, 234, 0.3);
  border-radius: 3px;
}

.note-text {
  font-size: 11px;
  color: var(--adobe-text-secondary);
  margin: 0;
  line-height: 1.4;
}

.note-text strong {
  color: var(--adobe-text-primary);
}
`;
