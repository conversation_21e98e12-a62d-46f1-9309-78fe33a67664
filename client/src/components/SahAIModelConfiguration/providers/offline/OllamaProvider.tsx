/**
 * Ollama Provider Configuration Component
 * Adapted from Cline for SahAI V2 architecture with local model discovery
 */

import React, { useState, useEffect } from 'react';
import { useSettingsStore } from '../../../../stores/settingsStore';
import { ModelInfoView } from '../../common';
import { getProviderMetadata } from '../../utils/providerUtils';
import { OllamaModelPicker } from '../../modelPickers/OllamaModelPicker';
import { RefreshIcon, InfoIcon } from '../../../ui';

interface OllamaProviderProps {
  showModelOptions?: boolean;
  isPopup?: boolean;
}

export const OllamaProvider: React.FC<OllamaProviderProps> = ({
  showModelOptions = true,
  isPopup = false
}) => {
  const {
    providers,
    currentProvider,
    availableModels,
    currentModel,
    modelsLoading,
    modelsError,
    setCurrentProvider,
    setCurrentModel,
    loadModelsForProvider
  } = useSettingsStore();

  const [baseUrl, setBaseUrl] = useState('http://localhost:11434');
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'unknown' | 'connected' | 'error'>('unknown');
  const [error, setError] = useState<string | null>(null);

  const provider = providers.find(p => p.id === 'ollama');
  const metadata = getProviderMetadata('ollama');
  const selectedModel = currentModel && currentProvider?.id === 'ollama' ? currentModel : null;

  // Initialize base URL from provider if available
  useEffect(() => {
    if (provider?.baseURL) {
      setBaseUrl(provider.baseURL);
    }
  }, [provider]);

  // Auto-connect when component mounts
  useEffect(() => {
    if (currentProvider?.id === 'ollama') {
      handleConnect();
    }
  }, [currentProvider?.id]);

  const handleConnect = async () => {
    setIsConnecting(true);
    setError(null);

    try {
      // Test connection to Ollama
      const response = await fetch(`${baseUrl}/api/tags`);
      if (response.ok) {
        setConnectionStatus('connected');
        await setCurrentProvider('ollama');
        await loadModelsForProvider('ollama');
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (err) {
      setConnectionStatus('error');
      setError(err instanceof Error ? err.message : 'Failed to connect to Ollama');
    } finally {
      setIsConnecting(false);
    }
  };

  const handleRefreshModels = async () => {
    if (connectionStatus === 'connected') {
      await loadModelsForProvider('ollama');
    }
  };

  const handleModelSelect = (modelId: string) => {
    setCurrentModel(modelId);
  };

  const isConfigured = connectionStatus === 'connected';

  return (
    <div className="provider-config ollama-provider">
      <div className="provider-header">
        <h3 className="provider-title">{metadata?.name || 'Ollama'}</h3>
        <p className="provider-description">
          {metadata?.description || 'Run large language models locally with Ollama'}
        </p>
      </div>

      <div className="config-section">
        <label className="config-label">Ollama Server URL</label>
        <div className="ollama-connection">
          <div className="url-input-container">
            <input
              type="text"
              className="base-url-input"
              placeholder="http://localhost:11434"
              value={baseUrl}
              onChange={(e) => setBaseUrl(e.target.value)}
              disabled={isConnecting}
            />
            <button
              className="connect-button"
              onClick={handleConnect}
              disabled={isConnecting || !baseUrl.trim()}
            >
              {isConnecting ? 'Connecting...' : 'Connect'}
            </button>
          </div>
          
          <div className={`connection-status ${connectionStatus}`}>
            <div className="status-indicator" />
            <span className="status-text">
              {connectionStatus === 'connected' && 'Connected to Ollama'}
              {connectionStatus === 'error' && 'Connection failed'}
              {connectionStatus === 'unknown' && 'Not connected'}
            </span>
          </div>
        </div>
        
        {error && (
          <div className="connection-error">
            <InfoIcon size={14} />
            <span>{error}</span>
          </div>
        )}
        
        <div className="ollama-help">
          <p>
            Make sure Ollama is running locally. 
            <a 
              href="https://ollama.ai/" 
              target="_blank" 
              rel="noopener noreferrer"
              className="help-link"
            >
              Download Ollama
            </a>
          </p>
        </div>
      </div>

      {showModelOptions && isConfigured && (
        <>
          <div className="config-section">
            <div className="models-header">
              <label className="config-label">Local Models</label>
              <button
                className="refresh-models-button"
                onClick={handleRefreshModels}
                disabled={modelsLoading}
                title="Refresh model list"
              >
                <RefreshIcon size={14} className={modelsLoading ? 'spinning' : ''} />
              </button>
            </div>
            
            <OllamaModelPicker
              models={availableModels}
              selectedModelId={selectedModel?.id || ''}
              onModelSelect={handleModelSelect}
              loading={modelsLoading}
              error={modelsError || undefined}
              baseUrl={baseUrl}
              isPopup={isPopup}
            />
          </div>

          {selectedModel && (
            <div className="config-section">
              <ModelInfoView
                model={selectedModel}
                showPricing={false} // Local models are free
                showCapabilities={true}
                showDescription={true}
                compact={isPopup}
              />
            </div>
          )}
        </>
      )}

      {!isConfigured && connectionStatus !== 'unknown' && (
        <div className="provider-status">
          <p className="status-message">
            Connect to your local Ollama server to access models
          </p>
        </div>
      )}
    </div>
  );
};

// Provider-specific styles
export const ollamaProviderStyles = `
.ollama-provider {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.ollama-connection {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.url-input-container {
  display: flex;
  gap: 8px;
  align-items: center;
}

.base-url-input {
  flex: 1;
  padding: 8px 12px;
  background: var(--adobe-bg-primary);
  border: 1px solid var(--adobe-border);
  border-radius: 3px;
  color: var(--adobe-text-primary);
  font-size: 12px;
  outline: none;
  transition: border-color 0.2s ease;
}

.base-url-input:focus {
  border-color: var(--adobe-accent);
  box-shadow: 0 0 0 1px var(--adobe-accent);
}

.connect-button {
  padding: 8px 16px;
  background: var(--adobe-accent);
  border: none;
  border-radius: 3px;
  color: white;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  white-space: nowrap;
}

.connect-button:hover:not(:disabled) {
  background: var(--adobe-accent-hover);
}

.connect-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--adobe-text-secondary);
}

.connection-status.connected .status-indicator {
  background: #10b981;
}

.connection-status.error .status-indicator {
  background: var(--adobe-error);
}

.connection-status.connected .status-text {
  color: #10b981;
}

.connection-status.error .status-text {
  color: var(--adobe-error);
}

.connection-error {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 3px;
  color: var(--adobe-error);
  font-size: 11px;
}

.ollama-help {
  font-size: 11px;
  color: var(--adobe-text-secondary);
}

.ollama-help p {
  margin: 0;
  line-height: 1.4;
}

.help-link {
  color: var(--adobe-accent);
  text-decoration: none;
  margin-left: 4px;
}

.help-link:hover {
  text-decoration: underline;
}

.models-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.refresh-models-button {
  background: none;
  border: 1px solid var(--adobe-border);
  border-radius: 3px;
  padding: 4px;
  cursor: pointer;
  color: var(--adobe-text-secondary);
  transition: all 0.2s ease;
}

.refresh-models-button:hover {
  background: var(--adobe-bg-tertiary);
  color: var(--adobe-text-primary);
}

.refresh-models-button .spinning {
  animation: spin 1s linear infinite;
}
`;
