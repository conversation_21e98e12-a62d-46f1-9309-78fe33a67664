/**
 * Generic Model Selector Component
 * Used for basic model selection across providers
 */

import React, { useState, useMemo } from 'react';
import { ModelInfo } from '../../../stores/settingsStore';
import { ChevronDownIcon, SearchIcon, InfoIcon } from '../../ui';

interface ModelSelectorProps {
  models: ModelInfo[];
  selectedModelId: string;
  onModelSelect: (modelId: string) => void;
  placeholder?: string;
  disabled?: boolean;
  loading?: boolean;
  error?: string;
  showSearch?: boolean;
  showModelInfo?: boolean;
  emptyMessage?: string;
}

export const ModelSelector: React.FC<ModelSelectorProps> = ({
  models,
  selectedModelId,
  onModelSelect,
  placeholder = 'Select Model',
  disabled = false,
  loading = false,
  error,
  showSearch = true,
  showModelInfo = true,
  emptyMessage = 'No models available'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Filter models based on search query
  const filteredModels = useMemo(() => {
    if (!searchQuery.trim()) {
      return models;
    }
    
    const query = searchQuery.toLowerCase();
    return models.filter(model =>
      model.name.toLowerCase().includes(query) ||
      model.id.toLowerCase().includes(query) ||
      (model.description && model.description.toLowerCase().includes(query))
    );
  }, [models, searchQuery]);

  const selectedModel = models.find(m => m.id === selectedModelId);

  const handleModelSelect = (modelId: string) => {
    onModelSelect(modelId);
    setIsOpen(false);
    setSearchQuery('');
  };

  const handleToggle = () => {
    if (!disabled && !loading) {
      setIsOpen(!isOpen);
    }
  };

  if (loading) {
    return (
      <div className="model-selector">
        <div className="model-selector-loading">
          <InfoIcon size={16} className="loading-icon" />
          <span>Loading models...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="model-selector">
        <div className="model-selector-error">
          <InfoIcon size={16} className="error-icon" />
          <span>Error: {error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="model-selector">
      {showSearch && models.length > 5 && (
        <div className="model-search-container">
          <SearchIcon size={14} className="search-icon" />
          <input
            type="text"
            className="model-search-input"
            placeholder="Search models..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onFocus={() => setIsOpen(true)}
            disabled={disabled}
          />
        </div>
      )}
      
      <div className="dropdown-container">
        <button
          className={`dropdown-trigger ${isOpen ? 'open' : ''} ${disabled ? 'disabled' : ''}`}
          onClick={handleToggle}
          disabled={disabled}
          aria-expanded={isOpen}
        >
          <span className="dropdown-text">
            {selectedModel ? selectedModel.name : placeholder}
          </span>
          <ChevronDownIcon size={14} className="dropdown-icon" />
        </button>
        
        {isOpen && (
          <div className="dropdown-menu model-dropdown">
            {filteredModels.length > 0 ? (
              filteredModels.map(model => (
                <button
                  key={model.id}
                  className={`dropdown-item ${selectedModelId === model.id ? 'selected' : ''}`}
                  onClick={() => handleModelSelect(model.id)}
                >
                  <div className="model-item">
                    <span className="model-name">{model.name}</span>
                    {model.description && (
                      <span className="model-description">{model.description}</span>
                    )}
                    {showModelInfo && (
                      <div className="model-meta">
                        {model.contextLength && (
                          <span className="model-context">
                            {model.contextLength.toLocaleString()} tokens
                          </span>
                        )}
                        {model.capabilities && model.capabilities.length > 0 && (
                          <div className="model-capabilities">
                            {model.capabilities.slice(0, 3).map((cap, idx) => (
                              <span key={idx} className="capability-tag">
                                {cap}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </button>
              ))
            ) : (
              <div className="no-models">
                <InfoIcon size={16} />
                <span>{searchQuery ? 'No models found' : emptyMessage}</span>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// CSS styles for the component
export const modelSelectorStyles = `
.model-selector {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.model-selector-loading,
.model-selector-error {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  color: var(--adobe-text-secondary);
  font-size: 12px;
}

.model-selector-error {
  color: var(--adobe-error);
}

.loading-icon {
  animation: spin 1s linear infinite;
}

.error-icon {
  color: var(--adobe-error);
}

.model-search-container {
  position: relative;
  margin-bottom: 8px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--adobe-text-secondary);
  pointer-events: none;
}

.model-search-input {
  width: 100%;
  padding: 8px 12px 8px 36px;
  background: var(--adobe-bg-primary);
  border: 1px solid var(--adobe-border);
  border-radius: 3px;
  color: var(--adobe-text-primary);
  font-size: 12px;
  outline: none;
  transition: border-color 0.2s ease;
}

.model-search-input:focus {
  border-color: var(--adobe-accent);
  box-shadow: 0 0 0 1px var(--adobe-accent);
}

.model-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
  text-align: left;
}

.model-name {
  font-weight: 500;
  font-size: 12px;
}

.model-description {
  font-size: 11px;
  opacity: 0.7;
  line-height: 1.3;
}

.model-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 2px;
}

.model-context {
  font-size: 10px;
  color: var(--adobe-text-secondary);
}

.model-capabilities {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.capability-tag {
  padding: 1px 4px;
  background: var(--adobe-bg-tertiary);
  border-radius: 2px;
  font-size: 9px;
  color: var(--adobe-text-primary);
}

.no-models {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  color: var(--adobe-text-secondary);
  font-size: 12px;
  justify-content: center;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
`;
