/**
 * Reusable API Key Input Field Component
 * Used across provider configuration components
 */

import React, { useState } from 'react';
import { EyeIcon, EyeOffIcon } from '../../ui';

interface ApiKeyFieldProps {
  label?: string;
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  onSubmit?: () => void;
  helpText?: string;
  helpLink?: string;
  helpLinkText?: string;
  disabled?: boolean;
  error?: string;
  showSubmitButton?: boolean;
  submitButtonText?: string;
  submitDisabled?: boolean;
}

export const ApiKeyField: React.FC<ApiKeyFieldProps> = ({
  label = 'API Key',
  placeholder = 'Enter your API key',
  value,
  onChange,
  onSubmit,
  helpText,
  helpLink,
  helpLinkText,
  disabled = false,
  error,
  showSubmitButton = true,
  submitButtonText = 'Save',
  submitDisabled = false
}) => {
  const [showKey, setShowKey] = useState(false);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && onSubmit && !submitDisabled) {
      onSubmit();
    }
  };

  const toggleShowKey = () => {
    setShowKey(!showKey);
  };

  return (
    <div className="api-key-field">
      {label && (
        <label className="config-label">
          {label}
        </label>
      )}
      
      <div className="api-key-input-container">
        <div className="api-key-input-wrapper">
          <input
            type={showKey ? 'text' : 'password'}
            className={`api-key-input ${error ? 'error' : ''}`}
            placeholder={placeholder}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onKeyDown={handleKeyDown}
            disabled={disabled}
            autoComplete="off"
            spellCheck={false}
          />
          
          <button
            type="button"
            className="api-key-toggle"
            onClick={toggleShowKey}
            disabled={disabled}
            aria-label={showKey ? 'Hide API key' : 'Show API key'}
          >
            {showKey ? <EyeOffIcon size={14} /> : <EyeIcon size={14} />}
          </button>
        </div>
        
        {showSubmitButton && onSubmit && (
          <button
            className="api-key-submit"
            onClick={onSubmit}
            disabled={disabled || submitDisabled || !value.trim()}
          >
            {submitButtonText}
          </button>
        )}
      </div>
      
      {error && (
        <div className="api-key-error">
          {error}
        </div>
      )}
      
      {helpText && (
        <div className="api-key-help">
          {helpText}
          {helpLink && helpLinkText && (
            <>
              {' '}
              <a 
                href={helpLink}
                target="_blank"
                rel="noopener noreferrer"
                className="api-key-link"
              >
                {helpLinkText}
              </a>
            </>
          )}
        </div>
      )}
    </div>
  );
};

// CSS styles for the component
export const apiKeyFieldStyles = `
.api-key-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.api-key-input-container {
  display: flex;
  gap: 8px;
  align-items: flex-start;
}

.api-key-input-wrapper {
  position: relative;
  flex: 1;
}

.api-key-input {
  width: 100%;
  padding: 8px 40px 8px 12px;
  background: var(--adobe-bg-primary);
  border: 1px solid var(--adobe-border);
  border-radius: 3px;
  color: var(--adobe-text-primary);
  font-size: 12px;
  outline: none;
  transition: border-color 0.2s ease;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.api-key-input:focus {
  border-color: var(--adobe-accent);
  box-shadow: 0 0 0 1px var(--adobe-accent);
}

.api-key-input.error {
  border-color: var(--adobe-error);
}

.api-key-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.api-key-toggle {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--adobe-text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease;
}

.api-key-toggle:hover:not(:disabled) {
  color: var(--adobe-text-primary);
}

.api-key-toggle:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.api-key-submit {
  padding: 8px 16px;
  background: var(--adobe-accent);
  border: none;
  border-radius: 3px;
  color: white;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  white-space: nowrap;
}

.api-key-submit:hover:not(:disabled) {
  background: var(--adobe-accent-hover);
}

.api-key-submit:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.api-key-error {
  font-size: 11px;
  color: var(--adobe-error);
  margin-top: 4px;
}

.api-key-help {
  font-size: 11px;
  color: var(--adobe-text-secondary);
  line-height: 1.4;
}

.api-key-link {
  color: var(--adobe-accent);
  text-decoration: none;
}

.api-key-link:hover {
  text-decoration: underline;
}
`;
