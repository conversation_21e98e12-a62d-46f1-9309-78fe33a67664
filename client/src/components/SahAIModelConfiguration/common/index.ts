/**
 * Common Components Export
 * Centralized exports for reusable components
 */

export { ApiKeyField, apiKeyFieldStyles } from './ApiKeyField';
export { ModelSelector, modelSelectorStyles } from './ModelSelector';
export { ModelInfoView, modelInfoViewStyles } from './ModelInfoView';

// Combined styles for easy import
export const commonComponentStyles = `
/* API Key Field Styles */
.api-key-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.api-key-input-container {
  display: flex;
  gap: 8px;
  align-items: flex-start;
}

.api-key-input-wrapper {
  position: relative;
  flex: 1;
}

.api-key-input {
  width: 100%;
  padding: 8px 40px 8px 12px;
  background: var(--adobe-bg-primary);
  border: 1px solid var(--adobe-border);
  border-radius: 3px;
  color: var(--adobe-text-primary);
  font-size: 12px;
  outline: none;
  transition: border-color 0.2s ease;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.api-key-input:focus {
  border-color: var(--adobe-accent);
  box-shadow: 0 0 0 1px var(--adobe-accent);
}

.api-key-input.error {
  border-color: var(--adobe-error);
}

.api-key-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.api-key-toggle {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--adobe-text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease;
}

.api-key-toggle:hover:not(:disabled) {
  color: var(--adobe-text-primary);
}

.api-key-toggle:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.api-key-submit {
  padding: 8px 16px;
  background: var(--adobe-accent);
  border: none;
  border-radius: 3px;
  color: white;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  white-space: nowrap;
}

.api-key-submit:hover:not(:disabled) {
  background: var(--adobe-accent-hover);
}

.api-key-submit:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.api-key-error {
  font-size: 11px;
  color: var(--adobe-error);
  margin-top: 4px;
}

.api-key-help {
  font-size: 11px;
  color: var(--adobe-text-secondary);
  line-height: 1.4;
}

.api-key-link {
  color: var(--adobe-accent);
  text-decoration: none;
}

.api-key-link:hover {
  text-decoration: underline;
}

/* Model Selector Styles */
.model-selector {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.model-selector-loading,
.model-selector-error {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  color: var(--adobe-text-secondary);
  font-size: 12px;
}

.model-selector-error {
  color: var(--adobe-error);
}

.loading-icon {
  animation: spin 1s linear infinite;
}

.error-icon {
  color: var(--adobe-error);
}

.model-search-container {
  position: relative;
  margin-bottom: 8px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--adobe-text-secondary);
  pointer-events: none;
}

.model-search-input {
  width: 100%;
  padding: 8px 12px 8px 36px;
  background: var(--adobe-bg-primary);
  border: 1px solid var(--adobe-border);
  border-radius: 3px;
  color: var(--adobe-text-primary);
  font-size: 12px;
  outline: none;
  transition: border-color 0.2s ease;
}

.model-search-input:focus {
  border-color: var(--adobe-accent);
  box-shadow: 0 0 0 1px var(--adobe-accent);
}

.model-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
  text-align: left;
}

.model-name {
  font-weight: 500;
  font-size: 12px;
}

.model-description {
  font-size: 11px;
  opacity: 0.7;
  line-height: 1.3;
}

.model-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 2px;
}

.model-context {
  font-size: 10px;
  color: var(--adobe-text-secondary);
}

.model-capabilities {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.capability-tag {
  padding: 1px 4px;
  background: var(--adobe-bg-tertiary);
  border-radius: 2px;
  font-size: 9px;
  color: var(--adobe-text-primary);
}

.no-models {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  color: var(--adobe-text-secondary);
  font-size: 12px;
  justify-content: center;
}

/* Model Info View Styles */
.model-info-compact {
  background: rgba(70, 160, 245, 0.05);
  border: 1px solid rgba(70, 160, 245, 0.1);
  border-radius: 3px;
  padding: 8px 12px;
}

.model-info-detailed {
  background: rgba(70, 160, 245, 0.1);
  border: 1px solid rgba(70, 160, 245, 0.2);
  border-radius: 3px;
  padding: 12px;
}

.model-info-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.model-info-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--adobe-text-primary);
  margin: 0;
}

.model-info-compact .model-info-title {
  font-size: 11px;
}

.model-info-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  font-size: 10px;
  color: var(--adobe-text-secondary);
}

.info-item {
  white-space: nowrap;
}

.model-info-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.model-info-item {
  display: flex;
  gap: 8px;
  align-items: flex-start;
}

.model-info-pricing {
  display: flex;
  gap: 8px;
  align-items: flex-start;
}

.info-label {
  font-weight: 600;
  min-width: 80px;
  color: var(--adobe-text-primary);
  font-size: 11px;
}

.info-value {
  flex: 1;
  color: var(--adobe-text-secondary);
  font-size: 11px;
  line-height: 1.4;
}

.pricing-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.pricing-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 6px;
  background: var(--adobe-bg-tertiary);
  border-radius: 2px;
  font-size: 10px;
}

.pricing-type {
  color: var(--adobe-text-secondary);
}

.pricing-value {
  color: var(--adobe-text-primary);
  font-weight: 500;
}

.capabilities-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  flex: 1;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 400px) {
  .model-info-summary {
    flex-direction: column;
    gap: 4px;
  }
  
  .pricing-details {
    gap: 2px;
  }
  
  .capabilities-list {
    gap: 2px;
  }
}
`;
