/**
 * Model Information Display Component
 * Shows detailed information about the selected model
 */

import React from 'react';
import { ModelInfo } from '../../../stores/settingsStore';
import { InfoIcon } from '../../ui';

interface ModelInfoViewProps {
  model: ModelInfo;
  showPricing?: boolean;
  showCapabilities?: boolean;
  showDescription?: boolean;
  compact?: boolean;
}

export const ModelInfoView: React.FC<ModelInfoViewProps> = ({
  model,
  showPricing = true,
  showCapabilities = true,
  showDescription = true,
  compact = false
}) => {
  const formatCost = (cost: number | undefined) => {
    if (cost === undefined || cost === 0) return 'Free';
    if (cost < 0.001) return `$${(cost * 1000).toFixed(3)}/1K tokens`;
    return `$${cost.toFixed(3)}/1K tokens`;
  };

  const formatContextLength = (length: number | undefined) => {
    if (!length) return 'Unknown';
    if (length >= 1000000) return `${(length / 1000000).toFixed(1)}M tokens`;
    if (length >= 1000) return `${(length / 1000).toFixed(0)}K tokens`;
    return `${length} tokens`;
  };

  if (compact) {
    return (
      <div className="model-info-compact">
        <div className="model-info-header">
          <InfoIcon size={14} />
          <span className="model-info-title">{model.name}</span>
        </div>
        
        <div className="model-info-summary">
          {model.contextLength && (
            <span className="info-item">
              Context: {formatContextLength(model.contextLength)}
            </span>
          )}
          
          {showPricing && (model.inputCost || model.outputCost) && (
            <span className="info-item">
              Cost: {formatCost(model.inputCost)} / {formatCost(model.outputCost)}
            </span>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="model-info-detailed">
      <div className="model-info-header">
        <InfoIcon size={16} />
        <h4 className="model-info-title">Model Information</h4>
      </div>
      
      <div className="model-info-content">
        <div className="model-info-item">
          <span className="info-label">Name:</span>
          <span className="info-value">{model.name}</span>
        </div>
        
        {showDescription && model.description && (
          <div className="model-info-item">
            <span className="info-label">Description:</span>
            <span className="info-value">{model.description}</span>
          </div>
        )}
        
        {model.contextLength && (
          <div className="model-info-item">
            <span className="info-label">Context Length:</span>
            <span className="info-value">{formatContextLength(model.contextLength)}</span>
          </div>
        )}
        
        {showPricing && (model.inputCost !== undefined || model.outputCost !== undefined) && (
          <div className="model-info-pricing">
            <span className="info-label">Pricing:</span>
            <div className="pricing-details">
              {model.inputCost !== undefined && (
                <div className="pricing-item">
                  <span className="pricing-type">Input:</span>
                  <span className="pricing-value">{formatCost(model.inputCost)}</span>
                </div>
              )}
              {model.outputCost !== undefined && (
                <div className="pricing-item">
                  <span className="pricing-type">Output:</span>
                  <span className="pricing-value">{formatCost(model.outputCost)}</span>
                </div>
              )}
            </div>
          </div>
        )}
        
        {showCapabilities && model.capabilities && model.capabilities.length > 0 && (
          <div className="model-info-item">
            <span className="info-label">Capabilities:</span>
            <div className="capabilities-list">
              {model.capabilities.map((capability, index) => (
                <span key={index} className="capability-tag">
                  {capability}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// CSS styles for the component
export const modelInfoViewStyles = `
.model-info-compact {
  background: rgba(70, 160, 245, 0.05);
  border: 1px solid rgba(70, 160, 245, 0.1);
  border-radius: 3px;
  padding: 8px 12px;
}

.model-info-detailed {
  background: rgba(70, 160, 245, 0.1);
  border: 1px solid rgba(70, 160, 245, 0.2);
  border-radius: 3px;
  padding: 12px;
}

.model-info-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.model-info-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--adobe-text-primary);
  margin: 0;
}

.model-info-compact .model-info-title {
  font-size: 11px;
}

.model-info-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  font-size: 10px;
  color: var(--adobe-text-secondary);
}

.info-item {
  white-space: nowrap;
}

.model-info-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.model-info-item {
  display: flex;
  gap: 8px;
  align-items: flex-start;
}

.model-info-pricing {
  display: flex;
  gap: 8px;
  align-items: flex-start;
}

.info-label {
  font-weight: 600;
  min-width: 80px;
  color: var(--adobe-text-primary);
  font-size: 11px;
}

.info-value {
  flex: 1;
  color: var(--adobe-text-secondary);
  font-size: 11px;
  line-height: 1.4;
}

.pricing-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.pricing-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 6px;
  background: var(--adobe-bg-tertiary);
  border-radius: 2px;
  font-size: 10px;
}

.pricing-type {
  color: var(--adobe-text-secondary);
}

.pricing-value {
  color: var(--adobe-text-primary);
  font-weight: 500;
}

.capabilities-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  flex: 1;
}

.capability-tag {
  padding: 2px 6px;
  background: var(--adobe-bg-tertiary);
  border-radius: 2px;
  font-size: 10px;
  color: var(--adobe-text-primary);
  border: 1px solid var(--adobe-border);
}

/* Responsive adjustments */
@media (max-width: 400px) {
  .model-info-summary {
    flex-direction: column;
    gap: 4px;
  }
  
  .pricing-details {
    gap: 2px;
  }
  
  .capabilities-list {
    gap: 2px;
  }
}
`;
