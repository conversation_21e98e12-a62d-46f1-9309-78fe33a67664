/**
 * Provider Health Dashboard Component
 * Displays real-time health status for all configured providers
 */

import React, { useEffect, useState } from 'react';
import { useSettingsStore } from '../../../stores/settingsStore';
import { HealthStatus } from '../../../types/api';

interface ProviderHealthDashboardProps {
  className?: string;
}

export const ProviderHealthDashboard: React.FC<ProviderHealthDashboardProps> = ({
  className = '',
}) => {
  const {
    providers,
    healthStatuses,
    healthLoading,
    refreshProviderHealth,
  } = useSettingsStore();

  const [autoRefresh, setAutoRefresh] = useState(true);

  // Auto-refresh health status every 30 seconds
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      refreshProviderHealth();
    }, 30000);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshProviderHealth]);

  // Initial health check
  useEffect(() => {
    refreshProviderHealth();
  }, [refreshProviderHealth]);

  const getStatusColor = (status: HealthStatus['status']): string => {
    switch (status) {
      case 'healthy':
        return '#10B981'; // Green
      case 'degraded':
        return '#F59E0B'; // Yellow
      case 'down':
        return '#EF4444'; // Red
      default:
        return '#6B7280'; // Gray
    }
  };

  const getStatusIcon = (status: HealthStatus['status']): string => {
    switch (status) {
      case 'healthy':
        return '✅';
      case 'degraded':
        return '⚠️';
      case 'down':
        return '❌';
      default:
        return '❓';
    }
  };

  const formatLatency = (latency: number): string => {
    if (latency < 1000) return `${latency}ms`;
    return `${(latency / 1000).toFixed(1)}s`;
  };

  const formatUptime = (uptime: number): string => {
    return `${(uptime * 100).toFixed(1)}%`;
  };

  const configuredProviders = providers.filter(p => p.isConfigured);

  return (
    <div className={`provider-health-dashboard ${className}`}>
      <div className="dashboard-header">
        <h3>Provider Health Status</h3>
        <div className="dashboard-controls">
          <label className="auto-refresh-toggle">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
            />
            Auto-refresh
          </label>
          <button
            className="refresh-button"
            onClick={() => refreshProviderHealth()}
            disabled={healthLoading}
          >
            {healthLoading ? '🔄' : '🔃'} Refresh
          </button>
        </div>
      </div>

      {configuredProviders.length === 0 ? (
        <div className="no-providers">
          <p>No providers configured yet.</p>
          <p>Add API keys to start monitoring provider health.</p>
        </div>
      ) : (
        <div className="provider-grid">
          {configuredProviders.map((provider) => {
            const health = healthStatuses[provider.id];
            const hasHealth = !!health;

            return (
              <div key={provider.id} className="provider-card">
                <div className="provider-header">
                  <div className="provider-name">
                    <span className="provider-icon">
                      {getStatusIcon(health?.status || 'down')}
                    </span>
                    {provider.name}
                  </div>
                  <div
                    className="status-indicator"
                    style={{ backgroundColor: getStatusColor(health?.status || 'down') }}
                  />
                </div>

                {hasHealth ? (
                  <div className="health-details">
                    <div className="health-metric">
                      <span className="metric-label">Status:</span>
                      <span className="metric-value status">
                        {health.status.toUpperCase()}
                      </span>
                    </div>
                    
                    <div className="health-metric">
                      <span className="metric-label">Latency:</span>
                      <span className="metric-value">
                        {formatLatency(health.latency)}
                      </span>
                    </div>
                    
                    <div className="health-metric">
                      <span className="metric-label">Uptime:</span>
                      <span className="metric-value">
                        {formatUptime(health.uptime)}
                      </span>
                    </div>
                    
                    <div className="health-metric">
                      <span className="metric-label">Error Rate:</span>
                      <span className="metric-value">
                        {(health.errorRate * 100).toFixed(1)}%
                      </span>
                    </div>
                    
                    <div className="health-metric">
                      <span className="metric-label">Last Check:</span>
                      <span className="metric-value">
                        {health.lastCheck.toLocaleTimeString()}
                      </span>
                    </div>

                    {health.details?.errorMessage && (
                      <div className="error-message">
                        <span className="metric-label">Error:</span>
                        <span className="metric-value error">
                          {health.details.errorMessage}
                        </span>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="health-loading">
                    {healthLoading ? (
                      <span>Checking health...</span>
                    ) : (
                      <span>Health check pending</span>
                    )}
                  </div>
                )}

                <div className="provider-actions">
                  <button
                    className="test-button"
                    onClick={() => refreshProviderHealth(provider.id)}
                    disabled={healthLoading}
                  >
                    Test Connection
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      )}

      <style>{`
        .provider-health-dashboard {
          background: #f8fafc;
          border-radius: 8px;
          padding: 16px;
          margin: 16px 0;
        }

        .dashboard-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          padding-bottom: 12px;
          border-bottom: 1px solid #e2e8f0;
        }

        .dashboard-header h3 {
          margin: 0;
          color: #1e293b;
          font-size: 18px;
          font-weight: 600;
        }

        .dashboard-controls {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .auto-refresh-toggle {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 14px;
          color: #64748b;
          cursor: pointer;
        }

        .refresh-button {
          background: #3b82f6;
          color: white;
          border: none;
          border-radius: 6px;
          padding: 6px 12px;
          font-size: 14px;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .refresh-button:hover:not(:disabled) {
          background: #2563eb;
        }

        .refresh-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .no-providers {
          text-align: center;
          padding: 32px;
          color: #64748b;
        }

        .provider-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
          gap: 16px;
        }

        .provider-card {
          background: white;
          border: 1px solid #e2e8f0;
          border-radius: 8px;
          padding: 16px;
          transition: box-shadow 0.2s;
        }

        .provider-card:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .provider-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
        }

        .provider-name {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #1e293b;
        }

        .provider-icon {
          font-size: 16px;
        }

        .status-indicator {
          width: 12px;
          height: 12px;
          border-radius: 50%;
        }

        .health-details {
          display: flex;
          flex-direction: column;
          gap: 8px;
          margin-bottom: 12px;
        }

        .health-metric {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 14px;
        }

        .metric-label {
          color: #64748b;
          font-weight: 500;
        }

        .metric-value {
          color: #1e293b;
          font-weight: 600;
        }

        .metric-value.status {
          text-transform: uppercase;
          font-size: 12px;
          letter-spacing: 0.5px;
        }

        .metric-value.error {
          color: #ef4444;
          font-size: 12px;
          max-width: 200px;
          text-align: right;
          word-break: break-word;
        }

        .error-message {
          background: #fef2f2;
          border: 1px solid #fecaca;
          border-radius: 4px;
          padding: 8px;
          margin-top: 8px;
        }

        .health-loading {
          text-align: center;
          padding: 20px;
          color: #64748b;
          font-style: italic;
        }

        .provider-actions {
          display: flex;
          justify-content: flex-end;
          margin-top: 12px;
          padding-top: 12px;
          border-top: 1px solid #f1f5f9;
        }

        .test-button {
          background: #10b981;
          color: white;
          border: none;
          border-radius: 4px;
          padding: 6px 12px;
          font-size: 12px;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .test-button:hover:not(:disabled) {
          background: #059669;
        }

        .test-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      `}</style>
    </div>
  );
};
