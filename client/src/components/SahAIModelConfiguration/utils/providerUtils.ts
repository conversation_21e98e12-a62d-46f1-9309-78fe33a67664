/**
 * Provider Utilities for SahAI V2
 * Adapted from Cline's provider utilities for centralized provider/model logic
 */

import { ProviderConfig, ModelInfo } from '../../../stores/settingsStore';

// Provider type definitions
export type ApiProvider = 
  | 'openai'
  | 'anthropic'
  | 'gemini'
  | 'deepseek'
  | 'xai'
  | 'mistral'
  | 'qwen'
  | 'vertex'
  | 'openrouter'
  | 'perplexity'
  | 'together'
  | 'groq'
  | 'moonshot'
  | 'ollama'
  | 'lmstudio';

// Configuration interface for normalized provider data
export interface NormalizedApiConfiguration {
  selectedProvider: ApiProvider | null;
  selectedModelId: string;
  selectedModelInfo: ModelInfo | null;
}

// Default model information for fallback scenarios
export const defaultModelInfo: ModelInfo = {
  id: 'default',
  name: 'Default Model',
  description: 'Default model configuration',
  contextLength: 4096,
  inputCost: 0,
  outputCost: 0,
  capabilities: ['text']
};

// Provider metadata for UI display
export const providerMetadata: Record<ApiProvider, {
  name: string;
  description: string;
  website: string;
  category: 'online' | 'offline';
  requiresApiKey: boolean;
  defaultModel?: string;
}> = {
  // Online Providers
  openai: {
    name: 'OpenAI',
    description: 'OpenAI\'s GPT models including GPT-4 and GPT-3.5',
    website: 'https://platform.openai.com/api-keys',
    category: 'online',
    requiresApiKey: true,
    defaultModel: 'gpt-4'
  },
  anthropic: {
    name: 'Anthropic',
    description: 'Anthropic\'s Claude models for advanced reasoning',
    website: 'https://console.anthropic.com/',
    category: 'online',
    requiresApiKey: true,
    defaultModel: 'claude-3-sonnet-20240229'
  },
  gemini: {
    name: 'Google Gemini',
    description: 'Google\'s Gemini models for multimodal AI',
    website: 'https://makersuite.google.com/app/apikey',
    category: 'online',
    requiresApiKey: true,
    defaultModel: 'gemini-pro'
  },
  deepseek: {
    name: 'DeepSeek',
    description: 'DeepSeek\'s advanced reasoning models',
    website: 'https://platform.deepseek.com/',
    category: 'online',
    requiresApiKey: true,
    defaultModel: 'deepseek-chat'
  },
  xai: {
    name: 'xAI (Grok)',
    description: 'xAI\'s Grok models with real-time information',
    website: 'https://console.x.ai/',
    category: 'online',
    requiresApiKey: true,
    defaultModel: 'grok-beta'
  },
  mistral: {
    name: 'Mistral AI',
    description: 'Mistral\'s efficient and powerful language models',
    website: 'https://console.mistral.ai/',
    category: 'online',
    requiresApiKey: true,
    defaultModel: 'mistral-large-latest'
  },
  qwen: {
    name: 'Qwen',
    description: 'Alibaba\'s Qwen series models',
    website: 'https://dashscope.aliyun.com/',
    category: 'online',
    requiresApiKey: true,
    defaultModel: 'qwen-turbo'
  },
  vertex: {
    name: 'Google Vertex AI',
    description: 'Google Cloud\'s Vertex AI platform',
    website: 'https://console.cloud.google.com/vertex-ai',
    category: 'online',
    requiresApiKey: true,
    defaultModel: 'gemini-pro'
  },
  openrouter: {
    name: 'OpenRouter',
    description: 'Access to multiple AI models through one API',
    website: 'https://openrouter.ai/keys',
    category: 'online',
    requiresApiKey: true,
    defaultModel: 'openai/gpt-4'
  },
  perplexity: {
    name: 'Perplexity',
    description: 'Perplexity\'s search-augmented language models',
    website: 'https://www.perplexity.ai/settings/api',
    category: 'online',
    requiresApiKey: true,
    defaultModel: 'llama-3.1-sonar-small-128k-online'
  },
  together: {
    name: 'Together AI',
    description: 'Together AI\'s platform for open-source models',
    website: 'https://api.together.xyz/settings/api-keys',
    category: 'online',
    requiresApiKey: true,
    defaultModel: 'meta-llama/Llama-2-7b-chat-hf'
  },
  groq: {
    name: 'Groq',
    description: 'Ultra-fast inference with Groq\'s LPU technology',
    website: 'https://console.groq.com/keys',
    category: 'online',
    requiresApiKey: true,
    defaultModel: 'llama3-8b-8192'
  },
  moonshot: {
    name: 'Moonshot AI',
    description: 'Moonshot\'s Kimi models with long context',
    website: 'https://platform.moonshot.cn/',
    category: 'online',
    requiresApiKey: true,
    defaultModel: 'moonshot-v1-8k'
  },
  
  // Offline Providers
  ollama: {
    name: 'Ollama',
    description: 'Run large language models locally with Ollama',
    website: 'https://ollama.ai/',
    category: 'offline',
    requiresApiKey: false,
    defaultModel: 'llama2'
  },
  lmstudio: {
    name: 'LM Studio',
    description: 'Local model hosting with LM Studio',
    website: 'https://lmstudio.ai/',
    category: 'offline',
    requiresApiKey: false,
    defaultModel: 'local-model'
  }
};

/**
 * Normalizes API configuration to provide consistent interface
 * Similar to Cline's normalizeApiConfiguration function
 */
export function normalizeApiConfiguration(
  currentProvider: ProviderConfig | null,
  currentModel: ModelInfo | null,
  availableModels: ModelInfo[]
): NormalizedApiConfiguration {
  if (!currentProvider) {
    return {
      selectedProvider: null,
      selectedModelId: '',
      selectedModelInfo: null
    };
  }

  const providerId = currentProvider.id as ApiProvider;
  const metadata = providerMetadata[providerId];
  
  // Determine selected model
  let selectedModelId = '';
  let selectedModelInfo: ModelInfo | null = null;

  if (currentModel) {
    selectedModelId = currentModel.id;
    selectedModelInfo = currentModel;
  } else if (availableModels.length > 0) {
    // Use first available model as fallback
    selectedModelInfo = availableModels[0];
    selectedModelId = selectedModelInfo.id;
  } else if (metadata?.defaultModel) {
    // Use provider's default model
    selectedModelId = metadata.defaultModel;
    selectedModelInfo = {
      ...defaultModelInfo,
      id: metadata.defaultModel,
      name: metadata.defaultModel
    };
  }

  return {
    selectedProvider: providerId,
    selectedModelId,
    selectedModelInfo
  };
}

/**
 * Get provider metadata by ID
 */
export function getProviderMetadata(providerId: string) {
  return providerMetadata[providerId as ApiProvider];
}

/**
 * Check if provider is online or offline
 */
export function isOnlineProvider(providerId: string): boolean {
  const metadata = getProviderMetadata(providerId);
  return metadata?.category === 'online';
}

/**
 * Check if provider requires API key
 */
export function requiresApiKey(providerId: string): boolean {
  const metadata = getProviderMetadata(providerId);
  return metadata?.requiresApiKey ?? true;
}

/**
 * Get all online provider IDs
 */
export function getOnlineProviders(): ApiProvider[] {
  return Object.keys(providerMetadata).filter(
    id => providerMetadata[id as ApiProvider].category === 'online'
  ) as ApiProvider[];
}

/**
 * Get all offline provider IDs
 */
export function getOfflineProviders(): ApiProvider[] {
  return Object.keys(providerMetadata).filter(
    id => providerMetadata[id as ApiProvider].category === 'offline'
  ) as ApiProvider[];
}
