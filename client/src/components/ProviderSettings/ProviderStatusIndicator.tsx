/**
 * Provider Status Indicator Component for SahAI CEP Extension V2
 * Shows the current status of the selected AI provider as a clickable dot
 * Clicking opens the health modal
 * Now uses the generic StatusIndicator component
 */

import React from 'react';
import { useSettingsStore } from '../../stores/settingsStore';
import { modalActions } from '../../stores/modalStore';
import { StatusIndicator, StatusSize } from '../ui/StatusIndicator';

interface ProviderStatusIndicatorProps {
  size?: StatusSize;
  showLabel?: boolean;
  className?: string;
}

export const ProviderStatusIndicator: React.FC<ProviderStatusIndicatorProps> = ({
  size = 'medium',
  showLabel = false,
  className = '',
}) => {
  const { currentProvider } = useSettingsStore();

  // Simplified status logic - only Green (ready) or Red (not ready)
  const getSimplifiedStatus = () => {
    if (!currentProvider || !currentProvider.isConfigured || !currentProvider.isOnline) {
      return {
        type: 'error' as const,
        label: 'Not Ready',
        description: 'Model is not ready to interact'
      };
    }

    return {
      type: 'success' as const,
      label: 'Ready',
      description: 'Model is online and ready to interact'
    };
  };

  const status = getSimplifiedStatus();

  // Handle click to open health modal
  const handleClick = () => {
    modalActions.showProviderHealth();
  };

  return (
    <StatusIndicator
      status={status}
      size={size}
      variant="dot"
      showLabel={showLabel}
      onClick={handleClick}
      className={className}
    />
  );
};
