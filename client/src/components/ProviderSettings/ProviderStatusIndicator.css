/* Provider Status Indicator Styles for SahAI CEP Extension V2 - Dot Style */

.provider-status-dot {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  background: transparent;
  border: none;
  padding: 2px;
  cursor: pointer;
  font-size: 11px;
  font-family: var(--adobe-font-family);
  transition: all 0.2s ease;
  outline: none;
}

/* Size variants */
.provider-status-dot.small {
  gap: 3px;
  padding: 1px;
  font-size: 10px;
}

.provider-status-dot.medium {
  gap: 4px;
  padding: 2px;
  font-size: 11px;
}

.provider-status-dot.large {
  gap: 5px;
  padding: 3px;
  font-size: 12px;
}

/* Status dot */
.status-dot {
  border-radius: 50%;
  flex-shrink: 0;
  transition: all 0.2s ease;
}

/* Status labels */
.status-label {
  font-weight: 500;
  white-space: nowrap;
  color: var(--adobe-text-primary);
}

/* Status type styles - Dot colors */
.provider-status-dot.success .status-dot {
  background: #4CAF50; /* Green dot for success */
  box-shadow: 0 0 0 1px rgba(76, 175, 80, 0.3);
}

.provider-status-dot.warning .status-dot {
  background: #FF9800; /* Orange dot for warning */
  box-shadow: 0 0 0 1px rgba(255, 152, 0, 0.3);
}

.provider-status-dot.error .status-dot {
  background: #F44336; /* Red dot for error */
  box-shadow: 0 0 0 1px rgba(244, 67, 54, 0.3);
}

.provider-status-dot.none .status-dot {
  background: #6c757d; /* Gray dot for none */
  box-shadow: 0 0 0 1px rgba(108, 117, 125, 0.3);
}

/* Hover effects */
.provider-status-dot:hover .status-dot {
  transform: scale(1.2);
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

.provider-status-dot:hover.success .status-dot {
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.5);
}

.provider-status-dot:hover.warning .status-dot {
  box-shadow: 0 0 0 2px rgba(255, 152, 0, 0.5);
}

.provider-status-dot:hover.error .status-dot {
  box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.5);
}

.provider-status-dot:hover.none .status-dot {
  box-shadow: 0 0 0 2px rgba(108, 117, 125, 0.5);
}

/* Adobe theme variables automatically handle light/dark themes */

/* Animation for status changes */
@keyframes status-change {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.3);
  }
  100% {
    transform: scale(1);
  }
}

.provider-status-dot.status-changed .status-dot {
  animation: status-change 0.3s ease-in-out;
}

/* Accessibility improvements */
.provider-status-dot:focus {
  outline: 2px solid var(--adobe-accent);
  outline-offset: 2px;
  border-radius: 50%;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .provider-status-dot.success .status-dot {
    background: #28a745;
    border: 2px solid white;
  }

  .provider-status-dot.warning .status-dot {
    background: #ffc107;
    border: 2px solid black;
  }

  .provider-status-dot.error .status-dot {
    background: #dc3545;
    border: 2px solid white;
  }

  .provider-status-dot.none .status-dot {
    background: #6c757d;
    border: 2px solid white;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .provider-status-dot {
    transition: none;
  }

  .provider-status-dot:hover .status-dot {
    transform: none;
  }

  .status-changed .status-dot {
    animation: none;
  }
}
