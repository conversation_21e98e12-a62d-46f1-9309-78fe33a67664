/**
 * Code execution utilities for the message composer
 * Handles running code blocks in terminal or appropriate environments
 */

export interface CodeExecutionResult {
  success: boolean;
  output?: string;
  error?: string;
  executionTime?: number;
}

export interface CodeExecutionOptions {
  language: string;
  code: string;
  filename?: string;
  workingDirectory?: string;
}

/**
 * Execute code based on language type
 */
export async function executeCode(options: CodeExecutionOptions): Promise<CodeExecutionResult> {
  const { language, code, filename } = options;
  
  try {
    switch (language.toLowerCase()) {
      case 'javascript':
      case 'js':
        return await executeJavaScript(code);
      
      case 'typescript':
      case 'ts':
        return await executeTypeScript(code);
      
      case 'python':
      case 'py':
        return await executePython(code, filename);
      
      case 'bash':
      case 'shell':
      case 'sh':
        return await executeBash(code);
      
      case 'powershell':
      case 'ps1':
        return await executePowerShell(code);
      
      default:
        return {
          success: false,
          error: `Execution not supported for language: ${language}`
        };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown execution error'
    };
  }
}

/**
 * Execute JavaScript code in a safe context
 */
async function executeJavaScript(code: string): Promise<CodeExecutionResult> {
  const startTime = Date.now();
  
  try {
    // Create a safe execution context
    const result = new Function(`
      const console = {
        log: (...args) => args.join(' '),
        error: (...args) => args.join(' '),
        warn: (...args) => args.join(' '),
        info: (...args) => args.join(' ')
      };
      
      ${code}
    `)();
    
    return {
      success: true,
      output: String(result || ''),
      executionTime: Date.now() - startTime
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'JavaScript execution error',
      executionTime: Date.now() - startTime
    };
  }
}

/**
 * Execute TypeScript code (compile to JS first)
 */
async function executeTypeScript(code: string): Promise<CodeExecutionResult> {
  // For now, treat as JavaScript
  // In a full implementation, you'd compile TS to JS first
  return executeJavaScript(code);
}

/**
 * Execute Python code via CEP ExtendScript bridge
 */
async function executePython(code: string, filename?: string): Promise<CodeExecutionResult> {
  const startTime = Date.now();
  
  try {
    // Check if we're in CEP environment
    if (typeof window !== 'undefined' && (window as any).CSInterface) {
      const csInterface = new (window as any).CSInterface();
      
      // Create ExtendScript to execute Python
      const extendScript = `
        var file = new File("${filename || 'temp.py'}");
        file.open("w");
        file.write("${code.replace(/"/g, '\\"').replace(/\n/g, '\\n')}");
        file.close();
        
        var result = system.callSystem("python \\"" + file.fsName + "\\"");
        file.remove();
        result;
      `;
      
      return new Promise((resolve) => {
        csInterface.evalScript(extendScript, (result: string) => {
          resolve({
            success: true,
            output: result,
            executionTime: Date.now() - startTime
          });
        });
      });
    } else {
      return {
        success: false,
        error: 'Python execution requires CEP environment',
        executionTime: Date.now() - startTime
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Python execution error',
      executionTime: Date.now() - startTime
    };
  }
}

/**
 * Execute Bash commands via CEP ExtendScript bridge
 */
async function executeBash(code: string): Promise<CodeExecutionResult> {
  const startTime = Date.now();
  
  try {
    if (typeof window !== 'undefined' && (window as any).CSInterface) {
      const csInterface = new (window as any).CSInterface();
      
      const extendScript = `
        system.callSystem("${code.replace(/"/g, '\\"')}");
      `;
      
      return new Promise((resolve) => {
        csInterface.evalScript(extendScript, (result: string) => {
          resolve({
            success: true,
            output: result,
            executionTime: Date.now() - startTime
          });
        });
      });
    } else {
      return {
        success: false,
        error: 'Bash execution requires CEP environment',
        executionTime: Date.now() - startTime
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Bash execution error',
      executionTime: Date.now() - startTime
    };
  }
}

/**
 * Execute PowerShell commands via CEP ExtendScript bridge
 */
async function executePowerShell(code: string): Promise<CodeExecutionResult> {
  const startTime = Date.now();
  
  try {
    if (typeof window !== 'undefined' && (window as any).CSInterface) {
      const csInterface = new (window as any).CSInterface();
      
      const extendScript = `
        system.callSystem("powershell -Command \\"${code.replace(/"/g, '\\"')}\\"");
      `;
      
      return new Promise((resolve) => {
        csInterface.evalScript(extendScript, (result: string) => {
          resolve({
            success: true,
            output: result,
            executionTime: Date.now() - startTime
          });
        });
      });
    } else {
      return {
        success: false,
        error: 'PowerShell execution requires CEP environment',
        executionTime: Date.now() - startTime
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'PowerShell execution error',
      executionTime: Date.now() - startTime
    };
  }
}

/**
 * Get supported languages for code execution
 */
export function getSupportedLanguages(): string[] {
  return [
    'javascript',
    'js',
    'typescript',
    'ts',
    'python',
    'py',
    'bash',
    'shell',
    'sh',
    'powershell',
    'ps1'
  ];
}

/**
 * Check if a language is supported for execution
 */
export function isLanguageSupported(language: string): boolean {
  return getSupportedLanguages().includes(language.toLowerCase());
}

/**
 * Format execution result for display
 */
export function formatExecutionResult(result: CodeExecutionResult): string {
  if (result.success) {
    let output = `✅ Execution completed`;
    if (result.executionTime) {
      output += ` in ${result.executionTime}ms`;
    }
    if (result.output) {
      output += `\n\nOutput:\n${result.output}`;
    }
    return output;
  } else {
    let output = `❌ Execution failed`;
    if (result.executionTime) {
      output += ` after ${result.executionTime}ms`;
    }
    if (result.error) {
      output += `\n\nError:\n${result.error}`;
    }
    return output;
  }
}
