/**
 * ID Generation Utilities for SahAI CEP Extension
 * Provides unique ID generation for various entities
 */

/**
 * Generate a unique ID using timestamp and random string
 * Format: timestamp_randomString
 */
export function generateId(): string {
  const timestamp = Date.now().toString(36);
  const randomPart = Math.random().toString(36).substring(2, 8);
  return `${timestamp}_${randomPart}`;
}

/**
 * Generate a short ID (8 characters)
 * Useful for UI elements that need shorter identifiers
 */
export function generateShortId(): string {
  return Math.random().toString(36).substring(2, 10);
}

/**
 * Generate a UUID-like string
 * Format: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Generate a prefixed ID
 * @param prefix - The prefix to add to the ID
 * @param separator - The separator between prefix and ID (default: '_')
 */
export function generatePrefixedId(prefix: string, separator: string = '_'): string {
  return `${prefix}${separator}${generateId()}`;
}

/**
 * Generate a session ID specifically for chat sessions
 */
export function generateSessionId(): string {
  return generatePrefixedId('session');
}

/**
 * Generate a message ID specifically for chat messages
 */
export function generateMessageId(): string {
  return generatePrefixedId('msg');
}

/**
 * Generate a provider ID specifically for AI providers
 */
export function generateProviderId(): string {
  return generatePrefixedId('provider');
}

/**
 * Validate if a string is a valid generated ID
 * @param id - The ID to validate
 */
export function isValidId(id: string): boolean {
  if (!id || typeof id !== 'string') {
    return false;
  }
  
  // Check for basic ID format (contains underscore and alphanumeric characters)
  const basicIdPattern = /^[a-zA-Z0-9_-]+$/;
  return basicIdPattern.test(id) && id.length >= 8;
}

/**
 * Extract timestamp from a generated ID
 * @param id - The ID to extract timestamp from
 */
export function extractTimestampFromId(id: string): number | null {
  try {
    const parts = id.split('_');
    if (parts.length >= 2) {
      const timestamp = parseInt(parts[0], 36);
      return isNaN(timestamp) ? null : timestamp;
    }
    return null;
  } catch {
    return null;
  }
}

/**
 * Generate a deterministic ID based on input string
 * Useful for creating consistent IDs for the same input
 * @param input - The input string to generate ID from
 */
export function generateDeterministicId(input: string): string {
  let hash = 0;
  for (let i = 0; i < input.length; i++) {
    const char = input.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(36);
}
