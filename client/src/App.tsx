/**
 * Main App Component for SahAI CEP Extension V2
 * Root component that renders the TopBar and manages global state
 */

import React, { useEffect } from 'react';
import TopBar from './features/TopBar/TopBar';
import ChatMessages from './features/ChatMessages/ChatMessages';
import InputArea from './features/InputArea/InputArea';
import { UnifiedModal } from './components/ui/Modal';
import { useSettingsStore } from './stores/settingsStore';
import { useChatStore } from './stores/chatStore';
import { useModalStore } from './stores/modalStore';
import { useTheme } from './hooks/useTheme';
import './App.css';

const App: React.FC = () => {
  const { initializeDefaultProviders } = useSettingsStore();
  const { initializeTheme, isThemeReady } = useTheme();
  const { createNewSession } = useChatStore();
  const { isOpen: isModalOpen, closeModal } = useModalStore();



  // Initialize the application
  useEffect(() => {
    const initializeApp = async () => {
      try {
        console.log('🚀 SahAI V2 App - Initializing...');

        // Initialize Adobe theme detection first
        await initializeTheme();

        // Initialize default providers
        initializeDefaultProviders();

        console.log('✅ SahAI V2 App - Ready');
      } catch (error) {
        console.error('❌ SahAI V2: Failed to initialize application:', error);
      }
    };

    initializeApp();
  }, [initializeTheme, initializeDefaultProviders]);

  const handleNewChat = () => {
    console.log('📝 New chat requested');
    createNewSession();
  };

  return (
    <div className="sahai-app">
      <div className="app-container">
        {/* TopBar with Status Indicator → Clickable Link → New Chat → History → Settings */}
        <TopBar onNewChat={handleNewChat} />

        {/* Main content area - Chat interface */}
        <div className="main-content">
          <ChatMessages />
        </div>

        {/* InputArea - Enhanced with file attachment, voice input, and context references */}
        <InputArea
          onAttachFile={() => {
            console.log('📎 File attachment initiated');
            // File attachment is now implemented in InputArea component
          }}
          onVoiceInput={() => {
            console.log('🎤 Voice input initiated');
            // Voice input is now implemented in InputArea component
          }}
          onContextReference={() => {
            console.log('@ Context reference initiated');
            // Context reference is now implemented in InputArea component
          }}
        />
      </div>

      {/* UnifiedModal - Consolidated modal system supporting both centered and slide-in variants */}
      <UnifiedModal
        isOpen={isModalOpen}
        onClose={closeModal}
      />
    </div>
  );
};

export default App;
