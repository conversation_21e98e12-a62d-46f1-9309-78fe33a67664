/**
 * useTheme Hook for SahAI CEP Extension V2
 * Manages Adobe theme detection and application following Adobe CEP Standards
 *
 * This implementation follows Adobe's official CEP documentation:
 * - Uses CSInterface.getHostEnvironment() to get AppSkinInfo
 * - Accesses panelBackgroundColor as UIColor object with nested RGBColor
 * - Listens to CSInterface.THEME_COLOR_CHANGED_EVENT for theme changes
 * - Calculates brightness using standard luminance formula (0.299*R + 0.587*G + 0.114*B)
 * - Applies theme via data-theme attribute on document.body
 *
 * Reference: Adobe CEP CSInterface.js documentation
 */

import { useState, useEffect, useCallback } from 'react';

declare global {
  interface Window {
    __adobe_cep__?: any;
    CSInterface?: new () => CSInterface;
  }
}

interface RGBColor {
  red: number;
  green: number;
  blue: number;
  alpha?: number;
}

interface UIColor {
  type: number;
  antialiasLevel: number;
  color: RGBColor;
}

interface AppSkinInfo {
  baseFontFamily: string;
  baseFontSize: number;
  appBarBackgroundColor: UIColor;
  panelBackgroundColor: UIColor;
  appBarBackgroundColorSRGB: UIColor;
  panelBackgroundColorSRGB: UIColor;
  systemHighlightColor: RGBColor;
}

interface HostEnvironment {
  appName: string;
  appVersion: string;
  appLocale: string;
  appUILocale: string;
  appId: string;
  isAppOnline: boolean;
  appSkinInfo: AppSkinInfo;
}

interface CSInterface {
  getHostEnvironment(): HostEnvironment;
  addEventListener(event: string, callback: (event: any) => void): void;
  removeEventListener(event: string, callback: (event: any) => void): void;
  THEME_COLOR_CHANGED_EVENT: string;
}

export interface UseThemeReturn {
  currentTheme: string;
  isThemeReady: boolean;

  // Actions
  initializeTheme: () => void;
  refreshTheme: () => void;
}

export const useTheme = (): UseThemeReturn => {
  const [currentTheme, setCurrentTheme] = useState<string>('dark');
  const [isThemeReady, setIsThemeReady] = useState<boolean>(false);

  // Apply theme based on Adobe host brightness - Following Adobe CEP Standard
  const applyTheme = useCallback(() => {
    console.log('🎨 SahAI V2: Applying Adobe theme using CEP standard...');

    if (typeof window.__adobe_cep__ !== 'undefined' && window.CSInterface) {
      try {
        const csInterface = new window.CSInterface();
        const hostEnv = csInterface.getHostEnvironment();
        const appSkinInfo = hostEnv?.appSkinInfo;

        console.log('🔍 SahAI V2: AppSkinInfo structure:', appSkinInfo);

        if (appSkinInfo?.panelBackgroundColor) {
          // According to Adobe CEP standard: panelBackgroundColor is a UIColor object
          // UIColor.color contains the RGBColor with red, green, blue values (0-255)
          const panelBgColor = appSkinInfo.panelBackgroundColor;
          console.log('🎨 SahAI V2: Panel background color object:', panelBgColor);

          if (panelBgColor.color) {
            const rgbColor = panelBgColor.color;
            console.log('🎨 SahAI V2: RGB color values:', rgbColor);

            // Calculate brightness using standard luminance formula
            // Adobe CEP uses 0-255 range for RGB values
            const brightness = (rgbColor.red * 0.299 + rgbColor.green * 0.587 + rgbColor.blue * 0.114);
            console.log('🎨 SahAI V2: Calculated brightness:', brightness, '(threshold: 127.5)');

            if (brightness < 127.5) {
              // Dark theme
              document.body.setAttribute('data-theme', 'dark');
              setCurrentTheme('dark');
              console.log('✅ SahAI V2: Applied dark theme (brightness:', brightness, ')');
            } else {
              // Light theme
              document.body.removeAttribute('data-theme');
              setCurrentTheme('light');
              console.log('✅ SahAI V2: Applied light theme (brightness:', brightness, ')');
            }
          } else {
            console.warn('⚠️ SahAI V2: No color property in panelBackgroundColor, using dark theme');
            document.body.setAttribute('data-theme', 'dark');
            setCurrentTheme('dark');
          }
        } else {
          console.warn('⚠️ SahAI V2: No panelBackgroundColor in appSkinInfo, using dark theme');
          document.body.setAttribute('data-theme', 'dark');
          setCurrentTheme('dark');
        }
      } catch (error) {
        console.error('❌ SahAI V2: Failed to apply Adobe theme:', error);
        document.body.setAttribute('data-theme', 'dark');
        setCurrentTheme('dark');
      }
    } else {
      console.warn('⚠️ SahAI V2: CSInterface not available, using dark theme for development');
      document.body.setAttribute('data-theme', 'dark');
      setCurrentTheme('dark');
    }
  }, []);

  // Initialize theme detection
  const initializeTheme = useCallback(async () => {
    console.log('🎨 SahAI V2: Initializing Adobe theme detection...');

    try {
      // Apply initial theme
      applyTheme();
      setIsThemeReady(true);
      console.log('✅ SahAI V2: Theme initialization complete');
    } catch (error) {
      console.error('❌ SahAI V2: Theme initialization failed:', error);
      // Fallback to dark theme
      document.body.setAttribute('data-theme', 'dark');
      setCurrentTheme('dark');
      setIsThemeReady(true);
    }
  }, [applyTheme]);

  // Refresh theme (re-detect from host)
  const refreshTheme = useCallback(() => {
    console.log('🔄 SahAI V2: Refreshing theme...');
    applyTheme();
  }, [applyTheme]);

  // Setup theme change listener using Adobe CEP standard
  useEffect(() => {
    if (typeof window.__adobe_cep__ !== 'undefined' && window.CSInterface) {
      const csInterface = new window.CSInterface();

      // Listen for Adobe theme changes using the official CSInterface constant
      const handleThemeChange = (event: any) => {
        console.log('🎨 SahAI V2: Adobe theme change event received:', event);
        console.log('🔄 SahAI V2: Re-applying theme after Adobe host theme change...');
        applyTheme();
      };

      // Use the official Adobe CEP constant for theme change events
      const themeChangeEvent = (csInterface as any).THEME_COLOR_CHANGED_EVENT || 'com.adobe.csxs.events.ThemeColorChanged';
      csInterface.addEventListener(themeChangeEvent, handleThemeChange);
      console.log('✅ SahAI V2: Adobe theme change listener setup with event:', themeChangeEvent);

      return () => {
        csInterface.removeEventListener(themeChangeEvent, handleThemeChange);
        console.log('🧹 SahAI V2: Adobe theme change listener cleaned up');
      };
    } else {
      console.warn('⚠️ SahAI V2: CSInterface not available for theme change listening');
    }
  }, [applyTheme]);

  // Auto-initialize theme on mount
  useEffect(() => {
    if (!isThemeReady) {
      initializeTheme();
    }
  }, [initializeTheme, isThemeReady]);

  return {
    currentTheme,
    isThemeReady,
    initializeTheme,
    refreshTheme,
  };
};
