/* Global Styles for SahAI CEP Extension V2 */
/* Following Adobe CEP Theme Standards from V1 */

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-family: var(--adobe-font-family, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
  font-size: var(--adobe-font-size-base, 14px);
  line-height: 1.4;
  background: var(--adobe-bg-primary);
  color: var(--adobe-text-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Adobe CEP Theme Variables */
:root {
  /* Typography Scale */
  --adobe-font-size-xs: 12px;
  --adobe-font-size-sm: 12px;
  --adobe-font-size-base: 14px;
  --adobe-font-size-md: 14px;
  --adobe-font-size-lg: 16px;
  --adobe-font-size-xl: 18px;
  --adobe-font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

  /* Semantic colors */
  --adobe-color-success: #4caf50;
  --adobe-color-warning: #ff9800;
  --adobe-color-error: #f44336;
  --adobe-shadow: rgba(0, 0, 0, 0.3);
}

/* Light Theme - Applied when data-theme is NOT set (Adobe light theme) */
body:not([data-theme]) {
  --adobe-bg-primary: #f5f5f5;
  --adobe-bg-secondary: #e5e5e5;
  --adobe-bg-tertiary: #d5d5d5;
  --adobe-text-primary: #000000;
  --adobe-text-secondary: #666666;
  --adobe-text-muted: #999999;
  --adobe-border: #cccccc;
  --adobe-accent: #0066cc;
  --adobe-accent-hover: #0052a3;
  --adobe-success: #4caf50;
  --adobe-warning: #ff9800;
  --adobe-error: #f44336;
  --adobe-shadow: rgba(0, 0, 0, 0.1);
}

/* Dark Theme - Applied when data-theme="dark" (Adobe dark theme) */
body[data-theme="dark"] {
  --adobe-bg-primary: #2a2a2a;
  --adobe-bg-secondary: #323232;
  --adobe-bg-tertiary: #3a3a3a;
  --adobe-text-primary: #ffffff;
  --adobe-text-secondary: #cccccc;
  --adobe-text-muted: #999999;
  --adobe-border: #555555;
  --adobe-accent: #46a0f5;
  --adobe-accent-hover: #5ba7f7;
  --adobe-success: #4caf50;
  --adobe-warning: #ff9800;
  --adobe-error: #f44336;
  --adobe-shadow: rgba(0, 0, 0, 0.3);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin: 0;
}

h1 { font-size: 24px; }
h2 { font-size: 20px; }
h3 { font-size: 16px; }
h4 { font-size: 14px; }
h5 { font-size: 12px; }
h6 { font-size: 11px; }

p {
  margin: 0;
  line-height: 1.4;
}

/* Links */
a {
  color: var(--adobe-accent);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--adobe-accent-hover);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--adobe-accent);
  outline-offset: 1px;
}

/* Form elements */
input, textarea, select, button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

input, textarea, select {
  background: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  color: var(--adobe-text-primary);
  border-radius: 3px;
  padding: 6px 8px;
  outline: none;
  transition: border-color 0.2s ease;
}

input:focus, textarea:focus, select:focus {
  border-color: var(--adobe-accent);
  box-shadow: 0 0 0 1px var(--adobe-accent);
}

input:disabled, textarea:disabled, select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

button {
  background: var(--adobe-accent);
  border: none;
  color: white;
  border-radius: 3px;
  padding: 6px 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  outline: none;
}

button:hover:not(:disabled) {
  background: var(--adobe-accent-hover);
}

button:focus {
  box-shadow: 0 0 0 2px var(--adobe-accent);
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Utility classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.p-0 { padding: 0; }
.p-1 { padding: 4px; }
.p-2 { padding: 8px; }
.p-3 { padding: 12px; }
.p-4 { padding: 16px; }

.m-0 { margin: 0; }
.m-1 { margin: 4px; }
.m-2 { margin: 8px; }
.m-3 { margin: 12px; }
.m-4 { margin: 16px; }

.mb-1 { margin-bottom: 4px; }
.mb-2 { margin-bottom: 8px; }
.mb-3 { margin-bottom: 12px; }
.mb-4 { margin-bottom: 16px; }

.mt-1 { margin-top: 4px; }
.mt-2 { margin-top: 8px; }
.mt-3 { margin-top: 12px; }
.mt-4 { margin-top: 16px; }

/* Adobe CEP-specific styles */
.adobe-panel {
  background: var(--adobe-bg-primary);
  color: var(--adobe-text-primary);
  border: 1px solid var(--adobe-border);
}

.adobe-header {
  background: var(--adobe-bg-secondary);
  border-bottom: 1px solid var(--adobe-border);
  padding: 8px 12px;
}

.adobe-content {
  padding: 12px;
}

/* Scrollbar styles for Adobe CEP */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--adobe-bg-primary);
}

::-webkit-scrollbar-thumb {
  background: var(--adobe-border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--adobe-text-secondary);
}

::-webkit-scrollbar-corner {
  background: var(--adobe-bg-primary);
}

/* Selection styles */
::selection {
  background: rgba(70, 160, 245, 0.3);
  color: var(--adobe-text-primary);
}

::-moz-selection {
  background: rgba(70, 160, 245, 0.3);
  color: var(--adobe-text-primary);
}

/* Focus styles for accessibility */
:focus-visible {
  outline: 2px solid var(--adobe-accent);
  outline-offset: 1px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --adobe-border: #000000;
    --adobe-accent: #0000ff;
  }

  body:not([data-theme]) {
    --adobe-text-primary: #000000;
    --adobe-text-secondary: #333333;
    --adobe-bg-primary: #ffffff;
    --adobe-bg-secondary: #f5f5f5;
    --adobe-border: #000000;
  }

  body[data-theme="dark"] {
    --adobe-text-primary: #ffffff;
    --adobe-text-secondary: #cccccc;
    --adobe-bg-primary: #000000;
    --adobe-bg-secondary: #1a1a1a;
    --adobe-border: #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print styles */
@media print {
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
}
