/**
 * Main Entry Point for SahAI CEP Extension V2
 * Initializes the React application and CEP integration
 */

import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import { useSettingsStore } from './stores/settingsStore';
import './styles/globals.css';

// Initialize CEP integration
function initializeCEP() {
  // Wait for CEP to be ready
  if (typeof window !== 'undefined' && (window as any).CSInterface) {
    console.log('🚀 SahAI CEP Extension V2 - CEP Interface Ready');

    // Set up CEP event listeners
    const csInterface = new (window as any).CSInterface();

    // Listen for host application changes
    csInterface.addEventListener(
      'com.adobe.csxs.events.ApplicationActivate',
      (event: any) => {
        console.log('📱 Application activated:', event.data);
      }
    );

  } else {
    console.log('⚠️ CEP Interface not available - running in development mode');
  }
}

// Initialize the application
function initializeApp() {
  const root = ReactDOM.createRoot(
    document.getElementById('root') as HTMLElement
  );
  
  root.render(
    <React.StrictMode>
      <App />
    </React.StrictMode>
  );
}

// Start the application
document.addEventListener('DOMContentLoaded', () => {
  console.log('🔄 SahAI CEP Extension V2 - Initializing...');
  
  // Initialize CEP integration
  initializeCEP();
  
  // Initialize React app
  initializeApp();
  
  console.log('✅ SahAI CEP Extension V2 - Ready');
});
