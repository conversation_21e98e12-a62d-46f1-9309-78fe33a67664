/**
 * Cache Types for SahAI V2 Intelligent Caching System
 * Comprehensive type definitions for caching layer
 */

// Core cache interfaces
export interface CacheEntry<T = any> {
  key: string;
  value: T;
  ttl: number;
  createdAt: Date;
  lastAccessed: Date;
  accessCount: number;
  size: number;
}

export interface CacheStorage {
  get<T>(key: string): Promise<CacheEntry<T> | null>;
  set<T>(key: string, value: T, ttl: number): Promise<void>;
  delete(key: string): Promise<boolean>;
  clear(): Promise<void>;
  keys(): Promise<string[]>;
  size(): Promise<number>;
  has(key: string): Promise<boolean>;
}

// Cache strategies
export interface CacheStrategy {
  name: string;
  ttl: number; // Time to live in milliseconds
  refreshThreshold: number; // Percentage (0-1) of TTL when to refresh
  evictionPolicy: 'lru' | 'lfu' | 'ttl' | 'fifo';
  maxSize: number; // Maximum number of entries
  maxMemory: number; // Maximum memory usage in bytes
  backgroundRefresh: boolean;
  compression: boolean;
}

// Predefined cache strategies
export const CACHE_STRATEGIES = {
  models: {
    name: 'models',
    ttl: 15 * 60 * 1000, // 15 minutes
    refreshThreshold: 0.8, // Refresh at 80% of TTL
    evictionPolicy: 'lru' as const,
    maxSize: 1000,
    maxMemory: 10 * 1024 * 1024, // 10MB
    backgroundRefresh: true,
    compression: false,
  },
  health: {
    name: 'health',
    ttl: 30 * 1000, // 30 seconds
    refreshThreshold: 0.5, // Refresh at 50% of TTL
    evictionPolicy: 'ttl' as const,
    maxSize: 100,
    maxMemory: 1 * 1024 * 1024, // 1MB
    backgroundRefresh: true,
    compression: false,
  },
  favorites: {
    name: 'favorites',
    ttl: Infinity, // Never expire
    refreshThreshold: 0,
    evictionPolicy: 'lfu' as const,
    maxSize: 500,
    maxMemory: 2 * 1024 * 1024, // 2MB
    backgroundRefresh: false,
    compression: true,
  },
  apiResponses: {
    name: 'apiResponses',
    ttl: 5 * 60 * 1000, // 5 minutes
    refreshThreshold: 0.7,
    evictionPolicy: 'lru' as const,
    maxSize: 200,
    maxMemory: 5 * 1024 * 1024, // 5MB
    backgroundRefresh: false,
    compression: true,
  },
} as const;

// Cache key patterns
export interface CacheKeyPattern {
  pattern: string;
  description: string;
  strategy: keyof typeof CACHE_STRATEGIES;
}

export const CACHE_KEY_PATTERNS: Record<string, CacheKeyPattern> = {
  MODELS: {
    pattern: 'models:{providerId}:{apiKeyHash}',
    description: 'Model lists for specific provider and API key',
    strategy: 'models',
  },
  HEALTH: {
    pattern: 'health:{providerId}',
    description: 'Health status for provider',
    strategy: 'health',
  },
  FAVORITES: {
    pattern: 'favorites:{userId}',
    description: 'User favorite models',
    strategy: 'favorites',
  },
  USAGE: {
    pattern: 'usage:{userId}:{providerId}',
    description: 'User usage statistics per provider',
    strategy: 'favorites',
  },
  API_RESPONSE: {
    pattern: 'api:{providerId}:{endpoint}:{hash}',
    description: 'Cached API responses',
    strategy: 'apiResponses',
  },
};

// Cache events
export interface CacheEvent {
  type: 'hit' | 'miss' | 'set' | 'delete' | 'evict' | 'refresh' | 'error';
  key: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface CacheEventListener {
  (event: CacheEvent): void;
}

// Cache statistics
export interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  evictions: number;
  refreshes: number;
  errors: number;
  hitRate: number;
  totalSize: number;
  totalMemory: number;
  averageAccessTime: number;
  oldestEntry?: Date;
  newestEntry?: Date;
}

// Cache service interface
export interface CacheService {
  // Core operations
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, ttl?: number): Promise<void>;
  delete(key: string): Promise<boolean>;
  clear(): Promise<void>;
  
  // Pattern operations
  invalidate(pattern: string): Promise<number>;
  refresh(key: string): Promise<void>;
  
  // Batch operations
  mget<T>(keys: string[]): Promise<(T | null)[]>;
  mset<T>(entries: Array<{ key: string; value: T; ttl?: number }>): Promise<void>;
  
  // Management
  getStats(): Promise<CacheStats>;
  getStrategy(key: string): CacheStrategy;
  setStrategy(pattern: string, strategy: Partial<CacheStrategy>): void;
  
  // Events
  on(event: CacheEvent['type'], listener: CacheEventListener): void;
  off(event: CacheEvent['type'], listener: CacheEventListener): void;
  
  // Background operations
  startBackgroundRefresh(): void;
  stopBackgroundRefresh(): void;
  
  // Utilities
  generateKey(pattern: string, params: Record<string, string>): string;
  hashApiKey(apiKey: string): string;
}

// Storage adapters
export interface MemoryStorageConfig {
  maxSize: number;
  maxMemory: number;
  gcInterval: number; // Garbage collection interval
}

export interface LocalStorageConfig {
  prefix: string;
  compression: boolean;
  encryption: boolean;
}

export interface IndexedDBConfig {
  dbName: string;
  version: number;
  storeName: string;
  keyPath: string;
}

// Cache invalidation
export interface InvalidationRule {
  pattern: string;
  triggers: string[]; // Events that trigger invalidation
  cascade: boolean; // Whether to cascade to related keys
}

// Background refresh
export interface RefreshJob {
  key: string;
  priority: number;
  scheduledAt: Date;
  attempts: number;
  maxAttempts: number;
  refreshFunction: () => Promise<any>;
}

export interface RefreshQueue {
  add(job: RefreshJob): void;
  remove(key: string): boolean;
  process(): Promise<void>;
  size(): number;
  clear(): void;
}

// Compression
export interface CompressionAdapter {
  compress(data: any): Promise<string>;
  decompress(compressed: string): Promise<any>;
  getCompressionRatio(original: any, compressed: string): number;
}

// Cache warming
export interface WarmupConfig {
  keys: string[];
  priority: number;
  parallel: boolean;
  timeout: number;
}

export interface CacheWarmer {
  warmup(config: WarmupConfig): Promise<void>;
  isWarming(): boolean;
  getProgress(): number;
}
