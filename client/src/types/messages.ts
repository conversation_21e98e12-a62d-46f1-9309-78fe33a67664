// Message types and interfaces for the chat system

export interface CodeBlockData {
  language: string;
  code: string;
  filename?: string;
  collapsed?: boolean;
  showLineNumbers?: boolean;
}

export interface ToolUse {
  name: string;
  input: any;
  id?: string;
}

export interface ToolResult {
  toolUseId: string;
  content: string;
  isError?: boolean;
}

export type MessageContentType = 
  | 'text' 
  | 'code' 
  | 'markdown' 
  | 'tool_use' 
  | 'tool_result'
  | 'image'
  | 'file';

export interface MessageContent {
  type: MessageContentType;
  text?: string;
  code?: CodeBlockData;
  markdown?: string;
  toolUse?: ToolUse;
  toolResult?: ToolResult;
  imageUrl?: string;
  fileName?: string;
  fileContent?: string;
}

export interface MessageMetadata {
  provider?: string;
  model?: string;
  tokens?: number;
  cost?: number;
  tokensIn?: number;
  tokensOut?: number;
  cacheReads?: number;
  cacheWrites?: number;
  reasoning?: string;
}

export interface MessageTimestamp {
  created: number;
  updated?: number;
}

export interface MessageState {
  isEditing?: boolean;
  isLoading?: boolean;
  hasError?: boolean;
  errorMessage?: string;
  isExpanded?: boolean;
}

// Extended message interface for rich content
export interface RichChatMessage {
  id: string;
  content: string | MessageContent[];
  role: 'user' | 'assistant' | 'system';
  timestamp: MessageTimestamp;
  metadata?: MessageMetadata;
  state?: MessageState;
  files?: string[];
  images?: string[];
}

// Message interaction types
export interface MessageInteraction {
  type: 'copy' | 'edit' | 'delete' | 'quote' | 'save' | 'run';
  messageId: string;
  content?: string;
  selection?: string;
}

// Code block toolbar actions
export interface CodeBlockAction {
  type: 'copy' | 'save' | 'collapse' | 'run' | 'expand';
  language: string;
  code: string;
  filename?: string;
}

// Message rendering props
export interface MessageRendererProps {
  message: RichChatMessage;
  isLast?: boolean;
  isExpanded?: boolean;
  onToggleExpand?: (messageId: string) => void;
  onEdit?: (messageId: string, newContent: string) => void;
  onDelete?: (messageId: string) => void;
  onRetry?: (messageId: string) => void;
  onCopy?: (content: string) => void;
  onQuote?: (content: string) => void;
  onCodeAction?: (action: CodeBlockAction) => void;
}

// Message factory types
export type MessageFactory = {
  createTextMessage: (content: string, role: 'user' | 'assistant') => RichChatMessage;
  createCodeMessage: (code: CodeBlockData, role: 'assistant') => RichChatMessage;
  createMarkdownMessage: (markdown: string, role: 'assistant') => RichChatMessage;
  createToolMessage: (toolUse: ToolUse, role: 'assistant') => RichChatMessage;
  createMixedMessage: (contents: MessageContent[], role: 'assistant') => RichChatMessage;
};

// Message parsing types
export interface MessageParser {
  parseMarkdown: (content: string) => MessageContent[];
  extractCodeBlocks: (content: string) => CodeBlockData[];
  parseToolUse: (content: string) => ToolUse | null;
  parseToolResult: (content: string) => ToolResult | null;
}

// Export utility types
export type MessageRole = 'user' | 'assistant' | 'system';
export type MessageId = string;
export type MessageTimestampValue = number;
