/**
 * Vite Plugin for CSP Hash Generation
 * Automatically generates CSP hashes for inline scripts during build
 */

const crypto = require('crypto');

/**
 * Generate SHA-256 hash for script content
 */
function generateScriptHash(scriptContent) {
  const hash = crypto.createHash('sha256');
  hash.update(scriptContent, 'utf8');
  return 'sha256-' + hash.digest('base64');
}

/**
 * Extract inline scripts from HTML
 */
function extractInlineScripts(html) {
  const scripts = [];
  const scriptRegex = /<script(?![^>]*src=)([^>]*)>([\s\S]*?)<\/script>/gi;
  let match;

  while ((match = scriptRegex.exec(html)) !== null) {
    const attributes = match[1];
    const content = match[2].trim();
    
    // Skip empty scripts or scripts with src attribute
    if (content && !attributes.includes('src=')) {
      scripts.push({
        fullMatch: match[0],
        attributes: attributes,
        content: content,
        hash: generateScriptHash(content)
      });
    }
  }

  return scripts;
}

/**
 * Update CSP with script hashes
 */
function updateCSPWithHashes(html, scriptHashes) {
  // Find the existing CSP meta tag
  const cspRegex = /<meta\s+http-equiv=["']Content-Security-Policy["']\s+content=["']([\s\S]*?)["']\s*>/i;
  const match = cspRegex.exec(html);
  
  if (!match) {
    console.warn('⚠️  Content-Security-Policy meta tag not found');
    return html;
  }

  const originalCSP = match[1];
  
  // Parse CSP directives
  const directives = originalCSP
    .split(';')
    .map(directive => directive.trim())
    .filter(directive => directive.length > 0);

  // Find and update script-src directive
  let scriptSrcIndex = -1;
  
  for (let i = 0; i < directives.length; i++) {
    if (directives[i].startsWith('script-src')) {
      scriptSrcIndex = i;
      break;
    }
  }

  if (scriptSrcIndex === -1) {
    console.warn('⚠️  script-src directive not found in CSP');
    return html;
  }

  // Remove 'unsafe-inline' and add hashes
  let newScriptSrc = directives[scriptSrcIndex]
    .replace(/'unsafe-inline'/g, '')
    .replace(/\s+/g, ' ')
    .trim();

  // Add script hashes
  if (scriptHashes.length > 0) {
    const hashStrings = scriptHashes.map(hash => `'${hash}'`);
    newScriptSrc += ' ' + hashStrings.join(' ');
  }

  // Clean up extra spaces
  newScriptSrc = newScriptSrc.replace(/\s+/g, ' ').trim();

  // Update the directive
  directives[scriptSrcIndex] = newScriptSrc;

  // Reconstruct CSP
  const newCSP = directives.join(';\n        ') + ';';

  // Replace in HTML
  const newCspTag = match[0].replace(originalCSP, newCSP);
  return html.replace(match[0], newCspTag);
}

/**
 * Vite Plugin for CSP Hash Generation
 */
function cspHashPlugin(options = {}) {
  const {
    enabled = true,
    logLevel = 'info'
  } = options;

  return {
    name: 'csp-hash',
    apply: 'build', // Only run during build
    
    transformIndexHtml: {
      order: 'post', // Run after other transformations
      handler(html, context) {
        if (!enabled) {
          return html;
        }

        try {
          // Extract inline scripts
          const inlineScripts = extractInlineScripts(html);
          
          if (inlineScripts.length === 0) {
            if (logLevel === 'info') {
              console.log('ℹ️  No inline scripts found for CSP hash generation');
            }
            return html;
          }

          // Generate hashes
          const scriptHashes = inlineScripts.map(script => script.hash);
          
          if (logLevel === 'info') {
            console.log(`🔐 Generated CSP hashes for ${inlineScripts.length} inline script(s):`);
            scriptHashes.forEach((hash, index) => {
              console.log(`   ${index + 1}. ${hash}`);
            });
          }

          // Update CSP
          const updatedHtml = updateCSPWithHashes(html, scriptHashes);
          
          if (logLevel === 'info') {
            console.log('✅ CSP updated with script hashes, removed \'unsafe-inline\'');
          }

          return updatedHtml;

        } catch (error) {
          console.error('❌ CSP hash generation failed:', error.message);
          
          // Return original HTML on error to prevent build failure
          return html;
        }
      }
    }
  };
}

module.exports = cspHashPlugin;
