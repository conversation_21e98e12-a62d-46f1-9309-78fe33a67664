<?xml version="1.0" encoding="UTF-8"?>
<ExtensionManifest Version="11.0" ExtensionBundleId="com.sahai.cep" ExtensionBundleVersion="1.1.0" ExtensionBundleName="SahAI CEP Chat Bot" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Author>SahAI</Author>
  <Contact mailto="<EMAIL>"/>
  <Legal href="https://sahai.com/legal"/>
  <Abstract href="https://sahai.com/sahai-cep">
    <![CDATA[AI Chat Bot Extension for Adobe Creative Suite with multi-provider support and native theme integration.]]>
  </Abstract>

  <ExtensionList>
    <Extension Id="com.sahai.cep.panel" Version="1.1.0"/>
  </ExtensionList>

  <ExecutionEnvironment>
    <HostList>
      <!-- After Effects -->
      <Host Name="AEFT" Version="[18.0,99.9]"/>
      <!-- Premiere Pro -->
      <Host Name="PPRO" Version="[14.0,99.9]"/>
      <!-- Photoshop -->
      <Host Name="PHXS" Version="[22.0,99.9]"/>
      <!-- Illustrator -->
      <Host Name="ILST" Version="[24.0,99.9]"/>
      <!-- Audition -->
      <Host Name="AUDT" Version="[13.0,99.9]"/>
    </HostList>

    <LocaleList>
      <Locale Code="All"/>
    </LocaleList>

    <RequiredRuntimeList>
      <RequiredRuntime Name="CSXS" Version="11.0"/>
    </RequiredRuntimeList>

    <!-- CEF parameters moved to individual extension resources -->
  </ExecutionEnvironment>

  <DispatchInfoList>
    <Extension Id="com.sahai.cep.panel">
      <DispatchInfo>
        <Resources>
          <MainPath>./index.html</MainPath>
          <ScriptPath>./extendscript/aftereffects/ae-integration.jsx</ScriptPath>
          <CEFCommandLine>
            <Parameter>--enable-nodejs</Parameter>
            <Parameter>--mixed-context</Parameter>
            <Parameter>--allow-file-access-from-files</Parameter>
            <Parameter>--allow-file-access</Parameter>
            <Parameter>--high-dpi-support=1</Parameter>
            <Parameter>--force-device-scale-factor=1</Parameter>
          </CEFCommandLine>
        </Resources>

        <Lifecycle>
          <AutoVisible>true</AutoVisible>
        </Lifecycle>

        <UI>
          <Type>Panel</Type>
          <Menu>SahAI Chat Bot</Menu>
          <Geometry>
            <Size>
              <Height>600</Height>
              <Width>350</Width>
            </Size>
            <MinSize>
              <Height>400</Height>
              <Width>300</Width>
            </MinSize>
            <MaxSize>
              <Height>1200</Height>
              <Width>800</Width>
            </MaxSize>
          </Geometry>

          <Icons>
            <Icon Type="Normal">./icons/icon-16.png</Icon>
            <Icon Type="RollOver">./icons/icon-16.png</Icon>
            <Icon Type="DarkNormal">./icons/icon-16.png</Icon>
            <Icon Type="DarkRollOver">./icons/icon-16.png</Icon>
            <Icon Type="Normal" Size="32">./icons/icon-32.png</Icon>
            <Icon Type="RollOver" Size="32">./icons/icon-32.png</Icon>
            <Icon Type="DarkNormal" Size="32">./icons/icon-32.png</Icon>
            <Icon Type="DarkRollOver" Size="32">./icons/icon-32.png</Icon>
          </Icons>
        </UI>

        <!-- ExtensionData removed - ScriptPath now in Resources -->
      </DispatchInfo>
    </Extension>
  </DispatchInfoList>
</ExtensionManifest>