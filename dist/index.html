<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SahAI CEP Extension V2</title>

    <!-- CEP Compatibility Meta Tags -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="format-detection" content="telephone=no">

    <!-- CEP Extension Styles -->
    <style>
        /* Prevent FOUC and ensure proper CEP integration */
        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background: var(--adobe-bg-primary, #1e1e1e);
            color: var(--adobe-text-primary, #e0e0e0);
            font-family: var(--adobe-font-family, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
        }

        #root {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        /* Loading state */
        .loading-container {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            flex-direction: column;
            gap: 16px;
        }

        .loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid var(--adobe-border-color, #3c3c3c);
            border-top: 3px solid var(--adobe-accent-color, #007acc);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            color: var(--adobe-text-secondary, #999999);
            font-size: 14px;
        }
    </style>
  <script src="./CSInterface.js"></script>
    <script crossorigin src="./assets/index-DZkLljcA.js"></script>
</head>
<body>
    <!-- CEP Extension Root Container -->
    <div id="root">
        <!-- Loading state shown until React app loads -->
        <div class="loading-container">
            <div class="loading-spinner"></div>
            <div class="loading-text">Loading SahAI...</div>
        </div>
    </div>

    <!-- React Application Entry Point -->
</body>
</html>